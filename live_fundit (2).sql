-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.9.11
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 29, 2025 at 12:32 PM
-- Server version: 10.11.6-MariaDB-0+deb12u1
-- PHP Version: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `live_fundit`
--

-- --------------------------------------------------------

--
-- Table structure for table `access`
--

CREATE TABLE `access` (
  `id` int(11) NOT NULL,
  `roleid` int(11) NOT NULL,
  `controllerid` int(11) NOT NULL,
  `Date` datetime NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL,
  `added_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `access`
--

INSERT INTO `access` (`id`, `roleid`, `controllerid`, `Date`, `system_date`, `added_by`) VALUES
(235, 2, 40, '2021-05-22 16:51:58', NULL, NULL),
(236, 2, 41, '2021-05-22 16:51:58', NULL, NULL),
(237, 2, 10, '2021-05-22 16:51:58', NULL, NULL),
(238, 2, 46, '2021-05-22 16:51:58', NULL, NULL),
(285, 12, 40, '2021-05-29 14:23:09', '2021-05-26', 7),
(286, 12, 41, '2021-05-29 14:23:09', '2021-05-26', 7),
(287, 12, 15, '2021-05-29 14:23:09', '2021-05-26', 7),
(288, 12, 16, '2021-05-29 14:23:09', '2021-05-26', 7),
(289, 11, 40, '2021-05-29 14:24:37', '2021-05-26', 7),
(290, 11, 41, '2021-05-29 14:24:37', '2021-05-26', 7),
(291, 11, 1, '2021-05-29 14:24:37', '2021-05-26', 7),
(292, 11, 2, '2021-05-29 14:24:37', '2021-05-26', 7),
(293, 11, 3, '2021-05-29 14:24:37', '2021-05-26', 7),
(294, 11, 4, '2021-05-29 14:24:37', '2021-05-26', 7),
(295, 11, 5, '2021-05-29 14:24:37', '2021-05-26', 7),
(296, 11, 6, '2021-05-29 14:24:37', '2021-05-26', 7),
(362, 6, 40, '2021-05-29 14:34:12', '2021-05-26', 7),
(363, 6, 41, '2021-05-29 14:34:12', '2021-05-26', 7),
(364, 6, 30, '2021-05-29 14:34:12', '2021-05-26', 7),
(365, 6, 31, '2021-05-29 14:34:12', '2021-05-26', 7),
(366, 6, 32, '2021-05-29 14:34:12', '2021-05-26', 7),
(367, 6, 33, '2021-05-29 14:34:12', '2021-05-26', 7),
(368, 6, 34, '2021-05-29 14:34:12', '2021-05-26', 7),
(369, 6, 35, '2021-05-29 14:34:12', '2021-05-26', 7),
(370, 6, 37, '2021-05-29 14:34:12', '2021-05-26', 7),
(371, 6, 38, '2021-05-29 14:34:12', '2021-05-26', 7),
(372, 6, 39, '2021-05-29 14:34:12', '2021-05-26', 7),
(373, 6, 42, '2021-05-29 14:34:12', '2021-05-26', 7),
(374, 6, 43, '2021-05-29 14:34:12', '2021-05-26', 7),
(375, 6, 45, '2021-05-29 14:34:12', '2021-05-26', 7),
(376, 6, 47, '2021-05-29 14:34:12', '2021-05-26', 7),
(377, 6, 28, '2021-05-29 14:34:12', '2021-05-26', 7),
(378, 6, 29, '2021-05-29 14:34:12', '2021-05-26', 7),
(379, 6, 27, '2021-05-29 14:34:12', '2021-05-26', 7),
(380, 6, 17, '2021-05-29 14:34:12', '2021-05-26', 7),
(381, 6, 18, '2021-05-29 14:34:12', '2021-05-26', 7),
(382, 6, 19, '2021-05-29 14:34:12', '2021-05-26', 7),
(383, 6, 20, '2021-05-29 14:34:12', '2021-05-26', 7),
(384, 6, 21, '2021-05-29 14:34:12', '2021-05-26', 7),
(385, 6, 22, '2021-05-29 14:34:12', '2021-05-26', 7),
(386, 6, 23, '2021-05-29 14:34:12', '2021-05-26', 7),
(387, 6, 24, '2021-05-29 14:34:12', '2021-05-26', 7),
(388, 6, 25, '2021-05-29 14:34:12', '2021-05-26', 7),
(389, 6, 26, '2021-05-29 14:34:12', '2021-05-26', 7),
(390, 6, 11, '2021-05-29 14:34:12', '2021-05-26', 7),
(391, 6, 12, '2021-05-29 14:34:12', '2021-05-26', 7),
(392, 6, 13, '2021-05-29 14:34:12', '2021-05-26', 7),
(393, 6, 14, '2021-05-29 14:34:12', '2021-05-26', 7),
(394, 6, 15, '2021-05-29 14:34:12', '2021-05-26', 7),
(395, 6, 16, '2021-05-29 14:34:12', '2021-05-26', 7),
(396, 6, 7, '2021-05-29 14:34:12', '2021-05-26', 7),
(397, 6, 8, '2021-05-29 14:34:12', '2021-05-26', 7),
(398, 6, 1, '2021-05-29 14:34:12', '2021-05-26', 7),
(399, 6, 3, '2021-05-29 14:34:12', '2021-05-26', 7),
(400, 6, 4, '2021-05-29 14:34:12', '2021-05-26', 7),
(401, 6, 5, '2021-05-29 14:34:12', '2021-05-26', 7),
(402, 6, 6, '2021-05-29 14:34:12', '2021-05-26', 7),
(403, 5, 40, '2021-05-29 14:37:22', '2021-05-26', 7),
(404, 5, 41, '2021-05-29 14:37:22', '2021-05-26', 7),
(405, 5, 30, '2021-05-29 14:37:22', '2021-05-26', 7),
(406, 5, 31, '2021-05-29 14:37:22', '2021-05-26', 7),
(407, 5, 32, '2021-05-29 14:37:22', '2021-05-26', 7),
(408, 5, 33, '2021-05-29 14:37:22', '2021-05-26', 7),
(409, 5, 34, '2021-05-29 14:37:22', '2021-05-26', 7),
(410, 5, 35, '2021-05-29 14:37:22', '2021-05-26', 7),
(411, 5, 37, '2021-05-29 14:37:22', '2021-05-26', 7),
(412, 5, 38, '2021-05-29 14:37:22', '2021-05-26', 7),
(413, 5, 39, '2021-05-29 14:37:22', '2021-05-26', 7),
(414, 5, 42, '2021-05-29 14:37:22', '2021-05-26', 7),
(415, 5, 43, '2021-05-29 14:37:22', '2021-05-26', 7),
(416, 5, 45, '2021-05-29 14:37:22', '2021-05-26', 7),
(417, 5, 47, '2021-05-29 14:37:22', '2021-05-26', 7),
(418, 5, 28, '2021-05-29 14:37:22', '2021-05-26', 7),
(419, 5, 29, '2021-05-29 14:37:22', '2021-05-26', 7),
(420, 5, 27, '2021-05-29 14:37:22', '2021-05-26', 7),
(421, 5, 17, '2021-05-29 14:37:22', '2021-05-26', 7),
(422, 5, 18, '2021-05-29 14:37:22', '2021-05-26', 7),
(423, 5, 19, '2021-05-29 14:37:22', '2021-05-26', 7),
(424, 5, 20, '2021-05-29 14:37:22', '2021-05-26', 7),
(425, 5, 21, '2021-05-29 14:37:22', '2021-05-26', 7),
(426, 5, 22, '2021-05-29 14:37:22', '2021-05-26', 7),
(427, 5, 23, '2021-05-29 14:37:22', '2021-05-26', 7),
(428, 5, 24, '2021-05-29 14:37:22', '2021-05-26', 7),
(429, 5, 25, '2021-05-29 14:37:22', '2021-05-26', 7),
(430, 5, 26, '2021-05-29 14:37:22', '2021-05-26', 7),
(431, 5, 9, '2021-05-29 14:37:22', '2021-05-26', 7),
(432, 5, 10, '2021-05-29 14:37:22', '2021-05-26', 7),
(433, 5, 11, '2021-05-29 14:37:22', '2021-05-26', 7),
(434, 5, 12, '2021-05-29 14:37:22', '2021-05-26', 7),
(435, 5, 13, '2021-05-29 14:37:22', '2021-05-26', 7),
(436, 5, 14, '2021-05-29 14:37:22', '2021-05-26', 7),
(437, 5, 15, '2021-05-29 14:37:22', '2021-05-26', 7),
(438, 5, 16, '2021-05-29 14:37:22', '2021-05-26', 7),
(439, 5, 7, '2021-05-29 14:37:22', '2021-05-26', 7),
(440, 5, 8, '2021-05-29 14:37:22', '2021-05-26', 7),
(441, 5, 46, '2021-05-29 14:37:22', '2021-05-26', 7),
(442, 5, 1, '2021-05-29 14:37:22', '2021-05-26', 7),
(443, 5, 2, '2021-05-29 14:37:22', '2021-05-26', 7),
(444, 5, 3, '2021-05-29 14:37:22', '2021-05-26', 7),
(445, 5, 4, '2021-05-29 14:37:22', '2021-05-26', 7),
(446, 5, 5, '2021-05-29 14:37:22', '2021-05-26', 7),
(447, 5, 6, '2021-05-29 14:37:22', '2021-05-26', 7),
(455, 3, 40, '2021-05-29 14:40:18', '2021-05-26', 7),
(456, 3, 41, '2021-05-29 14:40:18', '2021-05-26', 7),
(457, 3, 33, '2021-05-29 14:40:18', '2021-05-26', 7),
(458, 3, 43, '2021-05-29 14:40:18', '2021-05-26', 7),
(459, 3, 27, '2021-05-29 14:40:18', '2021-05-26', 7),
(460, 3, 18, '2021-05-29 14:40:18', '2021-05-26', 7),
(461, 3, 20, '2021-05-29 14:40:18', '2021-05-26', 7),
(462, 3, 22, '2021-05-29 14:40:18', '2021-05-26', 7),
(463, 3, 24, '2021-05-29 14:40:18', '2021-05-26', 7),
(464, 3, 26, '2021-05-29 14:40:18', '2021-05-26', 7),
(465, 3, 11, '2021-05-29 14:40:18', '2021-05-26', 7),
(466, 3, 13, '2021-05-29 14:40:18', '2021-05-26', 7),
(467, 3, 8, '2021-05-29 14:40:18', '2021-05-26', 7),
(468, 7, 40, '2021-05-29 14:42:43', '2021-05-26', 7),
(469, 7, 41, '2021-05-29 14:42:43', '2021-05-26', 7),
(470, 7, 30, '2021-05-29 14:42:43', '2021-05-26', 7),
(471, 7, 31, '2021-05-29 14:42:43', '2021-05-26', 7),
(472, 7, 32, '2021-05-29 14:42:43', '2021-05-26', 7),
(473, 7, 33, '2021-05-29 14:42:43', '2021-05-26', 7),
(474, 7, 34, '2021-05-29 14:42:43', '2021-05-26', 7),
(475, 7, 35, '2021-05-29 14:42:43', '2021-05-26', 7),
(476, 7, 37, '2021-05-29 14:42:43', '2021-05-26', 7),
(477, 7, 38, '2021-05-29 14:42:43', '2021-05-26', 7),
(478, 7, 39, '2021-05-29 14:42:43', '2021-05-26', 7),
(479, 7, 42, '2021-05-29 14:42:43', '2021-05-26', 7),
(480, 7, 43, '2021-05-29 14:42:43', '2021-05-26', 7),
(481, 7, 45, '2021-05-29 14:42:43', '2021-05-26', 7),
(482, 7, 47, '2021-05-29 14:42:43', '2021-05-26', 7),
(483, 7, 28, '2021-05-29 14:42:43', '2021-05-26', 7),
(484, 7, 29, '2021-05-29 14:42:43', '2021-05-26', 7),
(735, 10, 44, '2021-08-31 10:22:11', '2021-08-27', 7),
(736, 10, 49, '2021-08-31 10:22:11', '2021-08-27', 7),
(737, 10, 40, '2021-08-31 10:22:11', '2021-08-27', 7),
(738, 10, 41, '2021-08-31 10:22:11', '2021-08-27', 7),
(739, 10, 19, '2021-08-31 10:22:11', '2021-08-27', 7),
(740, 10, 56, '2021-08-31 10:22:11', '2021-08-27', 7),
(741, 10, 9, '2021-08-31 10:22:11', '2021-08-27', 7),
(742, 10, 10, '2021-08-31 10:22:11', '2021-08-27', 7),
(743, 10, 12, '2021-08-31 10:22:11', '2021-08-27', 7),
(744, 10, 14, '2021-08-31 10:22:11', '2021-08-27', 7),
(745, 10, 46, '2021-08-31 10:22:11', '2021-08-27', 7),
(808, 14, 44, '2021-10-18 09:45:24', '2021-09-04', 7),
(1763, 15, 61, '2022-04-01 15:02:10', '2021-09-04', 7),
(1764, 15, 66, '2022-04-01 15:02:10', '2021-09-04', 7),
(1765, 15, 84, '2022-04-01 15:02:10', '2021-09-04', 7),
(3076, 17, 106, '2022-05-26 07:09:08', '2021-09-04', 34),
(3077, 17, 64, '2022-05-26 07:09:08', '2021-09-04', 34),
(3078, 17, 66, '2022-05-26 07:09:08', '2021-09-04', 34),
(3079, 17, 91, '2022-05-26 07:09:08', '2021-09-04', 34),
(3080, 17, 84, '2022-05-26 07:09:08', '2021-09-04', 34),
(3081, 17, 85, '2022-05-26 07:09:08', '2021-09-04', 34),
(3082, 17, 88, '2022-05-26 07:09:08', '2021-09-04', 34),
(3083, 17, 58, '2022-05-26 07:09:08', '2021-09-04', 34),
(3084, 17, 60, '2022-05-26 07:09:08', '2021-09-04', 34),
(3085, 17, 75, '2022-05-26 07:09:08', '2021-09-04', 34),
(3086, 17, 83, '2022-05-26 07:09:08', '2021-09-04', 34),
(3087, 17, 50, '2022-05-26 07:09:08', '2021-09-04', 34),
(3088, 17, 62, '2022-05-26 07:09:08', '2021-09-04', 34),
(3089, 17, 52, '2022-05-26 07:09:08', '2021-09-04', 34),
(3090, 17, 65, '2022-05-26 07:09:08', '2021-09-04', 34),
(3091, 17, 67, '2022-05-26 07:09:08', '2021-09-04', 34),
(3092, 17, 68, '2022-05-26 07:09:08', '2021-09-04', 34),
(3093, 17, 79, '2022-05-26 07:09:08', '2021-09-04', 34),
(3094, 17, 40, '2022-05-26 07:09:08', '2021-09-04', 34),
(3095, 17, 69, '2022-05-26 07:09:08', '2021-09-04', 34),
(3096, 17, 71, '2022-05-26 07:09:08', '2021-09-04', 34),
(3097, 17, 90, '2022-05-26 07:09:08', '2021-09-04', 34),
(3098, 17, 114, '2022-05-26 07:09:08', '2021-09-04', 34),
(3099, 17, 119, '2022-05-26 07:09:08', '2021-09-04', 34),
(3569, 21, 109, '2022-05-29 05:18:06', '2021-09-04', 7),
(3570, 21, 110, '2022-05-29 05:18:06', '2021-09-04', 7),
(3571, 21, 111, '2022-05-29 05:18:06', '2021-09-04', 7),
(3572, 21, 112, '2022-05-29 05:18:06', '2021-09-04', 7),
(3573, 21, 106, '2022-05-29 05:18:06', '2021-09-04', 7),
(3574, 21, 107, '2022-05-29 05:18:06', '2021-09-04', 7),
(3575, 21, 61, '2022-05-29 05:18:06', '2021-09-04', 7),
(3576, 21, 64, '2022-05-29 05:18:06', '2021-09-04', 7),
(3577, 21, 66, '2022-05-29 05:18:06', '2021-09-04', 7),
(3578, 21, 91, '2022-05-29 05:18:06', '2021-09-04', 7),
(3579, 21, 84, '2022-05-29 05:18:06', '2021-09-04', 7),
(3580, 21, 86, '2022-05-29 05:18:06', '2021-09-04', 7),
(3581, 21, 87, '2022-05-29 05:18:06', '2021-09-04', 7),
(3582, 21, 88, '2022-05-29 05:18:06', '2021-09-04', 7),
(3583, 21, 89, '2022-05-29 05:18:06', '2021-09-04', 7),
(3584, 21, 105, '2022-05-29 05:18:06', '2021-09-04', 7),
(3585, 21, 99, '2022-05-29 05:18:06', '2021-09-04', 7),
(3586, 21, 100, '2022-05-29 05:18:06', '2021-09-04', 7),
(3587, 21, 101, '2022-05-29 05:18:06', '2021-09-04', 7),
(3588, 21, 102, '2022-05-29 05:18:06', '2021-09-04', 7),
(3589, 21, 103, '2022-05-29 05:18:06', '2021-09-04', 7),
(3590, 21, 104, '2022-05-29 05:18:06', '2021-09-04', 7),
(3591, 21, 96, '2022-05-29 05:18:06', '2021-09-04', 7),
(3592, 21, 97, '2022-05-29 05:18:06', '2021-09-04', 7),
(3593, 21, 98, '2022-05-29 05:18:06', '2021-09-04', 7),
(3594, 21, 108, '2022-05-29 05:18:06', '2021-09-04', 7),
(3595, 21, 92, '2022-05-29 05:18:06', '2021-09-04', 7),
(3596, 21, 93, '2022-05-29 05:18:06', '2021-09-04', 7),
(3597, 21, 94, '2022-05-29 05:18:06', '2021-09-04', 7),
(3598, 21, 95, '2022-05-29 05:18:06', '2021-09-04', 7),
(3599, 21, 82, '2022-05-29 05:18:06', '2021-09-04', 7),
(3600, 21, 81, '2022-05-29 05:18:06', '2021-09-04', 7),
(3601, 21, 58, '2022-05-29 05:18:06', '2021-09-04', 7),
(3602, 21, 60, '2022-05-29 05:18:06', '2021-09-04', 7),
(3603, 21, 75, '2022-05-29 05:18:06', '2021-09-04', 7),
(3604, 21, 76, '2022-05-29 05:18:06', '2021-09-04', 7),
(3605, 21, 77, '2022-05-29 05:18:06', '2021-09-04', 7),
(3606, 21, 78, '2022-05-29 05:18:06', '2021-09-04', 7),
(3607, 21, 83, '2022-05-29 05:18:06', '2021-09-04', 7),
(3608, 21, 30, '2022-05-29 05:18:06', '2021-09-04', 7),
(3609, 21, 31, '2022-05-29 05:18:06', '2021-09-04', 7),
(3610, 21, 39, '2022-05-29 05:18:06', '2021-09-04', 7),
(3611, 21, 42, '2022-05-29 05:18:06', '2021-09-04', 7),
(3612, 21, 43, '2022-05-29 05:18:06', '2021-09-04', 7),
(3613, 21, 121, '2022-05-29 05:18:06', '2021-09-04', 7),
(3614, 21, 50, '2022-05-29 05:18:06', '2021-09-04', 7),
(3615, 21, 62, '2022-05-29 05:18:06', '2021-09-04', 7),
(3616, 21, 52, '2022-05-29 05:18:06', '2021-09-04', 7),
(3617, 21, 65, '2022-05-29 05:18:06', '2021-09-04', 7),
(3618, 21, 67, '2022-05-29 05:18:06', '2021-09-04', 7),
(3619, 21, 68, '2022-05-29 05:18:06', '2021-09-04', 7),
(3620, 21, 79, '2022-05-29 05:18:06', '2021-09-04', 7),
(3621, 21, 80, '2022-05-29 05:18:06', '2021-09-04', 7),
(3622, 21, 113, '2022-05-29 05:18:06', '2021-09-04', 7),
(3623, 21, 40, '2022-05-29 05:18:06', '2021-09-04', 7),
(3624, 21, 34, '2022-05-29 05:18:06', '2021-09-04', 7),
(3625, 21, 35, '2022-05-29 05:18:06', '2021-09-04', 7),
(3626, 21, 38, '2022-05-29 05:18:06', '2021-09-04', 7),
(3627, 21, 48, '2022-05-29 05:18:06', '2021-09-04', 7),
(3628, 21, 57, '2022-05-29 05:18:06', '2021-09-04', 7),
(3629, 21, 74, '2022-05-29 05:18:06', '2021-09-04', 7),
(3630, 21, 120, '2022-05-29 05:18:06', '2021-09-04', 7),
(3631, 21, 69, '2022-05-29 05:18:06', '2021-09-04', 7),
(3632, 21, 70, '2022-05-29 05:18:06', '2021-09-04', 7),
(3633, 21, 71, '2022-05-29 05:18:06', '2021-09-04', 7),
(3634, 21, 72, '2022-05-29 05:18:06', '2021-09-04', 7),
(3635, 21, 73, '2022-05-29 05:18:06', '2021-09-04', 7),
(3636, 21, 90, '2022-05-29 05:18:06', '2021-09-04', 7),
(3637, 21, 114, '2022-05-29 05:18:06', '2021-09-04', 7),
(3638, 21, 115, '2022-05-29 05:18:06', '2021-09-04', 7),
(3639, 21, 116, '2022-05-29 05:18:06', '2021-09-04', 7),
(3640, 21, 117, '2022-05-29 05:18:06', '2021-09-04', 7),
(3641, 21, 118, '2022-05-29 05:18:06', '2021-09-04', 7),
(3642, 21, 119, '2022-05-29 05:18:06', '2021-09-04', 7),
(3643, 23, 109, '2022-05-29 05:37:22', '2021-09-04', 7),
(3644, 23, 110, '2022-05-29 05:37:22', '2021-09-04', 7),
(3645, 23, 111, '2022-05-29 05:37:22', '2021-09-04', 7),
(3646, 23, 112, '2022-05-29 05:37:22', '2021-09-04', 7),
(3647, 23, 106, '2022-05-29 05:37:22', '2021-09-04', 7),
(3648, 23, 107, '2022-05-29 05:37:22', '2021-09-04', 7),
(3649, 23, 64, '2022-05-29 05:37:22', '2021-09-04', 7),
(3650, 23, 66, '2022-05-29 05:37:22', '2021-09-04', 7),
(3651, 23, 91, '2022-05-29 05:37:22', '2021-09-04', 7),
(3652, 23, 86, '2022-05-29 05:37:22', '2021-09-04', 7),
(3653, 23, 87, '2022-05-29 05:37:22', '2021-09-04', 7),
(3654, 23, 88, '2022-05-29 05:37:22', '2021-09-04', 7),
(3655, 23, 89, '2022-05-29 05:37:22', '2021-09-04', 7),
(3656, 23, 105, '2022-05-29 05:37:22', '2021-09-04', 7),
(3657, 23, 99, '2022-05-29 05:37:22', '2021-09-04', 7),
(3658, 23, 100, '2022-05-29 05:37:22', '2021-09-04', 7),
(3659, 23, 101, '2022-05-29 05:37:22', '2021-09-04', 7),
(3660, 23, 102, '2022-05-29 05:37:22', '2021-09-04', 7),
(3661, 23, 103, '2022-05-29 05:37:22', '2021-09-04', 7),
(3662, 23, 104, '2022-05-29 05:37:22', '2021-09-04', 7),
(3663, 23, 96, '2022-05-29 05:37:22', '2021-09-04', 7),
(3664, 23, 97, '2022-05-29 05:37:22', '2021-09-04', 7),
(3665, 23, 98, '2022-05-29 05:37:22', '2021-09-04', 7),
(3666, 23, 108, '2022-05-29 05:37:22', '2021-09-04', 7),
(3667, 23, 92, '2022-05-29 05:37:22', '2021-09-04', 7),
(3668, 23, 93, '2022-05-29 05:37:22', '2021-09-04', 7),
(3669, 23, 94, '2022-05-29 05:37:22', '2021-09-04', 7),
(3670, 23, 95, '2022-05-29 05:37:22', '2021-09-04', 7),
(3671, 23, 58, '2022-05-29 05:37:22', '2021-09-04', 7),
(3672, 23, 60, '2022-05-29 05:37:22', '2021-09-04', 7),
(3673, 23, 75, '2022-05-29 05:37:22', '2021-09-04', 7),
(3674, 23, 76, '2022-05-29 05:37:22', '2021-09-04', 7),
(3675, 23, 77, '2022-05-29 05:37:22', '2021-09-04', 7),
(3676, 23, 78, '2022-05-29 05:37:22', '2021-09-04', 7),
(3677, 23, 50, '2022-05-29 05:37:22', '2021-09-04', 7),
(3678, 23, 62, '2022-05-29 05:37:22', '2021-09-04', 7),
(3679, 23, 52, '2022-05-29 05:37:22', '2021-09-04', 7),
(3680, 23, 65, '2022-05-29 05:37:22', '2021-09-04', 7),
(3681, 23, 67, '2022-05-29 05:37:22', '2021-09-04', 7),
(3682, 23, 68, '2022-05-29 05:37:22', '2021-09-04', 7),
(3683, 23, 79, '2022-05-29 05:37:22', '2021-09-04', 7),
(3684, 23, 113, '2022-05-29 05:37:22', '2021-09-04', 7),
(3685, 23, 40, '2022-05-29 05:37:22', '2021-09-04', 7),
(3686, 23, 70, '2022-05-29 05:37:22', '2021-09-04', 7),
(3687, 23, 71, '2022-05-29 05:37:22', '2021-09-04', 7),
(3688, 23, 72, '2022-05-29 05:37:22', '2021-09-04', 7),
(3689, 23, 73, '2022-05-29 05:37:22', '2021-09-04', 7),
(3690, 23, 115, '2022-05-29 05:37:22', '2021-09-04', 7),
(3691, 23, 116, '2022-05-29 05:37:22', '2021-09-04', 7),
(3692, 23, 117, '2022-05-29 05:37:22', '2021-09-04', 7),
(3693, 23, 118, '2022-05-29 05:37:22', '2021-09-04', 7),
(3694, 23, 119, '2022-05-29 05:37:22', '2021-09-04', 7),
(3695, 22, 111, '2022-05-29 06:27:36', '2021-09-04', 7),
(3696, 22, 112, '2022-05-29 06:27:36', '2021-09-04', 7),
(3697, 22, 64, '2022-05-29 06:27:36', '2021-09-04', 7),
(3698, 22, 66, '2022-05-29 06:27:36', '2021-09-04', 7),
(3699, 22, 91, '2022-05-29 06:27:36', '2021-09-04', 7),
(3700, 22, 84, '2022-05-29 06:27:36', '2021-09-04', 7),
(3701, 22, 87, '2022-05-29 06:27:36', '2021-09-04', 7),
(3702, 22, 89, '2022-05-29 06:27:36', '2021-09-04', 7),
(3703, 22, 93, '2022-05-29 06:27:36', '2021-09-04', 7),
(3704, 22, 95, '2022-05-29 06:27:36', '2021-09-04', 7),
(3705, 22, 58, '2022-05-29 06:27:36', '2021-09-04', 7),
(3706, 22, 60, '2022-05-29 06:27:36', '2021-09-04', 7),
(3707, 22, 75, '2022-05-29 06:27:36', '2021-09-04', 7),
(3708, 22, 76, '2022-05-29 06:27:36', '2021-09-04', 7),
(3709, 22, 77, '2022-05-29 06:27:36', '2021-09-04', 7),
(3710, 22, 50, '2022-05-29 06:27:36', '2021-09-04', 7),
(3711, 22, 62, '2022-05-29 06:27:36', '2021-09-04', 7),
(3712, 22, 52, '2022-05-29 06:27:36', '2021-09-04', 7),
(3713, 22, 65, '2022-05-29 06:27:36', '2021-09-04', 7),
(3714, 22, 67, '2022-05-29 06:27:36', '2021-09-04', 7),
(3715, 22, 68, '2022-05-29 06:27:36', '2021-09-04', 7),
(3716, 22, 79, '2022-05-29 06:27:36', '2021-09-04', 7),
(3717, 22, 40, '2022-05-29 06:27:36', '2021-09-04', 7),
(3718, 22, 71, '2022-05-29 06:27:36', '2021-09-04', 7),
(3719, 22, 117, '2022-05-29 06:27:36', '2021-09-04', 7),
(3720, 22, 118, '2022-05-29 06:27:36', '2021-09-04', 7),
(3813, 24, 64, '2022-07-10 15:45:56', '2021-09-04', 7),
(3814, 24, 66, '2022-07-10 15:45:56', '2021-09-04', 7),
(3815, 24, 91, '2022-07-10 15:45:56', '2021-09-04', 7),
(3816, 24, 99, '2022-07-10 15:45:56', '2021-09-04', 7),
(3817, 24, 100, '2022-07-10 15:45:56', '2021-09-04', 7),
(3818, 24, 102, '2022-07-10 15:45:56', '2021-09-04', 7),
(3819, 24, 96, '2022-07-10 15:45:56', '2021-09-04', 7),
(3820, 24, 92, '2022-07-10 15:45:56', '2021-09-04', 7),
(3821, 24, 93, '2022-07-10 15:45:56', '2021-09-04', 7),
(3822, 24, 94, '2022-07-10 15:45:56', '2021-09-04', 7),
(3823, 24, 95, '2022-07-10 15:45:56', '2021-09-04', 7),
(3824, 24, 82, '2022-07-10 15:45:56', '2021-09-04', 7),
(3825, 24, 81, '2022-07-10 15:45:56', '2021-09-04', 7),
(3826, 24, 77, '2022-07-10 15:45:56', '2021-09-04', 7),
(3827, 24, 78, '2022-07-10 15:45:56', '2021-09-04', 7),
(3828, 24, 50, '2022-07-10 15:45:56', '2021-09-04', 7),
(3829, 24, 62, '2022-07-10 15:45:56', '2021-09-04', 7),
(3830, 24, 52, '2022-07-10 15:45:56', '2021-09-04', 7),
(3831, 24, 65, '2022-07-10 15:45:56', '2021-09-04', 7),
(3832, 24, 67, '2022-07-10 15:45:56', '2021-09-04', 7),
(3833, 24, 68, '2022-07-10 15:45:56', '2021-09-04', 7),
(3834, 24, 79, '2022-07-10 15:45:56', '2021-09-04', 7),
(3835, 24, 80, '2022-07-10 15:45:56', '2021-09-04', 7),
(3836, 24, 113, '2022-07-10 15:45:56', '2021-09-04', 7),
(3837, 24, 40, '2022-07-10 15:45:56', '2021-09-04', 7),
(3838, 24, 72, '2022-07-10 15:45:56', '2021-09-04', 7),
(3839, 24, 73, '2022-07-10 15:45:56', '2021-09-04', 7),
(3840, 24, 116, '2022-07-10 15:45:56', '2021-09-04', 7),
(3841, 24, 117, '2022-07-10 15:45:56', '2021-09-04', 7),
(3842, 24, 119, '2022-07-10 15:45:56', '2021-09-04', 7),
(3843, 8, 61, '2022-07-10 15:46:47', '2021-09-04', 7),
(3844, 8, 64, '2022-07-10 15:46:47', '2021-09-04', 7),
(3845, 8, 66, '2022-07-10 15:46:47', '2021-09-04', 7),
(3846, 8, 91, '2022-07-10 15:46:47', '2021-09-04', 7),
(3847, 8, 52, '2022-07-10 15:46:47', '2021-09-04', 7),
(3848, 8, 65, '2022-07-10 15:46:47', '2021-09-04', 7),
(3849, 8, 67, '2022-07-10 15:46:47', '2021-09-04', 7),
(3850, 8, 68, '2022-07-10 15:46:47', '2021-09-04', 7),
(3851, 8, 79, '2022-07-10 15:46:47', '2021-09-04', 7),
(3852, 8, 80, '2022-07-10 15:46:47', '2021-09-04', 7),
(3853, 8, 113, '2022-07-10 15:46:47', '2021-09-04', 7),
(3854, 8, 40, '2022-07-10 15:46:47', '2021-09-04', 7),
(9511, 33, 109, '2024-03-15 05:50:31', '2021-09-04', 110),
(9512, 33, 111, '2024-03-15 05:50:31', '2021-09-04', 110),
(9513, 33, 112, '2024-03-15 05:50:31', '2021-09-04', 110),
(9514, 33, 106, '2024-03-15 05:50:31', '2021-09-04', 110),
(9515, 33, 107, '2024-03-15 05:50:31', '2021-09-04', 110),
(9516, 33, 61, '2024-03-15 05:50:31', '2021-09-04', 110),
(9517, 33, 126, '2024-03-15 05:50:31', '2021-09-04', 110),
(9518, 33, 127, '2024-03-15 05:50:31', '2021-09-04', 110),
(9519, 33, 128, '2024-03-15 05:50:31', '2021-09-04', 110),
(9520, 33, 64, '2024-03-15 05:50:31', '2021-09-04', 110),
(9521, 33, 66, '2024-03-15 05:50:31', '2021-09-04', 110),
(9522, 33, 91, '2024-03-15 05:50:31', '2021-09-04', 110),
(9523, 33, 138, '2024-03-15 05:50:31', '2021-09-04', 110),
(9524, 33, 84, '2024-03-15 05:50:31', '2021-09-04', 110),
(9525, 33, 85, '2024-03-15 05:50:31', '2021-09-04', 110),
(9526, 33, 105, '2024-03-15 05:50:31', '2021-09-04', 110),
(9527, 33, 137, '2024-03-15 05:50:31', '2021-09-04', 110),
(9528, 33, 99, '2024-03-15 05:50:31', '2021-09-04', 110),
(9529, 33, 100, '2024-03-15 05:50:31', '2021-09-04', 110),
(9530, 33, 101, '2024-03-15 05:50:31', '2021-09-04', 110),
(9531, 33, 102, '2024-03-15 05:50:31', '2021-09-04', 110),
(9532, 33, 103, '2024-03-15 05:50:31', '2021-09-04', 110),
(9533, 33, 104, '2024-03-15 05:50:31', '2021-09-04', 110),
(9534, 33, 133, '2024-03-15 05:50:31', '2021-09-04', 110),
(9535, 33, 96, '2024-03-15 05:50:31', '2021-09-04', 110),
(9536, 33, 97, '2024-03-15 05:50:31', '2021-09-04', 110),
(9537, 33, 98, '2024-03-15 05:50:31', '2021-09-04', 110),
(9538, 33, 108, '2024-03-15 05:50:31', '2021-09-04', 110),
(9539, 33, 81, '2024-03-15 05:50:31', '2021-09-04', 110),
(9540, 33, 58, '2024-03-15 05:50:32', '2021-09-04', 110),
(9541, 33, 75, '2024-03-15 05:50:32', '2021-09-04', 110),
(9542, 33, 77, '2024-03-15 05:50:32', '2021-09-04', 110),
(9543, 33, 78, '2024-03-15 05:50:32', '2021-09-04', 110),
(9544, 33, 83, '2024-03-15 05:50:32', '2021-09-04', 110),
(9545, 33, 123, '2024-03-15 05:50:32', '2021-09-04', 110),
(9546, 33, 124, '2024-03-15 05:50:32', '2021-09-04', 110),
(9547, 33, 30, '2024-03-15 05:50:32', '2021-09-04', 110),
(9548, 33, 50, '2024-03-15 05:50:32', '2021-09-04', 110),
(9549, 33, 62, '2024-03-15 05:50:32', '2021-09-04', 110),
(9550, 33, 52, '2024-03-15 05:50:32', '2021-09-04', 110),
(9551, 33, 65, '2024-03-15 05:50:32', '2021-09-04', 110),
(9552, 33, 67, '2024-03-15 05:50:32', '2021-09-04', 110),
(9553, 33, 68, '2024-03-15 05:50:32', '2021-09-04', 110),
(9554, 33, 79, '2024-03-15 05:50:32', '2021-09-04', 110),
(9555, 33, 113, '2024-03-15 05:50:32', '2021-09-04', 110),
(9556, 33, 135, '2024-03-15 05:50:32', '2021-09-04', 110),
(9557, 33, 40, '2024-03-15 05:50:32', '2021-09-04', 110),
(9558, 33, 69, '2024-03-15 05:50:32', '2021-09-04', 110),
(9559, 33, 71, '2024-03-15 05:50:32', '2021-09-04', 110),
(9560, 33, 90, '2024-03-15 05:50:32', '2021-09-04', 110),
(9913, 31, 110, '2024-04-22 02:58:20', '2021-09-04', 110),
(9914, 31, 111, '2024-04-22 02:58:20', '2021-09-04', 110),
(9915, 31, 112, '2024-04-22 02:58:20', '2021-09-04', 110),
(9916, 31, 61, '2024-04-22 02:58:20', '2021-09-04', 110),
(9917, 31, 64, '2024-04-22 02:58:20', '2021-09-04', 110),
(9918, 31, 66, '2024-04-22 02:58:20', '2021-09-04', 110),
(9919, 31, 91, '2024-04-22 02:58:20', '2021-09-04', 110),
(9920, 31, 138, '2024-04-22 02:58:20', '2021-09-04', 110),
(9921, 31, 84, '2024-04-22 02:58:20', '2021-09-04', 110),
(9922, 31, 86, '2024-04-22 02:58:20', '2021-09-04', 110),
(9923, 31, 137, '2024-04-22 02:58:20', '2021-09-04', 110),
(9924, 31, 58, '2024-04-22 02:58:20', '2021-09-04', 110),
(9925, 31, 75, '2024-04-22 02:58:20', '2021-09-04', 110),
(9926, 31, 76, '2024-04-22 02:58:20', '2021-09-04', 110),
(9927, 31, 77, '2024-04-22 02:58:20', '2021-09-04', 110),
(9928, 31, 78, '2024-04-22 02:58:20', '2021-09-04', 110),
(9929, 31, 83, '2024-04-22 02:58:20', '2021-09-04', 110),
(9930, 31, 123, '2024-04-22 02:58:20', '2021-09-04', 110),
(9931, 31, 124, '2024-04-22 02:58:20', '2021-09-04', 110),
(9932, 31, 125, '2024-04-22 02:58:20', '2021-09-04', 110),
(9933, 31, 134, '2024-04-22 02:58:20', '2021-09-04', 110),
(9934, 31, 50, '2024-04-22 02:58:20', '2021-09-04', 110),
(9935, 31, 62, '2024-04-22 02:58:20', '2021-09-04', 110),
(9936, 31, 52, '2024-04-22 02:58:20', '2021-09-04', 110),
(9937, 31, 65, '2024-04-22 02:58:20', '2021-09-04', 110),
(9938, 31, 67, '2024-04-22 02:58:20', '2021-09-04', 110),
(9939, 31, 68, '2024-04-22 02:58:20', '2021-09-04', 110),
(9940, 31, 79, '2024-04-22 02:58:20', '2021-09-04', 110),
(9941, 31, 113, '2024-04-22 02:58:20', '2021-09-04', 110),
(9942, 31, 135, '2024-04-22 02:58:20', '2021-09-04', 110),
(9943, 31, 141, '2024-04-22 02:58:20', '2021-09-04', 110),
(9944, 31, 40, '2024-04-22 02:58:20', '2021-09-04', 110),
(9945, 31, 70, '2024-04-22 02:58:20', '2021-09-04', 110),
(9946, 31, 71, '2024-04-22 02:58:20', '2021-09-04', 110),
(9947, 31, 115, '2024-04-22 02:58:20', '2021-09-04', 110),
(9948, 31, 118, '2024-04-22 02:58:20', '2021-09-04', 110),
(9949, 31, 136, '2024-04-22 02:58:20', '2021-09-04', 110),
(9950, 31, 142, '2024-04-22 02:58:20', '2021-09-04', 110),
(9951, 31, 143, '2024-04-22 02:58:20', '2021-09-04', 110),
(9952, 31, 144, '2024-04-22 02:58:20', '2021-09-04', 110),
(9953, 31, 145, '2024-04-22 02:58:20', '2021-09-04', 110),
(9982, 32, 111, '2024-04-22 03:07:15', '2021-09-04', 110),
(9983, 32, 112, '2024-04-22 03:07:15', '2021-09-04', 110),
(9984, 32, 106, '2024-04-22 03:07:15', '2021-09-04', 110),
(9985, 32, 107, '2024-04-22 03:07:15', '2021-09-04', 110),
(9986, 32, 61, '2024-04-22 03:07:15', '2021-09-04', 110),
(9987, 32, 64, '2024-04-22 03:07:15', '2021-09-04', 110),
(9988, 32, 66, '2024-04-22 03:07:15', '2021-09-04', 110),
(9989, 32, 91, '2024-04-22 03:07:15', '2021-09-04', 110),
(9990, 32, 138, '2024-04-22 03:07:15', '2021-09-04', 110),
(9991, 32, 84, '2024-04-22 03:07:15', '2021-09-04', 110),
(9992, 32, 86, '2024-04-22 03:07:15', '2021-09-04', 110),
(9993, 32, 105, '2024-04-22 03:07:15', '2021-09-04', 110),
(9994, 32, 137, '2024-04-22 03:07:15', '2021-09-04', 110),
(9995, 32, 99, '2024-04-22 03:07:15', '2021-09-04', 110),
(9996, 32, 100, '2024-04-22 03:07:15', '2021-09-04', 110),
(9997, 32, 101, '2024-04-22 03:07:15', '2021-09-04', 110),
(9998, 32, 102, '2024-04-22 03:07:15', '2021-09-04', 110),
(9999, 32, 103, '2024-04-22 03:07:16', '2021-09-04', 110),
(10000, 32, 104, '2024-04-22 03:07:16', '2021-09-04', 110),
(10001, 32, 133, '2024-04-22 03:07:16', '2021-09-04', 110),
(10002, 32, 96, '2024-04-22 03:07:16', '2021-09-04', 110),
(10003, 32, 97, '2024-04-22 03:07:16', '2021-09-04', 110),
(10004, 32, 98, '2024-04-22 03:07:16', '2021-09-04', 110),
(10005, 32, 108, '2024-04-22 03:07:16', '2021-09-04', 110),
(10006, 32, 92, '2024-04-22 03:07:16', '2021-09-04', 110),
(10007, 32, 93, '2024-04-22 03:07:16', '2021-09-04', 110),
(10008, 32, 94, '2024-04-22 03:07:16', '2021-09-04', 110),
(10009, 32, 95, '2024-04-22 03:07:16', '2021-09-04', 110),
(10010, 32, 129, '2024-04-22 03:07:16', '2021-09-04', 110),
(10011, 32, 130, '2024-04-22 03:07:16', '2021-09-04', 110),
(10012, 32, 131, '2024-04-22 03:07:16', '2021-09-04', 110),
(10013, 32, 132, '2024-04-22 03:07:16', '2021-09-04', 110),
(10014, 32, 82, '2024-04-22 03:07:16', '2021-09-04', 110),
(10015, 32, 81, '2024-04-22 03:07:16', '2021-09-04', 110),
(10016, 32, 58, '2024-04-22 03:07:16', '2021-09-04', 110),
(10017, 32, 75, '2024-04-22 03:07:16', '2021-09-04', 110),
(10018, 32, 76, '2024-04-22 03:07:16', '2021-09-04', 110),
(10019, 32, 77, '2024-04-22 03:07:16', '2021-09-04', 110),
(10020, 32, 78, '2024-04-22 03:07:16', '2021-09-04', 110),
(10021, 32, 123, '2024-04-22 03:07:16', '2021-09-04', 110),
(10022, 32, 124, '2024-04-22 03:07:16', '2021-09-04', 110),
(10023, 32, 134, '2024-04-22 03:07:16', '2021-09-04', 110),
(10024, 32, 30, '2024-04-22 03:07:16', '2021-09-04', 110),
(10025, 32, 50, '2024-04-22 03:07:16', '2021-09-04', 110),
(10026, 32, 62, '2024-04-22 03:07:16', '2021-09-04', 110),
(10027, 32, 52, '2024-04-22 03:07:16', '2021-09-04', 110),
(10028, 32, 65, '2024-04-22 03:07:16', '2021-09-04', 110),
(10029, 32, 67, '2024-04-22 03:07:16', '2021-09-04', 110),
(10030, 32, 68, '2024-04-22 03:07:16', '2021-09-04', 110),
(10031, 32, 79, '2024-04-22 03:07:16', '2021-09-04', 110),
(10032, 32, 113, '2024-04-22 03:07:16', '2021-09-04', 110),
(10033, 32, 135, '2024-04-22 03:07:16', '2021-09-04', 110),
(10034, 32, 141, '2024-04-22 03:07:16', '2021-09-04', 110),
(10035, 32, 40, '2024-04-22 03:07:16', '2021-09-04', 110),
(10036, 32, 34, '2024-04-22 03:07:16', '2021-09-04', 110),
(10037, 32, 35, '2024-04-22 03:07:16', '2021-09-04', 110),
(10038, 32, 38, '2024-04-22 03:07:16', '2021-09-04', 110),
(10039, 32, 48, '2024-04-22 03:07:16', '2021-09-04', 110),
(10040, 32, 57, '2024-04-22 03:07:16', '2021-09-04', 110),
(10041, 32, 74, '2024-04-22 03:07:16', '2021-09-04', 110),
(10042, 32, 120, '2024-04-22 03:07:16', '2021-09-04', 110),
(10043, 32, 122, '2024-04-22 03:07:16', '2021-09-04', 110),
(10044, 32, 69, '2024-04-22 03:07:16', '2021-09-04', 110),
(10045, 32, 70, '2024-04-22 03:07:16', '2021-09-04', 110),
(10046, 32, 71, '2024-04-22 03:07:16', '2021-09-04', 110),
(10047, 32, 72, '2024-04-22 03:07:16', '2021-09-04', 110),
(10048, 32, 114, '2024-04-22 03:07:16', '2021-09-04', 110),
(10049, 32, 136, '2024-04-22 03:07:16', '2021-09-04', 110),
(10050, 36, 109, '2024-04-22 03:08:33', '2021-09-04', 110),
(10051, 36, 110, '2024-04-22 03:08:33', '2021-09-04', 110),
(10052, 36, 111, '2024-04-22 03:08:33', '2021-09-04', 110),
(10053, 36, 112, '2024-04-22 03:08:33', '2021-09-04', 110),
(10054, 36, 106, '2024-04-22 03:08:33', '2021-09-04', 110),
(10055, 36, 107, '2024-04-22 03:08:33', '2021-09-04', 110),
(10056, 36, 61, '2024-04-22 03:08:33', '2021-09-04', 110),
(10057, 36, 64, '2024-04-22 03:08:33', '2021-09-04', 110),
(10058, 36, 66, '2024-04-22 03:08:33', '2021-09-04', 110),
(10059, 36, 91, '2024-04-22 03:08:33', '2021-09-04', 110),
(10060, 36, 138, '2024-04-22 03:08:33', '2021-09-04', 110),
(10061, 36, 84, '2024-04-22 03:08:33', '2021-09-04', 110),
(10062, 36, 137, '2024-04-22 03:08:33', '2021-09-04', 110),
(10063, 36, 58, '2024-04-22 03:08:33', '2021-09-04', 110),
(10064, 36, 75, '2024-04-22 03:08:33', '2021-09-04', 110),
(10065, 36, 77, '2024-04-22 03:08:33', '2021-09-04', 110),
(10066, 36, 78, '2024-04-22 03:08:33', '2021-09-04', 110),
(10067, 36, 134, '2024-04-22 03:08:33', '2021-09-04', 110),
(10068, 36, 30, '2024-04-22 03:08:33', '2021-09-04', 110),
(10069, 36, 50, '2024-04-22 03:08:33', '2021-09-04', 110),
(10070, 36, 62, '2024-04-22 03:08:33', '2021-09-04', 110),
(10071, 36, 52, '2024-04-22 03:08:33', '2021-09-04', 110),
(10072, 36, 65, '2024-04-22 03:08:33', '2021-09-04', 110),
(10073, 36, 67, '2024-04-22 03:08:33', '2021-09-04', 110),
(10074, 36, 68, '2024-04-22 03:08:33', '2021-09-04', 110),
(10075, 36, 79, '2024-04-22 03:08:33', '2021-09-04', 110),
(10076, 36, 113, '2024-04-22 03:08:33', '2021-09-04', 110),
(10077, 36, 135, '2024-04-22 03:08:33', '2021-09-04', 110),
(10078, 36, 141, '2024-04-22 03:08:33', '2021-09-04', 110),
(10079, 36, 146, '2024-04-22 03:08:33', '2021-09-04', 110),
(10080, 36, 40, '2024-04-22 03:08:33', '2021-09-04', 110),
(10081, 36, 71, '2024-04-22 03:08:33', '2021-09-04', 110),
(10082, 36, 118, '2024-04-22 03:08:33', '2021-09-04', 110),
(10083, 40, 106, '2024-06-07 11:11:56', '2021-09-04', 110),
(10084, 40, 107, '2024-06-07 11:11:56', '2021-09-04', 110),
(10085, 40, 61, '2024-06-07 11:11:56', '2021-09-04', 110),
(10086, 40, 126, '2024-06-07 11:11:56', '2021-09-04', 110),
(10087, 40, 127, '2024-06-07 11:11:56', '2021-09-04', 110),
(10088, 40, 64, '2024-06-07 11:11:56', '2021-09-04', 110),
(10089, 40, 66, '2024-06-07 11:11:56', '2021-09-04', 110),
(10090, 40, 91, '2024-06-07 11:11:56', '2021-09-04', 110),
(10091, 40, 138, '2024-06-07 11:11:56', '2021-09-04', 110),
(10092, 40, 84, '2024-06-07 11:11:56', '2021-09-04', 110),
(10093, 40, 85, '2024-06-07 11:11:56', '2021-09-04', 110),
(10094, 40, 86, '2024-06-07 11:11:56', '2021-09-04', 110),
(10095, 40, 105, '2024-06-07 11:11:56', '2021-09-04', 110),
(10096, 40, 137, '2024-06-07 11:11:56', '2021-09-04', 110),
(10097, 40, 99, '2024-06-07 11:11:56', '2021-09-04', 110),
(10098, 40, 100, '2024-06-07 11:11:57', '2021-09-04', 110),
(10099, 40, 101, '2024-06-07 11:11:57', '2021-09-04', 110),
(10100, 40, 102, '2024-06-07 11:11:57', '2021-09-04', 110),
(10101, 40, 103, '2024-06-07 11:11:57', '2021-09-04', 110),
(10102, 40, 104, '2024-06-07 11:11:57', '2021-09-04', 110),
(10103, 40, 133, '2024-06-07 11:11:57', '2021-09-04', 110),
(10104, 40, 96, '2024-06-07 11:11:57', '2021-09-04', 110),
(10105, 40, 97, '2024-06-07 11:11:57', '2021-09-04', 110),
(10106, 40, 98, '2024-06-07 11:11:57', '2021-09-04', 110),
(10107, 40, 108, '2024-06-07 11:11:57', '2021-09-04', 110),
(10108, 40, 92, '2024-06-07 11:11:57', '2021-09-04', 110),
(10109, 40, 93, '2024-06-07 11:11:57', '2021-09-04', 110),
(10110, 40, 94, '2024-06-07 11:11:57', '2021-09-04', 110),
(10111, 40, 95, '2024-06-07 11:11:57', '2021-09-04', 110),
(10112, 40, 129, '2024-06-07 11:11:57', '2021-09-04', 110),
(10113, 40, 81, '2024-06-07 11:11:57', '2021-09-04', 110),
(10114, 40, 58, '2024-06-07 11:11:57', '2021-09-04', 110),
(10115, 40, 75, '2024-06-07 11:11:57', '2021-09-04', 110),
(10116, 40, 77, '2024-06-07 11:11:57', '2021-09-04', 110),
(10117, 40, 83, '2024-06-07 11:11:57', '2021-09-04', 110),
(10118, 40, 123, '2024-06-07 11:11:57', '2021-09-04', 110),
(10119, 40, 124, '2024-06-07 11:11:57', '2021-09-04', 110),
(10120, 40, 134, '2024-06-07 11:11:57', '2021-09-04', 110),
(10121, 40, 50, '2024-06-07 11:11:57', '2021-09-04', 110),
(10122, 40, 62, '2024-06-07 11:11:57', '2021-09-04', 110),
(10123, 40, 52, '2024-06-07 11:11:57', '2021-09-04', 110),
(10124, 40, 65, '2024-06-07 11:11:57', '2021-09-04', 110),
(10125, 40, 67, '2024-06-07 11:11:57', '2021-09-04', 110),
(10126, 40, 68, '2024-06-07 11:11:57', '2021-09-04', 110),
(10127, 40, 79, '2024-06-07 11:11:57', '2021-09-04', 110),
(10128, 40, 113, '2024-06-07 11:11:57', '2021-09-04', 110),
(10129, 40, 135, '2024-06-07 11:11:57', '2021-09-04', 110),
(10130, 40, 141, '2024-06-07 11:11:57', '2021-09-04', 110),
(10131, 40, 146, '2024-06-07 11:11:57', '2021-09-04', 110),
(10132, 40, 40, '2024-06-07 11:11:57', '2021-09-04', 110),
(10133, 40, 34, '2024-06-07 11:11:57', '2021-09-04', 110),
(10134, 40, 35, '2024-06-07 11:11:57', '2021-09-04', 110),
(10135, 40, 69, '2024-06-07 11:11:57', '2021-09-04', 110),
(10136, 40, 70, '2024-06-07 11:11:57', '2021-09-04', 110),
(10137, 40, 71, '2024-06-07 11:11:57', '2021-09-04', 110),
(10138, 40, 90, '2024-06-07 11:11:57', '2021-09-04', 110),
(10139, 40, 136, '2024-06-07 11:11:57', '2021-09-04', 110),
(10159, 38, 109, '2024-06-13 07:51:12', '2021-09-04', 110),
(10160, 38, 111, '2024-06-13 07:51:12', '2021-09-04', 110),
(10161, 38, 112, '2024-06-13 07:51:12', '2021-09-04', 110),
(10162, 38, 106, '2024-06-13 07:51:12', '2021-09-04', 110),
(10163, 38, 107, '2024-06-13 07:51:12', '2021-09-04', 110),
(10164, 38, 61, '2024-06-13 07:51:12', '2021-09-04', 110),
(10165, 38, 126, '2024-06-13 07:51:12', '2021-09-04', 110),
(10166, 38, 128, '2024-06-13 07:51:12', '2021-09-04', 110),
(10167, 38, 64, '2024-06-13 07:51:12', '2021-09-04', 110),
(10168, 38, 66, '2024-06-13 07:51:12', '2021-09-04', 110),
(10169, 38, 91, '2024-06-13 07:51:12', '2021-09-04', 110),
(10170, 38, 138, '2024-06-13 07:51:12', '2021-09-04', 110),
(10171, 38, 84, '2024-06-13 07:51:12', '2021-09-04', 110),
(10172, 38, 85, '2024-06-13 07:51:12', '2021-09-04', 110),
(10173, 38, 105, '2024-06-13 07:51:12', '2021-09-04', 110),
(10174, 38, 58, '2024-06-13 07:51:12', '2021-09-04', 110),
(10175, 38, 75, '2024-06-13 07:51:12', '2021-09-04', 110),
(10176, 38, 77, '2024-06-13 07:51:12', '2021-09-04', 110),
(10177, 38, 78, '2024-06-13 07:51:12', '2021-09-04', 110),
(10178, 38, 83, '2024-06-13 07:51:12', '2021-09-04', 110),
(10179, 38, 123, '2024-06-13 07:51:12', '2021-09-04', 110),
(10180, 38, 124, '2024-06-13 07:51:12', '2021-09-04', 110),
(10181, 38, 134, '2024-06-13 07:51:12', '2021-09-04', 110),
(10182, 38, 50, '2024-06-13 07:51:12', '2021-09-04', 110),
(10183, 38, 62, '2024-06-13 07:51:12', '2021-09-04', 110),
(10184, 38, 52, '2024-06-13 07:51:12', '2021-09-04', 110),
(10185, 38, 65, '2024-06-13 07:51:12', '2021-09-04', 110),
(10186, 38, 67, '2024-06-13 07:51:12', '2021-09-04', 110),
(10187, 38, 68, '2024-06-13 07:51:12', '2021-09-04', 110),
(10188, 38, 79, '2024-06-13 07:51:12', '2021-09-04', 110),
(10189, 38, 113, '2024-06-13 07:51:12', '2021-09-04', 110),
(10190, 38, 40, '2024-06-13 07:51:12', '2021-09-04', 110),
(10191, 38, 122, '2024-06-13 07:51:12', '2021-09-04', 110),
(10192, 38, 69, '2024-06-13 07:51:12', '2021-09-04', 110),
(10193, 38, 70, '2024-06-13 07:51:12', '2021-09-04', 110),
(10194, 38, 71, '2024-06-13 07:51:12', '2021-09-04', 110),
(10195, 38, 90, '2024-06-13 07:51:12', '2021-09-04', 110),
(10196, 38, 118, '2024-06-13 07:51:12', '2021-09-04', 110),
(10197, 38, 136, '2024-06-13 07:51:12', '2021-09-04', 110),
(10198, 38, 143, '2024-06-13 07:51:12', '2021-09-04', 110),
(10199, 38, 144, '2024-06-13 07:51:12', '2021-09-04', 110),
(10577, 39, 109, '2024-09-25 09:24:22', '2021-09-04', 110),
(10578, 39, 110, '2024-09-25 09:24:22', '2021-09-04', 110),
(10579, 39, 111, '2024-09-25 09:24:22', '2021-09-04', 110),
(10580, 39, 112, '2024-09-25 09:24:22', '2021-09-04', 110),
(10581, 39, 106, '2024-09-25 09:24:22', '2021-09-04', 110),
(10582, 39, 107, '2024-09-25 09:24:22', '2021-09-04', 110),
(10583, 39, 61, '2024-09-25 09:24:22', '2021-09-04', 110),
(10584, 39, 126, '2024-09-25 09:24:22', '2021-09-04', 110),
(10585, 39, 127, '2024-09-25 09:24:22', '2021-09-04', 110),
(10586, 39, 128, '2024-09-25 09:24:22', '2021-09-04', 110),
(10587, 39, 64, '2024-09-25 09:24:22', '2021-09-04', 110),
(10588, 39, 66, '2024-09-25 09:24:22', '2021-09-04', 110),
(10589, 39, 91, '2024-09-25 09:24:22', '2021-09-04', 110),
(10590, 39, 138, '2024-09-25 09:24:22', '2021-09-04', 110),
(10591, 39, 84, '2024-09-25 09:24:22', '2021-09-04', 110),
(10592, 39, 85, '2024-09-25 09:24:22', '2021-09-04', 110),
(10593, 39, 86, '2024-09-25 09:24:22', '2021-09-04', 110),
(10594, 39, 105, '2024-09-25 09:24:22', '2021-09-04', 110),
(10595, 39, 137, '2024-09-25 09:24:22', '2021-09-04', 110),
(10596, 39, 99, '2024-09-25 09:24:22', '2021-09-04', 110),
(10597, 39, 100, '2024-09-25 09:24:22', '2021-09-04', 110),
(10598, 39, 101, '2024-09-25 09:24:22', '2021-09-04', 110),
(10599, 39, 102, '2024-09-25 09:24:22', '2021-09-04', 110),
(10600, 39, 103, '2024-09-25 09:24:22', '2021-09-04', 110),
(10601, 39, 104, '2024-09-25 09:24:22', '2021-09-04', 110),
(10602, 39, 133, '2024-09-25 09:24:22', '2021-09-04', 110),
(10603, 39, 147, '2024-09-25 09:24:22', '2021-09-04', 110),
(10604, 39, 96, '2024-09-25 09:24:22', '2021-09-04', 110),
(10605, 39, 97, '2024-09-25 09:24:22', '2021-09-04', 110),
(10606, 39, 98, '2024-09-25 09:24:22', '2021-09-04', 110),
(10607, 39, 108, '2024-09-25 09:24:22', '2021-09-04', 110),
(10608, 39, 92, '2024-09-25 09:24:22', '2021-09-04', 110),
(10609, 39, 93, '2024-09-25 09:24:22', '2021-09-04', 110),
(10610, 39, 94, '2024-09-25 09:24:22', '2021-09-04', 110),
(10611, 39, 95, '2024-09-25 09:24:22', '2021-09-04', 110),
(10612, 39, 129, '2024-09-25 09:24:22', '2021-09-04', 110),
(10613, 39, 130, '2024-09-25 09:24:22', '2021-09-04', 110),
(10614, 39, 131, '2024-09-25 09:24:22', '2021-09-04', 110),
(10615, 39, 132, '2024-09-25 09:24:22', '2021-09-04', 110),
(10616, 39, 82, '2024-09-25 09:24:22', '2021-09-04', 110),
(10617, 39, 81, '2024-09-25 09:24:22', '2021-09-04', 110),
(10618, 39, 58, '2024-09-25 09:24:22', '2021-09-04', 110),
(10619, 39, 75, '2024-09-25 09:24:22', '2021-09-04', 110),
(10620, 39, 76, '2024-09-25 09:24:22', '2021-09-04', 110),
(10621, 39, 77, '2024-09-25 09:24:22', '2021-09-04', 110),
(10622, 39, 78, '2024-09-25 09:24:22', '2021-09-04', 110),
(10623, 39, 83, '2024-09-25 09:24:22', '2021-09-04', 110),
(10624, 39, 123, '2024-09-25 09:24:22', '2021-09-04', 110),
(10625, 39, 124, '2024-09-25 09:24:22', '2021-09-04', 110),
(10626, 39, 125, '2024-09-25 09:24:22', '2021-09-04', 110),
(10627, 39, 134, '2024-09-25 09:24:22', '2021-09-04', 110),
(10628, 39, 30, '2024-09-25 09:24:22', '2021-09-04', 110),
(10629, 39, 31, '2024-09-25 09:24:22', '2021-09-04', 110),
(10630, 39, 39, '2024-09-25 09:24:22', '2021-09-04', 110),
(10631, 39, 42, '2024-09-25 09:24:22', '2021-09-04', 110),
(10632, 39, 43, '2024-09-25 09:24:22', '2021-09-04', 110),
(10633, 39, 121, '2024-09-25 09:24:22', '2021-09-04', 110),
(10634, 39, 50, '2024-09-25 09:24:22', '2021-09-04', 110),
(10635, 39, 62, '2024-09-25 09:24:22', '2021-09-04', 110),
(10636, 39, 52, '2024-09-25 09:24:22', '2021-09-04', 110),
(10637, 39, 65, '2024-09-25 09:24:22', '2021-09-04', 110),
(10638, 39, 67, '2024-09-25 09:24:22', '2021-09-04', 110),
(10639, 39, 68, '2024-09-25 09:24:22', '2021-09-04', 110),
(10640, 39, 79, '2024-09-25 09:24:22', '2021-09-04', 110),
(10641, 39, 113, '2024-09-25 09:24:22', '2021-09-04', 110),
(10642, 39, 135, '2024-09-25 09:24:22', '2021-09-04', 110),
(10643, 39, 141, '2024-09-25 09:24:22', '2021-09-04', 110),
(10644, 39, 146, '2024-09-25 09:24:22', '2021-09-04', 110),
(10645, 39, 148, '2024-09-25 09:24:22', '2021-09-04', 110),
(10646, 39, 149, '2024-09-25 09:24:22', '2021-09-04', 110),
(10647, 39, 40, '2024-09-25 09:24:22', '2021-09-04', 110),
(10648, 39, 34, '2024-09-25 09:24:22', '2021-09-04', 110),
(10649, 39, 35, '2024-09-25 09:24:22', '2021-09-04', 110),
(10650, 39, 38, '2024-09-25 09:24:22', '2021-09-04', 110),
(10651, 39, 48, '2024-09-25 09:24:22', '2021-09-04', 110),
(10652, 39, 57, '2024-09-25 09:24:22', '2021-09-04', 110),
(10653, 39, 74, '2024-09-25 09:24:22', '2021-09-04', 110),
(10654, 39, 120, '2024-09-25 09:24:22', '2021-09-04', 110),
(10655, 39, 122, '2024-09-25 09:24:22', '2021-09-04', 110),
(10656, 39, 69, '2024-09-25 09:24:22', '2021-09-04', 110),
(10657, 39, 70, '2024-09-25 09:24:22', '2021-09-04', 110),
(10658, 39, 71, '2024-09-25 09:24:22', '2021-09-04', 110),
(10659, 39, 72, '2024-09-25 09:24:22', '2021-09-04', 110),
(10660, 39, 90, '2024-09-25 09:24:22', '2021-09-04', 110),
(10661, 39, 114, '2024-09-25 09:24:22', '2021-09-04', 110),
(10662, 39, 115, '2024-09-25 09:24:22', '2021-09-04', 110),
(10663, 39, 118, '2024-09-25 09:24:22', '2021-09-04', 110),
(10664, 39, 136, '2024-09-25 09:24:22', '2021-09-04', 110),
(10665, 39, 142, '2024-09-25 09:24:22', '2021-09-04', 110),
(10666, 39, 143, '2024-09-25 09:24:22', '2021-09-04', 110),
(10667, 39, 144, '2024-09-25 09:24:22', '2021-09-04', 110),
(10668, 39, 145, '2024-09-25 09:24:22', '2021-09-04', 110),
(10836, 34, 109, '2024-10-14 09:38:51', '2021-09-04', 110),
(10837, 34, 110, '2024-10-14 09:38:51', '2021-09-04', 110),
(10838, 34, 106, '2024-10-14 09:38:51', '2021-09-04', 110),
(10839, 34, 107, '2024-10-14 09:38:51', '2021-09-04', 110),
(10840, 34, 61, '2024-10-14 09:38:51', '2021-09-04', 110),
(10841, 34, 126, '2024-10-14 09:38:51', '2021-09-04', 110),
(10842, 34, 127, '2024-10-14 09:38:51', '2021-09-04', 110),
(10843, 34, 128, '2024-10-14 09:38:51', '2021-09-04', 110),
(10844, 34, 64, '2024-10-14 09:38:51', '2021-09-04', 110),
(10845, 34, 66, '2024-10-14 09:38:51', '2021-09-04', 110),
(10846, 34, 91, '2024-10-14 09:38:51', '2021-09-04', 110),
(10847, 34, 84, '2024-10-14 09:38:51', '2021-09-04', 110),
(10848, 34, 85, '2024-10-14 09:38:51', '2021-09-04', 110),
(10849, 34, 86, '2024-10-14 09:38:51', '2021-09-04', 110),
(10850, 34, 105, '2024-10-14 09:38:51', '2021-09-04', 110),
(10851, 34, 137, '2024-10-14 09:38:51', '2021-09-04', 110),
(10852, 34, 99, '2024-10-14 09:38:51', '2021-09-04', 110),
(10853, 34, 100, '2024-10-14 09:38:51', '2021-09-04', 110),
(10854, 34, 101, '2024-10-14 09:38:51', '2021-09-04', 110),
(10855, 34, 102, '2024-10-14 09:38:51', '2021-09-04', 110),
(10856, 34, 103, '2024-10-14 09:38:51', '2021-09-04', 110),
(10857, 34, 104, '2024-10-14 09:38:51', '2021-09-04', 110),
(10858, 34, 133, '2024-10-14 09:38:51', '2021-09-04', 110),
(10859, 34, 147, '2024-10-14 09:38:51', '2021-09-04', 110),
(10860, 34, 96, '2024-10-14 09:38:51', '2021-09-04', 110),
(10861, 34, 97, '2024-10-14 09:38:51', '2021-09-04', 110),
(10862, 34, 98, '2024-10-14 09:38:51', '2021-09-04', 110),
(10863, 34, 92, '2024-10-14 09:38:51', '2021-09-04', 110),
(10864, 34, 93, '2024-10-14 09:38:51', '2021-09-04', 110),
(10865, 34, 94, '2024-10-14 09:38:51', '2021-09-04', 110),
(10866, 34, 95, '2024-10-14 09:38:51', '2021-09-04', 110),
(10867, 34, 129, '2024-10-14 09:38:51', '2021-09-04', 110),
(10868, 34, 58, '2024-10-14 09:38:51', '2021-09-04', 110),
(10869, 34, 75, '2024-10-14 09:38:51', '2021-09-04', 110),
(10870, 34, 77, '2024-10-14 09:38:51', '2021-09-04', 110),
(10871, 34, 83, '2024-10-14 09:38:51', '2021-09-04', 110),
(10872, 34, 123, '2024-10-14 09:38:51', '2021-09-04', 110),
(10873, 34, 124, '2024-10-14 09:38:51', '2021-09-04', 110),
(10874, 34, 50, '2024-10-14 09:38:51', '2021-09-04', 110),
(10875, 34, 62, '2024-10-14 09:38:51', '2021-09-04', 110),
(10876, 34, 52, '2024-10-14 09:38:51', '2021-09-04', 110),
(10877, 34, 65, '2024-10-14 09:38:51', '2021-09-04', 110),
(10878, 34, 67, '2024-10-14 09:38:51', '2021-09-04', 110),
(10879, 34, 68, '2024-10-14 09:38:51', '2021-09-04', 110),
(10880, 34, 79, '2024-10-14 09:38:51', '2021-09-04', 110),
(10881, 34, 113, '2024-10-14 09:38:51', '2021-09-04', 110),
(10882, 34, 135, '2024-10-14 09:38:51', '2021-09-04', 110),
(10883, 34, 141, '2024-10-14 09:38:51', '2021-09-04', 110),
(10884, 34, 148, '2024-10-14 09:38:51', '2021-09-04', 110),
(10885, 34, 149, '2024-10-14 09:38:51', '2021-09-04', 110),
(10886, 34, 40, '2024-10-14 09:38:51', '2021-09-04', 110),
(10887, 34, 69, '2024-10-14 09:38:51', '2021-09-04', 110),
(10888, 34, 70, '2024-10-14 09:38:51', '2021-09-04', 110),
(10889, 34, 71, '2024-10-14 09:38:51', '2021-09-04', 110),
(10890, 34, 72, '2024-10-14 09:38:51', '2021-09-04', 110),
(10891, 34, 90, '2024-10-14 09:38:51', '2021-09-04', 110),
(10892, 34, 136, '2024-10-14 09:38:51', '2021-09-04', 110),
(11317, 0, 109, '2024-10-24 17:02:39', '2021-09-04', 90),
(11318, 0, 111, '2024-10-24 17:02:39', '2021-09-04', 90),
(11319, 0, 112, '2024-10-24 17:02:39', '2021-09-04', 90),
(11320, 0, 106, '2024-10-24 17:02:39', '2021-09-04', 90),
(11321, 0, 107, '2024-10-24 17:02:39', '2021-09-04', 90),
(11322, 0, 61, '2024-10-24 17:02:39', '2021-09-04', 90),
(11323, 0, 126, '2024-10-24 17:02:39', '2021-09-04', 90),
(11324, 0, 127, '2024-10-24 17:02:39', '2021-09-04', 90),
(11325, 0, 64, '2024-10-24 17:02:39', '2021-09-04', 90),
(11326, 0, 66, '2024-10-24 17:02:39', '2021-09-04', 90),
(11327, 0, 91, '2024-10-24 17:02:39', '2021-09-04', 90),
(11328, 0, 138, '2024-10-24 17:02:39', '2021-09-04', 90),
(11329, 0, 84, '2024-10-24 17:02:39', '2021-09-04', 90),
(11330, 0, 85, '2024-10-24 17:02:39', '2021-09-04', 90),
(11331, 0, 105, '2024-10-24 17:02:39', '2021-09-04', 90),
(11332, 0, 137, '2024-10-24 17:02:39', '2021-09-04', 90),
(11333, 0, 99, '2024-10-24 17:02:39', '2021-09-04', 90),
(11334, 0, 100, '2024-10-24 17:02:39', '2021-09-04', 90),
(11335, 0, 101, '2024-10-24 17:02:39', '2021-09-04', 90),
(11336, 0, 102, '2024-10-24 17:02:39', '2021-09-04', 90),
(11337, 0, 103, '2024-10-24 17:02:39', '2021-09-04', 90),
(11338, 0, 104, '2024-10-24 17:02:39', '2021-09-04', 90),
(11339, 0, 133, '2024-10-24 17:02:39', '2021-09-04', 90),
(11340, 0, 147, '2024-10-24 17:02:39', '2021-09-04', 90),
(11341, 0, 96, '2024-10-24 17:02:39', '2021-09-04', 90),
(11342, 0, 97, '2024-10-24 17:02:39', '2021-09-04', 90),
(11343, 0, 98, '2024-10-24 17:02:39', '2021-09-04', 90),
(11344, 0, 108, '2024-10-24 17:02:39', '2021-09-04', 90),
(11345, 0, 81, '2024-10-24 17:02:39', '2021-09-04', 90),
(11346, 0, 58, '2024-10-24 17:02:39', '2021-09-04', 90),
(11347, 0, 75, '2024-10-24 17:02:39', '2021-09-04', 90),
(11348, 0, 77, '2024-10-24 17:02:39', '2021-09-04', 90),
(11349, 0, 78, '2024-10-24 17:02:39', '2021-09-04', 90),
(11350, 0, 83, '2024-10-24 17:02:39', '2021-09-04', 90),
(11351, 0, 123, '2024-10-24 17:02:39', '2021-09-04', 90),
(11352, 0, 124, '2024-10-24 17:02:39', '2021-09-04', 90),
(11353, 0, 134, '2024-10-24 17:02:39', '2021-09-04', 90),
(11354, 0, 30, '2024-10-24 17:02:39', '2021-09-04', 90),
(11355, 0, 31, '2024-10-24 17:02:39', '2021-09-04', 90),
(11356, 0, 42, '2024-10-24 17:02:39', '2021-09-04', 90),
(11357, 0, 43, '2024-10-24 17:02:39', '2021-09-04', 90),
(11358, 0, 50, '2024-10-24 17:02:39', '2021-09-04', 90),
(11359, 0, 62, '2024-10-24 17:02:39', '2021-09-04', 90),
(11360, 0, 52, '2024-10-24 17:02:39', '2021-09-04', 90),
(11361, 0, 65, '2024-10-24 17:02:39', '2021-09-04', 90),
(11362, 0, 67, '2024-10-24 17:02:39', '2021-09-04', 90),
(11363, 0, 68, '2024-10-24 17:02:39', '2021-09-04', 90),
(11364, 0, 79, '2024-10-24 17:02:39', '2021-09-04', 90),
(11365, 0, 113, '2024-10-24 17:02:39', '2021-09-04', 90),
(11366, 0, 135, '2024-10-24 17:02:39', '2021-09-04', 90),
(11367, 0, 141, '2024-10-24 17:02:39', '2021-09-04', 90),
(11368, 0, 146, '2024-10-24 17:02:39', '2021-09-04', 90),
(11369, 0, 148, '2024-10-24 17:02:39', '2021-09-04', 90),
(11370, 0, 149, '2024-10-24 17:02:39', '2021-09-04', 90),
(11371, 0, 40, '2024-10-24 17:02:39', '2021-09-04', 90),
(11372, 0, 34, '2024-10-24 17:02:39', '2021-09-04', 90),
(11373, 0, 35, '2024-10-24 17:02:39', '2021-09-04', 90),
(11374, 0, 38, '2024-10-24 17:02:39', '2021-09-04', 90),
(11375, 0, 48, '2024-10-24 17:02:39', '2021-09-04', 90),
(11376, 0, 57, '2024-10-24 17:02:39', '2021-09-04', 90),
(11377, 0, 74, '2024-10-24 17:02:39', '2021-09-04', 90),
(11378, 0, 120, '2024-10-24 17:02:39', '2021-09-04', 90),
(11379, 0, 122, '2024-10-24 17:02:39', '2021-09-04', 90),
(11380, 0, 69, '2024-10-24 17:02:39', '2021-09-04', 90),
(11381, 0, 71, '2024-10-24 17:02:39', '2021-09-04', 90),
(11382, 0, 72, '2024-10-24 17:02:39', '2021-09-04', 90),
(11383, 0, 90, '2024-10-24 17:02:39', '2021-09-04', 90),
(11384, 0, 114, '2024-10-24 17:02:39', '2021-09-04', 90),
(11385, 0, 118, '2024-10-24 17:02:39', '2021-09-04', 90),
(11386, 0, 136, '2024-10-24 17:02:39', '2021-09-04', 90),
(11387, 0, 142, '2024-10-24 17:02:39', '2021-09-04', 90),
(11388, 0, 144, '2024-10-24 17:02:39', '2021-09-04', 90),
(11389, 37, 64, '2024-11-07 08:51:17', '2021-09-04', 110),
(11390, 37, 66, '2024-11-07 08:51:17', '2021-09-04', 110),
(11391, 37, 91, '2024-11-07 08:51:17', '2021-09-04', 110),
(11392, 37, 138, '2024-11-07 08:51:17', '2021-09-04', 110),
(11393, 37, 84, '2024-11-07 08:51:17', '2021-09-04', 110),
(11394, 37, 58, '2024-11-07 08:51:17', '2021-09-04', 110),
(11395, 37, 50, '2024-11-07 08:51:17', '2021-09-04', 110),
(11396, 37, 62, '2024-11-07 08:51:17', '2021-09-04', 110),
(11397, 37, 52, '2024-11-07 08:51:17', '2021-09-04', 110),
(11398, 37, 65, '2024-11-07 08:51:17', '2021-09-04', 110),
(11399, 37, 67, '2024-11-07 08:51:17', '2021-09-04', 110),
(11400, 37, 68, '2024-11-07 08:51:17', '2021-09-04', 110),
(11401, 37, 79, '2024-11-07 08:51:17', '2021-09-04', 110),
(11402, 37, 113, '2024-11-07 08:51:17', '2021-09-04', 110),
(11403, 37, 135, '2024-11-07 08:51:17', '2021-09-04', 110),
(11404, 37, 141, '2024-11-07 08:51:17', '2021-09-04', 110),
(11405, 37, 146, '2024-11-07 08:51:17', '2021-09-04', 110),
(11406, 37, 149, '2024-11-07 08:51:17', '2021-09-04', 110),
(11407, 37, 40, '2024-11-07 08:51:17', '2021-09-04', 110),
(11408, 37, 71, '2024-11-07 08:51:17', '2021-09-04', 110),
(11690, 29, 168, '2025-04-25 14:52:03', '2021-09-04', 72),
(11691, 29, 172, '2025-04-25 14:52:03', '2021-09-04', 72),
(11692, 29, 173, '2025-04-25 14:52:03', '2021-09-04', 72);
INSERT INTO `access` (`id`, `roleid`, `controllerid`, `Date`, `system_date`, `added_by`) VALUES
(11693, 29, 61, '2025-04-25 14:52:03', '2021-09-04', 72),
(11694, 29, 64, '2025-04-25 14:52:03', '2021-09-04', 72),
(11695, 29, 66, '2025-04-25 14:52:03', '2021-09-04', 72),
(11696, 29, 91, '2025-04-25 14:52:03', '2021-09-04', 72),
(11697, 29, 58, '2025-04-25 14:52:03', '2021-09-04', 72),
(11698, 29, 78, '2025-04-25 14:52:03', '2021-09-04', 72),
(11699, 29, 83, '2025-04-25 14:52:03', '2021-09-04', 72),
(11700, 29, 123, '2025-04-25 14:52:03', '2021-09-04', 72),
(11701, 29, 124, '2025-04-25 14:52:03', '2021-09-04', 72),
(11702, 29, 62, '2025-04-25 14:52:03', '2021-09-04', 72),
(11703, 29, 52, '2025-04-25 14:52:03', '2021-09-04', 72),
(11704, 29, 65, '2025-04-25 14:52:03', '2021-09-04', 72),
(11705, 29, 67, '2025-04-25 14:52:03', '2021-09-04', 72),
(11706, 29, 68, '2025-04-25 14:52:03', '2021-09-04', 72),
(11707, 29, 79, '2025-04-25 14:52:03', '2021-09-04', 72),
(11708, 29, 134, '2025-04-25 14:52:03', '2021-09-04', 72),
(11709, 29, 136, '2025-04-25 14:52:03', '2021-09-04', 72),
(11710, 29, 139, '2025-04-25 14:52:03', '2021-09-04', 72),
(11711, 29, 140, '2025-04-25 14:52:03', '2021-09-04', 72),
(11712, 29, 69, '2025-04-25 14:52:03', '2021-09-04', 72),
(11713, 29, 71, '2025-04-25 14:52:03', '2021-09-04', 72),
(11714, 42, 168, '2025-04-25 15:00:17', '2021-09-04', 72),
(11715, 42, 170, '2025-04-25 15:00:17', '2021-09-04', 72),
(11716, 42, 171, '2025-04-25 15:00:17', '2021-09-04', 72),
(11717, 42, 172, '2025-04-25 15:00:17', '2021-09-04', 72),
(11718, 42, 174, '2025-04-25 15:00:17', '2021-09-04', 72),
(11719, 42, 175, '2025-04-25 15:00:17', '2021-09-04', 72),
(11720, 42, 106, '2025-04-25 15:00:17', '2021-09-04', 72),
(11721, 42, 61, '2025-04-25 15:00:17', '2021-09-04', 72),
(11722, 42, 64, '2025-04-25 15:00:17', '2021-09-04', 72),
(11723, 42, 66, '2025-04-25 15:00:17', '2021-09-04', 72),
(11724, 42, 91, '2025-04-25 15:00:17', '2021-09-04', 72),
(11725, 42, 58, '2025-04-25 15:00:17', '2021-09-04', 72),
(11726, 42, 76, '2025-04-25 15:00:17', '2021-09-04', 72),
(11727, 42, 77, '2025-04-25 15:00:17', '2021-09-04', 72),
(11728, 42, 78, '2025-04-25 15:00:17', '2021-09-04', 72),
(11729, 42, 43, '2025-04-25 15:00:17', '2021-09-04', 72),
(11730, 42, 62, '2025-04-25 15:00:17', '2021-09-04', 72),
(11731, 42, 52, '2025-04-25 15:00:17', '2021-09-04', 72),
(11732, 42, 65, '2025-04-25 15:00:17', '2021-09-04', 72),
(11733, 42, 67, '2025-04-25 15:00:17', '2021-09-04', 72),
(11734, 42, 68, '2025-04-25 15:00:17', '2021-09-04', 72),
(11735, 42, 79, '2025-04-25 15:00:17', '2021-09-04', 72),
(11736, 42, 134, '2025-04-25 15:00:17', '2021-09-04', 72),
(11737, 42, 136, '2025-04-25 15:00:17', '2021-09-04', 72),
(11738, 42, 139, '2025-04-25 15:00:17', '2021-09-04', 72),
(11739, 42, 140, '2025-04-25 15:00:17', '2021-09-04', 72),
(11740, 42, 71, '2025-04-25 15:00:17', '2021-09-04', 72),
(11741, 42, 72, '2025-04-25 15:00:17', '2021-09-04', 72),
(11742, 42, 114, '2025-04-25 15:00:17', '2021-09-04', 72),
(11743, 42, 118, '2025-04-25 15:00:17', '2021-09-04', 72),
(11848, 45, 168, '2025-04-25 15:19:31', '2021-09-04', 72),
(11849, 45, 170, '2025-04-25 15:19:31', '2021-09-04', 72),
(11850, 45, 171, '2025-04-25 15:19:31', '2021-09-04', 72),
(11851, 45, 172, '2025-04-25 15:19:31', '2021-09-04', 72),
(11852, 45, 174, '2025-04-25 15:19:31', '2021-09-04', 72),
(11853, 45, 175, '2025-04-25 15:19:31', '2021-09-04', 72),
(11854, 45, 106, '2025-04-25 15:19:31', '2021-09-04', 72),
(11855, 45, 61, '2025-04-25 15:19:31', '2021-09-04', 72),
(11856, 45, 126, '2025-04-25 15:19:31', '2021-09-04', 72),
(11857, 45, 127, '2025-04-25 15:19:31', '2021-09-04', 72),
(11858, 45, 64, '2025-04-25 15:19:31', '2021-09-04', 72),
(11859, 45, 66, '2025-04-25 15:19:31', '2021-09-04', 72),
(11860, 45, 91, '2025-04-25 15:19:31', '2021-09-04', 72),
(11861, 45, 43, '2025-04-25 15:19:31', '2021-09-04', 72),
(11862, 45, 121, '2025-04-25 15:19:31', '2021-09-04', 72),
(11863, 45, 62, '2025-04-25 15:19:31', '2021-09-04', 72),
(11864, 45, 52, '2025-04-25 15:19:31', '2021-09-04', 72),
(11865, 45, 65, '2025-04-25 15:19:31', '2021-09-04', 72),
(11866, 45, 67, '2025-04-25 15:19:31', '2021-09-04', 72),
(11867, 45, 68, '2025-04-25 15:19:31', '2021-09-04', 72),
(11868, 45, 79, '2025-04-25 15:19:31', '2021-09-04', 72),
(11869, 45, 80, '2025-04-25 15:19:31', '2021-09-04', 72),
(11870, 45, 134, '2025-04-25 15:19:31', '2021-09-04', 72),
(11871, 45, 136, '2025-04-25 15:19:31', '2021-09-04', 72),
(11872, 45, 139, '2025-04-25 15:19:31', '2021-09-04', 72),
(11873, 45, 140, '2025-04-25 15:19:31', '2021-09-04', 72),
(11874, 45, 70, '2025-04-25 15:19:31', '2021-09-04', 72),
(11875, 45, 71, '2025-04-25 15:19:31', '2021-09-04', 72),
(11876, 45, 115, '2025-04-25 15:19:31', '2021-09-04', 72),
(11877, 45, 176, '2025-04-25 15:19:31', '2021-09-04', 72),
(11878, 45, 177, '2025-04-25 15:19:31', '2021-09-04', 72),
(11879, 44, 168, '2025-04-25 15:19:42', '2021-09-04', 72),
(11880, 44, 170, '2025-04-25 15:19:42', '2021-09-04', 72),
(11881, 44, 171, '2025-04-25 15:19:42', '2021-09-04', 72),
(11882, 44, 172, '2025-04-25 15:19:42', '2021-09-04', 72),
(11883, 44, 174, '2025-04-25 15:19:42', '2021-09-04', 72),
(11884, 44, 175, '2025-04-25 15:19:42', '2021-09-04', 72),
(11885, 44, 61, '2025-04-25 15:19:42', '2021-09-04', 72),
(11886, 44, 64, '2025-04-25 15:19:42', '2021-09-04', 72),
(11887, 44, 66, '2025-04-25 15:19:42', '2021-09-04', 72),
(11888, 44, 91, '2025-04-25 15:19:42', '2021-09-04', 72),
(11889, 44, 43, '2025-04-25 15:19:42', '2021-09-04', 72),
(11890, 44, 121, '2025-04-25 15:19:42', '2021-09-04', 72),
(11891, 44, 62, '2025-04-25 15:19:42', '2021-09-04', 72),
(11892, 44, 52, '2025-04-25 15:19:42', '2021-09-04', 72),
(11893, 44, 65, '2025-04-25 15:19:42', '2021-09-04', 72),
(11894, 44, 67, '2025-04-25 15:19:42', '2021-09-04', 72),
(11895, 44, 68, '2025-04-25 15:19:42', '2021-09-04', 72),
(11896, 44, 79, '2025-04-25 15:19:42', '2021-09-04', 72),
(11897, 44, 80, '2025-04-25 15:19:42', '2021-09-04', 72),
(11898, 44, 134, '2025-04-25 15:19:42', '2021-09-04', 72),
(11899, 44, 136, '2025-04-25 15:19:42', '2021-09-04', 72),
(11900, 44, 139, '2025-04-25 15:19:42', '2021-09-04', 72),
(11901, 44, 140, '2025-04-25 15:19:42', '2021-09-04', 72),
(11902, 44, 70, '2025-04-25 15:19:42', '2021-09-04', 72),
(11903, 44, 71, '2025-04-25 15:19:42', '2021-09-04', 72),
(11904, 44, 115, '2025-04-25 15:19:42', '2021-09-04', 72),
(11905, 44, 176, '2025-04-25 15:19:42', '2021-09-04', 72),
(11906, 44, 177, '2025-04-25 15:19:42', '2021-09-04', 72),
(11907, 30, 168, '2025-04-25 15:20:13', '2021-09-04', 72),
(11908, 30, 170, '2025-04-25 15:20:13', '2021-09-04', 72),
(11909, 30, 171, '2025-04-25 15:20:13', '2021-09-04', 72),
(11910, 30, 172, '2025-04-25 15:20:13', '2021-09-04', 72),
(11911, 30, 174, '2025-04-25 15:20:13', '2021-09-04', 72),
(11912, 30, 175, '2025-04-25 15:20:13', '2021-09-04', 72),
(11913, 30, 61, '2025-04-25 15:20:13', '2021-09-04', 72),
(11914, 30, 127, '2025-04-25 15:20:13', '2021-09-04', 72),
(11915, 30, 64, '2025-04-25 15:20:13', '2021-09-04', 72),
(11916, 30, 66, '2025-04-25 15:20:13', '2021-09-04', 72),
(11917, 30, 91, '2025-04-25 15:20:13', '2021-09-04', 72),
(11918, 30, 43, '2025-04-25 15:20:13', '2021-09-04', 72),
(11919, 30, 121, '2025-04-25 15:20:13', '2021-09-04', 72),
(11920, 30, 62, '2025-04-25 15:20:13', '2021-09-04', 72),
(11921, 30, 52, '2025-04-25 15:20:13', '2021-09-04', 72),
(11922, 30, 65, '2025-04-25 15:20:13', '2021-09-04', 72),
(11923, 30, 67, '2025-04-25 15:20:13', '2021-09-04', 72),
(11924, 30, 68, '2025-04-25 15:20:13', '2021-09-04', 72),
(11925, 30, 79, '2025-04-25 15:20:13', '2021-09-04', 72),
(11926, 30, 80, '2025-04-25 15:20:13', '2021-09-04', 72),
(11927, 30, 134, '2025-04-25 15:20:13', '2021-09-04', 72),
(11928, 30, 136, '2025-04-25 15:20:13', '2021-09-04', 72),
(11929, 30, 139, '2025-04-25 15:20:13', '2021-09-04', 72),
(11930, 30, 140, '2025-04-25 15:20:13', '2021-09-04', 72),
(11931, 30, 70, '2025-04-25 15:20:13', '2021-09-04', 72),
(11932, 30, 71, '2025-04-25 15:20:13', '2021-09-04', 72),
(11933, 30, 115, '2025-04-25 15:20:13', '2021-09-04', 72),
(11934, 30, 176, '2025-04-25 15:20:13', '2021-09-04', 72),
(11935, 30, 177, '2025-04-25 15:20:13', '2021-09-04', 72),
(11967, 41, 168, '2025-04-25 15:23:42', '2021-09-04', 72),
(11968, 41, 170, '2025-04-25 15:23:42', '2021-09-04', 72),
(11969, 41, 171, '2025-04-25 15:23:42', '2021-09-04', 72),
(11970, 41, 172, '2025-04-25 15:23:42', '2021-09-04', 72),
(11971, 41, 173, '2025-04-25 15:23:42', '2021-09-04', 72),
(11972, 41, 174, '2025-04-25 15:23:42', '2021-09-04', 72),
(11973, 41, 175, '2025-04-25 15:23:42', '2021-09-04', 72),
(11974, 41, 61, '2025-04-25 15:23:42', '2021-09-04', 72),
(11975, 41, 126, '2025-04-25 15:23:42', '2021-09-04', 72),
(11976, 41, 127, '2025-04-25 15:23:42', '2021-09-04', 72),
(11977, 41, 64, '2025-04-25 15:23:42', '2021-09-04', 72),
(11978, 41, 66, '2025-04-25 15:23:42', '2021-09-04', 72),
(11979, 41, 91, '2025-04-25 15:23:42', '2021-09-04', 72),
(11980, 41, 43, '2025-04-25 15:23:42', '2021-09-04', 72),
(11981, 41, 121, '2025-04-25 15:23:42', '2021-09-04', 72),
(11982, 41, 62, '2025-04-25 15:23:42', '2021-09-04', 72),
(11983, 41, 52, '2025-04-25 15:23:42', '2021-09-04', 72),
(11984, 41, 65, '2025-04-25 15:23:42', '2021-09-04', 72),
(11985, 41, 67, '2025-04-25 15:23:42', '2021-09-04', 72),
(11986, 41, 68, '2025-04-25 15:23:42', '2021-09-04', 72),
(11987, 41, 79, '2025-04-25 15:23:42', '2021-09-04', 72),
(11988, 41, 80, '2025-04-25 15:23:42', '2021-09-04', 72),
(11989, 41, 134, '2025-04-25 15:23:42', '2021-09-04', 72),
(11990, 41, 136, '2025-04-25 15:23:42', '2021-09-04', 72),
(11991, 41, 139, '2025-04-25 15:23:42', '2021-09-04', 72),
(11992, 41, 140, '2025-04-25 15:23:42', '2021-09-04', 72),
(11993, 41, 70, '2025-04-25 15:23:42', '2021-09-04', 72),
(11994, 41, 71, '2025-04-25 15:23:42', '2021-09-04', 72),
(11995, 41, 115, '2025-04-25 15:23:42', '2021-09-04', 72),
(11996, 41, 176, '2025-04-25 15:23:42', '2021-09-04', 72),
(11997, 41, 177, '2025-04-25 15:23:42', '2021-09-04', 72),
(12047, 27, 168, '2025-04-25 15:29:22', '2021-09-04', 72),
(12048, 27, 171, '2025-04-25 15:29:22', '2021-09-04', 72),
(12049, 27, 172, '2025-04-25 15:29:22', '2021-09-04', 72),
(12050, 27, 175, '2025-04-25 15:29:22', '2021-09-04', 72),
(12051, 27, 106, '2025-04-25 15:29:22', '2021-09-04', 72),
(12052, 27, 61, '2025-04-25 15:29:22', '2021-09-04', 72),
(12053, 27, 64, '2025-04-25 15:29:22', '2021-09-04', 72),
(12054, 27, 66, '2025-04-25 15:29:22', '2021-09-04', 72),
(12055, 27, 91, '2025-04-25 15:29:22', '2021-09-04', 72),
(12056, 27, 30, '2025-04-25 15:29:22', '2021-09-04', 72),
(12057, 27, 43, '2025-04-25 15:29:22', '2021-09-04', 72),
(12058, 27, 62, '2025-04-25 15:29:22', '2021-09-04', 72),
(12059, 27, 52, '2025-04-25 15:29:22', '2021-09-04', 72),
(12060, 27, 65, '2025-04-25 15:29:22', '2021-09-04', 72),
(12061, 27, 67, '2025-04-25 15:29:22', '2021-09-04', 72),
(12062, 27, 68, '2025-04-25 15:29:22', '2021-09-04', 72),
(12063, 27, 79, '2025-04-25 15:29:22', '2021-09-04', 72),
(12064, 27, 80, '2025-04-25 15:29:22', '2021-09-04', 72),
(12065, 27, 134, '2025-04-25 15:29:22', '2021-09-04', 72),
(12066, 27, 136, '2025-04-25 15:29:22', '2021-09-04', 72),
(12067, 27, 139, '2025-04-25 15:29:22', '2021-09-04', 72),
(12068, 27, 140, '2025-04-25 15:29:22', '2021-09-04', 72),
(12069, 27, 135, '2025-04-25 15:29:22', '2021-09-04', 72),
(12070, 27, 71, '2025-04-25 15:29:22', '2021-09-04', 72),
(12071, 27, 72, '2025-04-25 15:29:22', '2021-09-04', 72),
(12072, 26, 168, '2025-04-29 22:17:39', '2021-09-04', 1),
(12073, 26, 172, '2025-04-29 22:17:39', '2021-09-04', 1),
(12074, 26, 30, '2025-04-29 22:17:39', '2021-09-04', 1),
(12075, 26, 43, '2025-04-29 22:17:39', '2021-09-04', 1),
(12076, 26, 52, '2025-04-29 22:17:39', '2021-09-04', 1),
(12077, 26, 65, '2025-04-29 22:17:39', '2021-09-04', 1),
(12078, 26, 67, '2025-04-29 22:17:39', '2021-09-04', 1),
(12079, 26, 68, '2025-04-29 22:17:39', '2021-09-04', 1),
(12080, 26, 79, '2025-04-29 22:17:39', '2021-09-04', 1),
(12081, 26, 134, '2025-04-29 22:17:39', '2021-09-04', 1),
(12082, 26, 136, '2025-04-29 22:17:39', '2021-09-04', 1),
(12083, 26, 139, '2025-04-29 22:17:39', '2021-09-04', 1),
(12084, 26, 140, '2025-04-29 22:17:39', '2021-09-04', 1),
(12085, 26, 71, '2025-04-29 22:17:39', '2021-09-04', 1),
(12141, 1, 168, '2025-05-05 15:35:23', '2021-09-04', 72),
(12142, 1, 170, '2025-05-05 15:35:23', '2021-09-04', 72),
(12143, 1, 171, '2025-05-05 15:35:23', '2021-09-04', 72),
(12144, 1, 172, '2025-05-05 15:35:23', '2021-09-04', 72),
(12145, 1, 173, '2025-05-05 15:35:23', '2021-09-04', 72),
(12146, 1, 174, '2025-05-05 15:35:23', '2021-09-04', 72),
(12147, 1, 175, '2025-05-05 15:35:23', '2021-09-04', 72),
(12148, 1, 106, '2025-05-05 15:35:23', '2021-09-04', 72),
(12149, 1, 61, '2025-05-05 15:35:23', '2021-09-04', 72),
(12150, 1, 126, '2025-05-05 15:35:23', '2021-09-04', 72),
(12151, 1, 127, '2025-05-05 15:35:23', '2021-09-04', 72),
(12152, 1, 128, '2025-05-05 15:35:23', '2021-09-04', 72),
(12153, 1, 64, '2025-05-05 15:35:23', '2021-09-04', 72),
(12154, 1, 66, '2025-05-05 15:35:23', '2021-09-04', 72),
(12155, 1, 91, '2025-05-05 15:35:23', '2021-09-04', 72),
(12156, 1, 96, '2025-05-05 15:35:23', '2021-09-04', 72),
(12157, 1, 97, '2025-05-05 15:35:23', '2021-09-04', 72),
(12158, 1, 98, '2025-05-05 15:35:23', '2021-09-04', 72),
(12159, 1, 108, '2025-05-05 15:35:23', '2021-09-04', 72),
(12160, 1, 58, '2025-05-05 15:35:23', '2021-09-04', 72),
(12161, 1, 76, '2025-05-05 15:35:23', '2021-09-04', 72),
(12162, 1, 77, '2025-05-05 15:35:23', '2021-09-04', 72),
(12163, 1, 78, '2025-05-05 15:35:23', '2021-09-04', 72),
(12164, 1, 83, '2025-05-05 15:35:23', '2021-09-04', 72),
(12165, 1, 123, '2025-05-05 15:35:23', '2021-09-04', 72),
(12166, 1, 124, '2025-05-05 15:35:23', '2021-09-04', 72),
(12167, 1, 125, '2025-05-05 15:35:23', '2021-09-04', 72),
(12168, 1, 30, '2025-05-05 15:35:23', '2021-09-04', 72),
(12169, 1, 31, '2025-05-05 15:35:23', '2021-09-04', 72),
(12170, 1, 39, '2025-05-05 15:35:23', '2021-09-04', 72),
(12171, 1, 42, '2025-05-05 15:35:23', '2021-09-04', 72),
(12172, 1, 43, '2025-05-05 15:35:23', '2021-09-04', 72),
(12173, 1, 121, '2025-05-05 15:35:23', '2021-09-04', 72),
(12174, 1, 62, '2025-05-05 15:35:23', '2021-09-04', 72),
(12175, 1, 52, '2025-05-05 15:35:23', '2021-09-04', 72),
(12176, 1, 65, '2025-05-05 15:35:23', '2021-09-04', 72),
(12177, 1, 67, '2025-05-05 15:35:23', '2021-09-04', 72),
(12178, 1, 68, '2025-05-05 15:35:23', '2021-09-04', 72),
(12179, 1, 79, '2025-05-05 15:35:23', '2021-09-04', 72),
(12180, 1, 134, '2025-05-05 15:35:23', '2021-09-04', 72),
(12181, 1, 136, '2025-05-05 15:35:23', '2021-09-04', 72),
(12182, 1, 34, '2025-05-05 15:35:23', '2021-09-04', 72),
(12183, 1, 35, '2025-05-05 15:35:23', '2021-09-04', 72),
(12184, 1, 38, '2025-05-05 15:35:23', '2021-09-04', 72),
(12185, 1, 57, '2025-05-05 15:35:23', '2021-09-04', 72),
(12186, 1, 74, '2025-05-05 15:35:23', '2021-09-04', 72),
(12187, 1, 120, '2025-05-05 15:35:23', '2021-09-04', 72),
(12188, 1, 122, '2025-05-05 15:35:23', '2021-09-04', 72),
(12189, 1, 135, '2025-05-05 15:35:23', '2021-09-04', 72),
(12190, 1, 69, '2025-05-05 15:35:23', '2021-09-04', 72),
(12191, 1, 70, '2025-05-05 15:35:23', '2021-09-04', 72),
(12192, 1, 71, '2025-05-05 15:35:23', '2021-09-04', 72),
(12193, 1, 72, '2025-05-05 15:35:23', '2021-09-04', 72),
(12194, 1, 114, '2025-05-05 15:35:23', '2021-09-04', 72),
(12195, 1, 115, '2025-05-05 15:35:23', '2021-09-04', 72),
(12196, 1, 118, '2025-05-05 15:35:23', '2021-09-04', 72),
(12197, 1, 150, '2025-05-05 15:35:23', '2021-09-04', 72),
(12198, 1, 176, '2025-05-05 15:35:23', '2021-09-04', 72),
(12199, 1, 177, '2025-05-05 15:35:23', '2021-09-04', 72),
(12200, 46, 172, '2025-06-17 06:53:36', '2021-09-04', 1),
(12201, 46, 175, '2025-06-17 06:53:36', '2021-09-04', 1),
(12202, 46, 106, '2025-06-17 06:53:36', '2021-09-04', 1),
(12203, 46, 61, '2025-06-17 06:53:36', '2021-09-04', 1),
(12204, 46, 64, '2025-06-17 06:53:36', '2021-09-04', 1),
(12205, 46, 66, '2025-06-17 06:53:36', '2021-09-04', 1),
(12206, 46, 91, '2025-06-17 06:53:36', '2021-09-04', 1),
(12207, 46, 96, '2025-06-17 06:53:36', '2021-09-04', 1),
(12208, 46, 97, '2025-06-17 06:53:36', '2021-09-04', 1),
(12209, 46, 98, '2025-06-17 06:53:36', '2021-09-04', 1),
(12210, 46, 108, '2025-06-17 06:53:36', '2021-09-04', 1),
(12211, 46, 58, '2025-06-17 06:53:36', '2021-09-04', 1),
(12212, 46, 77, '2025-06-17 06:53:36', '2021-09-04', 1),
(12213, 46, 78, '2025-06-17 06:53:36', '2021-09-04', 1),
(12214, 46, 30, '2025-06-17 06:53:36', '2021-09-04', 1),
(12215, 46, 43, '2025-06-17 06:53:36', '2021-09-04', 1),
(12216, 46, 62, '2025-06-17 06:53:36', '2021-09-04', 1),
(12217, 46, 52, '2025-06-17 06:53:36', '2021-09-04', 1),
(12218, 46, 65, '2025-06-17 06:53:36', '2021-09-04', 1),
(12219, 46, 67, '2025-06-17 06:53:36', '2021-09-04', 1),
(12220, 46, 68, '2025-06-17 06:53:36', '2021-09-04', 1),
(12221, 46, 79, '2025-06-17 06:53:36', '2021-09-04', 1),
(12222, 46, 80, '2025-06-17 06:53:36', '2021-09-04', 1),
(12223, 46, 134, '2025-06-17 06:53:36', '2021-09-04', 1),
(12224, 46, 136, '2025-06-17 06:53:36', '2021-09-04', 1),
(12225, 46, 139, '2025-06-17 06:53:36', '2021-09-04', 1),
(12226, 46, 140, '2025-06-17 06:53:36', '2021-09-04', 1),
(12227, 46, 34, '2025-06-17 06:53:36', '2021-09-04', 1),
(12228, 46, 35, '2025-06-17 06:53:36', '2021-09-04', 1),
(12229, 46, 57, '2025-06-17 06:53:36', '2021-09-04', 1),
(12230, 46, 74, '2025-06-17 06:53:36', '2021-09-04', 1),
(12231, 46, 135, '2025-06-17 06:53:36', '2021-09-04', 1),
(12232, 46, 71, '2025-06-17 06:53:36', '2021-09-04', 1),
(12233, 46, 72, '2025-06-17 06:53:36', '2021-09-04', 1),
(12234, 46, 115, '2025-06-17 06:53:36', '2021-09-04', 1),
(12235, 46, 150, '2025-06-17 06:53:36', '2021-09-04', 1);

-- --------------------------------------------------------

--
-- Table structure for table `account`
--

CREATE TABLE `account` (
  `account_id` int(11) NOT NULL,
  `client_id` varchar(200) NOT NULL,
  `account_number` varchar(200) NOT NULL,
  `balance` decimal(18,2) NOT NULL,
  `account_type` int(11) NOT NULL,
  `account_type_product` int(11) DEFAULT NULL,
  `is_teller` enum('Yes','No') NOT NULL DEFAULT 'No',
  `is_income_account` enum('Yes','No') NOT NULL DEFAULT 'No',
  `is_expense` enum('Yes','No') NOT NULL DEFAULT 'No',
  `is_vault` enum('Yes','No') NOT NULL DEFAULT 'No',
  `capital_account` enum('Yes','No') NOT NULL DEFAULT 'No',
  `collection_account` enum('Yes','No') NOT NULL DEFAULT 'No',
  `account_status` enum('Deleted','Closed','Active','Pending') NOT NULL DEFAULT 'Pending',
  `added_by` varchar(200) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `account`
--

INSERT INTO `account` (`account_id`, `client_id`, `account_number`, `balance`, `account_type`, `account_type_product`, `is_teller`, `is_income_account`, `is_expense`, `is_vault`, `capital_account`, `collection_account`, `account_status`, `added_by`, `date_added`) VALUES
(1, '4', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'Yes', 'No', 'No', 'Active', '72', '2024-12-30 04:48:34'),
(2, '1', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2024-12-31 05:57:31'),
(3, '5', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2024-12-31 06:13:55'),
(4, '7', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-11 13:06:08'),
(5, '8', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-11 13:29:11'),
(6, '9', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-12 18:04:14'),
(7, '10', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-12 18:10:12'),
(8, '11', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-12 18:24:50'),
(9, '12', '**********', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2025-01-12 19:53:43'),
(10, '14', '3000106714', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2025-01-12 20:26:27'),
(11, '15', '3000111470', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2025-01-12 20:34:16'),
(12, '16', '3000122420', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-13 18:39:57'),
(13, '17', '300013743', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '1', '2025-01-15 21:48:25'),
(14, '18', '3000147614', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2025-01-17 08:13:34'),
(15, '19', '3000158379', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-19 17:27:02'),
(16, '20', '3000165107', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-19 17:53:04'),
(17, '21', '3000171861', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-19 18:41:07'),
(18, '22', '3000182822', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-20 17:04:50'),
(19, '23', '3000196333', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '1', '2025-01-21 19:43:55'),
(20, '24', '3000203173', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-26 22:21:07'),
(21, '25', '3000214028', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-27 02:56:00'),
(22, '26', '3000228535', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-01-27 03:18:36'),
(23, '27', '3000236673', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-01-27 03:25:17'),
(24, '28', '300024434', '0.00', 1, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '1', '2025-02-06 05:46:54'),
(25, '0', '300001', '0.00', 3, 0, 'Yes', 'No', 'No', 'No', 'No', 'No', 'Pending', '1', '2025-02-08 08:41:15'),
(27, '0', '300002', '5000.00', 3, 0, 'No', 'No', 'No', 'No', 'No', 'Yes', 'Active', '1', '2025-02-08 09:33:20'),
(40, '0', '300003', '0.00', 3, 0, 'Yes', 'No', 'No', 'No', 'No', 'No', 'Pending', '72', '2025-03-26 22:28:31'),
(63, '31', '300004', '-5000.00', 3, 31, 'Yes', 'No', 'No', 'No', 'No', 'No', 'Active', '72', '2025-05-05 15:37:46'),
(65, '2', 'INVODis0001-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-05-16 10:26:21'),
(66, '2', 'INVODis0002-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-05-21 08:32:21'),
(67, '12', 'INVODis0003-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '', '2025-05-21 10:22:18'),
(68, '2', 'INVODis0004-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-05-21 10:27:49'),
(69, '14', 'INVODis0005-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '', '2025-05-21 13:27:16'),
(70, '24', 'OrderFi0001-25', '0.00', 2, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-05-27 13:31:43'),
(71, '38', 'SP100001-25', '0.00', 2, 5, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-03 12:33:16'),
(72, '34', 'INVODis0006-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-04 07:25:56'),
(73, '31', 'INVODis0007-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-04 09:58:27'),
(74, '39', 'INVODis0008-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-04 10:25:22'),
(75, '43', 'INVODis0009-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '', '2025-06-09 09:21:11'),
(76, '43', 'INVODis00010-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Active', '', '2025-06-09 09:24:29'),
(77, '29', 'OrderFi0002-25', '0.00', 2, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-09 09:27:20'),
(78, '33', 'INVODis00011-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-09 09:53:24'),
(79, '48', 'INVODis00012-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-11 09:35:09'),
(80, '30', 'INVODis00013-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-11 14:17:39'),
(81, '1', 'INVODis00014-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-11 14:21:17'),
(82, '35', 'INVODis00015-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-16 15:20:44'),
(83, '15', 'INVODis00016-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-16 15:45:50'),
(84, '10', 'INVODis00017-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-16 16:08:39'),
(85, '21', 'INVODis00018-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-17 09:02:48'),
(86, '50', 'INVODis00019-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-17 09:37:37'),
(87, '50', 'INVODis00020-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-17 09:44:15'),
(88, '50', 'INVODis00021-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-17 11:22:43'),
(89, '33', 'INVODis00022-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 13:38:05'),
(90, '38', 'INVODis00023-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 13:40:09'),
(91, '31', 'INVODis00024-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 13:42:06'),
(92, '2', 'INVODis00025-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:16:46'),
(93, '33', 'INVODis00026-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:20:50'),
(94, '30', 'INVODis00027-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:27:19'),
(95, '2', 'INVODis00028-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:31:13'),
(96, '48', 'INVODis00029-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:33:13'),
(97, '31', 'INVODis00030-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:35:24'),
(98, '38', 'INVODis00031-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 14:39:36'),
(99, '2', 'INVODis00032-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 15:28:27'),
(100, '34', 'INVODis00033-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 15:32:27'),
(101, '2', 'INVODis00034-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-18 15:36:59'),
(102, '29', 'OrderFi0003-25', '0.00', 2, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:22:04'),
(103, '30', 'INVODis00035-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:30:01'),
(104, '30', 'INVODis00036-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:31:56'),
(105, '31', 'INVODis00037-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:34:08'),
(106, '33', 'INVODis00038-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:37:22'),
(107, '33', 'INVODis00039-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:43:42'),
(108, '29', 'OrderFi0004-25', '0.00', 2, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:47:49'),
(109, '2', 'INVODis00040-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:53:11'),
(110, '2', 'INVODis00041-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 09:55:18'),
(111, '33', 'INVODis00042-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 10:19:38'),
(112, '53', 'INVODis00043-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 11:26:03'),
(113, '54', 'INVODis00044-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 11:51:47'),
(114, '54', 'INVODis00045-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 12:03:50'),
(115, '53', 'INVODis00046-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 12:09:47'),
(116, '53', 'INVODis00047-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 12:43:23'),
(117, '53', 'INVODis00048-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 12:46:33'),
(118, '55', 'INVODis00049-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 14:25:59'),
(119, '55', 'INVODis00050-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-19 14:28:14'),
(120, '29', 'INVODis00051-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-20 08:04:42'),
(121, '2', 'INVODis00052-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-20 13:54:21'),
(122, '10', 'INVODis00053-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-23 08:38:25'),
(123, '56', 'INVODis00054-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-06-27 09:54:35'),
(124, '29', 'OrderFi0005-25', '0.00', 2, 2, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-01 14:40:51'),
(125, '57', 'INVODis00055-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-03 08:58:01'),
(126, '30', 'INVODis00056-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-03 09:49:13'),
(127, '14', 'INVODis00057-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-03 10:31:53'),
(128, '1', 'INVODis00058-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-11 15:52:39'),
(129, '1', 'INVODis00059-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-11 16:06:26'),
(130, '1', 'INVODis00060-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-11 16:08:20'),
(131, '1', 'INVODis00061-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-15 13:41:56'),
(132, '12', 'INVODis00062-25', '0.00', 2, 1, 'No', 'No', 'No', 'No', 'No', 'No', 'Pending', '', '2025-07-16 10:26:29');

-- --------------------------------------------------------

--
-- Table structure for table `account_types`
--

CREATE TABLE `account_types` (
  `account_type_id` int(11) NOT NULL,
  `account_type_name` varchar(200) NOT NULL,
  `added_by` varchar(200) NOT NULL,
  `type` enum('system_generated','user_generated') NOT NULL DEFAULT 'user_generated',
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `account_types`
--

INSERT INTO `account_types` (`account_type_id`, `account_type_name`, `added_by`, `type`, `date_added`) VALUES
(1, 'Savings account', '1', 'system_generated', '2022-04-01 12:37:13'),
(2, 'Loan accounts', '1', 'system_generated', '2022-04-01 12:37:13'),
(3, 'Internal accounts', '1', 'system_generated', '2022-04-01 12:37:13'),
(4, 'Students Account', '7', 'user_generated', '2022-05-24 16:21:25');

-- --------------------------------------------------------

--
-- Table structure for table `activity_logger`
--

CREATE TABLE `activity_logger` (
  `id` int(11) NOT NULL,
  `user_id` varchar(200) NOT NULL,
  `activity` text NOT NULL,
  `activity_cate` varchar(50) NOT NULL,
  `old_data` text NOT NULL,
  `new_data` text NOT NULL,
  `system_time` datetime NOT NULL,
  `server_time` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `activity_logger`
--

INSERT INTO `activity_logger` (`id`, `user_id`, `activity`, `activity_cate`, `old_data`, `new_data`, `system_time`, `server_time`) VALUES
(1, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-13 17:49:06'),
(2, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-13 17:49:19'),
(3, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-13 17:50:37'),
(4, '7', 'Create employeeDaka Chipasha', 'adding', '', '', '0000-00-00 00:00:00', '2024-12-13 17:53:01'),
(5, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-14 10:44:54'),
(6, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-14 10:46:30'),
(7, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-14 10:48:43'),
(8, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:11:56'),
(9, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:12:27'),
(10, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:14:40'),
(11, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:16:35'),
(12, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:17:08'),
(13, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:18:00'),
(14, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-15 15:18:22'),
(15, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:15:55'),
(16, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:16:33'),
(17, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:18:36'),
(18, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:20:06'),
(19, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:30:41'),
(20, '0', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:33:15'),
(21, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 08:58:49'),
(22, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 09:12:00'),
(23, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 20:27:55'),
(24, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-16 20:35:38'),
(25, '7', 'Update system settings eg company logo, address, phone number, email etc', '', '', '', '0000-00-00 00:00:00', '2024-12-16 20:53:32'),
(26, '7', 'Update system settings eg company logo, address, phone number, email etc', '', '', '', '0000-00-00 00:00:00', '2024-12-16 20:54:26'),
(27, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-19 20:22:11'),
(28, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-20 09:46:29'),
(29, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-20 17:02:44'),
(30, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-20 17:15:45'),
(31, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-20 17:24:09'),
(32, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-21 13:23:31'),
(33, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-21 13:28:45'),
(34, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-21 14:43:51'),
(35, '7', 'Register a customerClara banda', 'customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-21 14:54:56'),
(36, '7', 'customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2024-12-21 15:00:58'),
(37, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-21 15:07:47'),
(38, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-23 14:20:41'),
(39, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-23 14:21:21'),
(40, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-24 19:51:17'),
(41, '7', 'logged in the system', 'logging', '', '', '0000-00-00 00:00:00', '2024-12-24 20:32:34'),
(42, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-24 23:08:24'),
(43, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 00:14:46'),
(44, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 00:30:28'),
(45, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 00:56:19'),
(46, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 01:10:08'),
(47, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 04:51:46'),
(48, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:05:30'),
(49, '72', 'Create employeeChapasasha daka', 'adding', '', '', '0000-00-00 00:00:00', '2024-12-25 05:17:34'),
(50, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:32:14'),
(51, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:37:27'),
(52, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:44:09'),
(53, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:45:47'),
(54, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:46:17'),
(55, '0', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:48:41'),
(56, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 05:52:02'),
(57, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 18:29:35'),
(58, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 18:37:20'),
(59, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 18:40:00'),
(60, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-25 18:47:22'),
(61, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 08:18:27'),
(62, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 08:29:53'),
(63, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 08:40:12'),
(64, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 08:59:42'),
(65, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 10:04:13'),
(66, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 10:40:01'),
(67, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 10:44:21'),
(68, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 10:56:42'),
(69, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 12:15:34'),
(70, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 12:19:37'),
(71, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 12:34:11'),
(72, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 12:41:19'),
(73, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 12:57:47'),
(74, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 13:15:01'),
(75, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 13:57:32'),
(76, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 14:01:20'),
(77, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-26 14:16:10'),
(78, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-27 05:17:33'),
(79, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-27 14:09:45'),
(80, '1', 'Register a customerCustomer #1 SME', 'customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-27 14:25:08'),
(81, '1', 'customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2024-12-27 14:27:00'),
(82, '1', 'Create employeePhilip Bwembya', 'adding', '', '', '0000-00-00 00:00:00', '2024-12-27 15:03:18'),
(83, '1', 'Create employeePaul Kalubale', 'adding', '', '', '0000-00-00 00:00:00', '2024-12-27 15:05:34'),
(84, '1', 'Create employeeMutinta Lunda', 'adding', '', '', '0000-00-00 00:00:00', '2024-12-27 15:08:33'),
(85, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-27 16:40:48'),
(86, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 14:58:47'),
(87, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 15:06:53'),
(88, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 15:13:17'),
(89, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 15:19:30'),
(90, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 15:29:49'),
(91, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 16:02:19'),
(92, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 21:48:00'),
(93, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 22:37:23'),
(94, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 22:51:42'),
(95, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-28 23:04:59'),
(96, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 00:25:18'),
(97, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 00:44:29'),
(98, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 01:00:39'),
(99, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 01:19:00'),
(100, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 01:27:08'),
(101, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 01:45:06'),
(102, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2024-12-29 01:45:53'),
(103, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 01:46:19'),
(104, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 14:59:31'),
(105, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 16:12:47'),
(106, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 16:18:20'),
(107, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 16:37:10'),
(108, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-29 22:08:52'),
(109, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 03:38:01'),
(110, '72', 'Register corporate  a customerSacco', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-30 04:43:27'),
(111, '72', 'Register corporate  a customerSacco', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-30 04:45:52'),
(112, '72', 'Register corporate  a customerSacco', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-30 04:48:34'),
(113, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 05:29:43'),
(114, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 06:11:26'),
(115, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 06:15:58'),
(116, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 06:43:21'),
(117, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2024-12-30 06:43:43'),
(118, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 06:56:22'),
(119, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-30 07:01:17'),
(120, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-31 05:45:11'),
(121, '1', 'Register a customerCustomer #1 SME', 'customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-31 05:57:31'),
(122, '1', 'customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2024-12-31 05:57:50'),
(123, '1', 'Register corporate  a customerMopani', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2024-12-31 06:13:55'),
(124, '1', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2024-12-31 06:15:42'),
(125, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-31 13:38:23'),
(126, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2024-12-31 14:12:12'),
(127, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-04 09:34:42'),
(128, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-04 12:50:56'),
(129, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-04 13:12:14'),
(130, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-04 14:13:06'),
(131, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-07 11:46:58'),
(132, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-07 11:56:50'),
(133, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-08 06:52:33'),
(134, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-08 14:51:59'),
(135, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-08 15:05:14'),
(136, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-08 15:36:22'),
(137, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-08 15:45:46'),
(138, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 09:08:57'),
(139, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 09:16:25'),
(140, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 09:40:04'),
(141, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 10:21:37'),
(142, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 11:55:30'),
(143, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 12:02:30'),
(144, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 12:12:02'),
(145, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 12:20:09'),
(146, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 12:45:55'),
(147, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 12:56:08'),
(148, '72', 'Register corporate  a customerSacco', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-11 13:06:08'),
(149, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 13:14:54'),
(150, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 13:25:11'),
(151, '72', 'Register corporate  a customerMorewide', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-11 13:29:11'),
(152, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 16:24:41'),
(153, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 16:55:07'),
(154, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 16:55:54'),
(155, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 16:56:03'),
(156, '72', 'Shareholder approved', 'shareholder_approval', '', '', '0000-00-00 00:00:00', '2025-01-11 17:01:22'),
(157, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 17:06:44'),
(158, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 17:09:13'),
(159, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-11 17:10:32'),
(160, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 17:10:57'),
(161, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 17:11:46'),
(162, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 17:50:38'),
(163, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 18:09:44'),
(164, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 18:21:18'),
(165, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 18:25:13'),
(166, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 19:11:32'),
(167, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 22:00:35'),
(168, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-11 22:17:10'),
(169, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 14:24:13'),
(170, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 15:03:35'),
(171, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 15:10:23'),
(172, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 15:16:19'),
(173, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 15:38:28'),
(174, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 16:58:43'),
(175, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 18:00:08'),
(176, '72', 'Register corporate  a customerMorewide electronics', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 18:04:14'),
(177, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 18:07:43'),
(178, '72', 'Register corporate  a customerMorewide electronics ss', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 18:10:12'),
(179, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 18:22:48'),
(180, '72', 'Register corporate  a customerMorewide electronics sss', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 18:24:50'),
(181, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 19:48:14'),
(182, '1', 'Register corporate  a customerTurnip Investments Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 19:53:43'),
(183, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-12 20:21:11'),
(184, '1', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-12 20:22:01'),
(185, '1', 'Register corporate  a customerTurnip Enterprises Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 20:26:27'),
(186, '1', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-12 20:29:31'),
(187, '1', 'Register corporate  a customerNME Group Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-12 20:34:16'),
(188, '1', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-12 20:35:29'),
(189, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-13 08:54:01'),
(190, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-13 12:34:09'),
(191, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-13 16:44:49'),
(192, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-13 18:09:02'),
(193, '72', 'Register corporate customer Morewide electronics sss', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-13 18:39:57'),
(194, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-13 20:52:23'),
(195, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 07:37:29'),
(196, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 07:49:35'),
(197, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 08:45:16'),
(198, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 08:52:45'),
(199, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 10:01:49'),
(200, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 11:40:24'),
(201, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 11:45:16'),
(202, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 12:29:59'),
(203, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 12:33:41'),
(204, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 12:45:42'),
(205, '72', 'Updated corporate customer SACCO1', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-14 12:47:01'),
(206, '72', 'Updated corporate customer SACCO1', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-14 12:47:41'),
(207, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 13:05:56'),
(208, '72', 'Updated corporate customer SACCO1', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-14 13:06:44'),
(209, '72', 'Updated corporate customer Morewide electronics', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-14 13:08:47'),
(210, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 13:18:43'),
(211, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 16:00:53'),
(212, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-14 21:59:47'),
(213, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 04:00:37'),
(214, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 21:08:32'),
(215, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 21:16:06'),
(216, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 21:20:33'),
(217, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 21:24:57'),
(218, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-15 21:28:51'),
(219, '1', 'Register corporate customer Turnip Logistics Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-15 21:48:25'),
(220, '1', 'Updated corporate customer Turnip Logistics Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-15 21:49:24'),
(221, '1', 'Updated corporate customer Turnip Logistics Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-15 21:50:10'),
(222, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-16 20:33:56'),
(223, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-17 07:59:26'),
(224, '1', 'Register corporate customer NME Zambia Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-17 08:13:34'),
(225, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 13:30:03'),
(226, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 13:42:12'),
(227, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 14:47:40'),
(228, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 15:18:04'),
(229, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 15:25:54'),
(230, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 17:12:44'),
(231, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 17:21:45'),
(232, '72', 'Register corporate customer Morewide electronics  Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-19 17:27:02'),
(233, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 17:45:10'),
(234, '72', 'Register corporate customer Morewide electronics  Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-19 17:53:04'),
(235, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 18:09:54'),
(236, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-19 18:20:14'),
(237, '72', 'Register corporate customer Morewide electronics  Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-19 18:41:07'),
(238, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 08:57:34'),
(239, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 09:12:00'),
(240, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 09:22:49'),
(241, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-20 09:23:51'),
(242, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-20 09:33:43'),
(243, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 16:03:20'),
(244, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 16:09:25'),
(245, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 16:58:25'),
(246, '72', 'Register corporate customer Sacco', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-20 17:04:50'),
(247, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-20 17:05:09'),
(248, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-20 17:05:13'),
(249, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 17:27:51'),
(250, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-20 17:32:21'),
(251, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-21 19:34:34'),
(252, '1', 'Register corporate customer Turnip Enterprises Ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-21 19:43:55'),
(253, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-21 20:11:49'),
(254, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-21 20:12:31'),
(255, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-21 20:17:03'),
(256, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-21 20:20:08'),
(257, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-01-21 20:21:05'),
(258, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-23 09:33:22'),
(259, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-23 10:22:25'),
(260, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-23 14:16:05'),
(261, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-26 20:10:37'),
(262, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-26 21:54:52'),
(263, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-26 22:13:55'),
(264, '72', 'Register corporate customer Infocus', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-26 22:21:07'),
(265, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 02:48:36'),
(266, '72', 'Register corporate customer Infocus tech', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-27 02:56:00'),
(267, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 03:14:57'),
(268, '72', 'Register corporate customer Infocus tech ltd', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-27 03:18:36'),
(269, '72', 'Register corporate customer Infocus tech ltds', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-01-27 03:25:17'),
(270, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 03:33:04'),
(271, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 04:33:56'),
(272, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 04:33:56'),
(273, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 05:23:53'),
(274, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 05:28:23'),
(275, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 05:41:39'),
(276, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 06:04:01'),
(277, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-01-27 06:07:40'),
(278, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 06:29:10'),
(279, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 06:48:46'),
(280, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 07:10:18'),
(281, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 07:12:31'),
(282, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 07:14:34'),
(283, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 07:17:02'),
(284, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-01-27 07:24:50'),
(285, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 07:34:24'),
(286, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 12:50:46'),
(287, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 13:06:05'),
(288, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 13:11:25'),
(289, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 13:22:19'),
(290, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 18:54:13'),
(291, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 19:04:28'),
(292, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 19:04:46'),
(293, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-01-27 19:04:52'),
(294, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-01-27 19:08:51'),
(295, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 19:12:35'),
(296, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-01-27 19:21:49'),
(297, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-27 19:40:39'),
(298, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-31 17:42:58'),
(299, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-31 18:06:00'),
(300, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-31 18:12:59'),
(301, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-01-31 18:18:17'),
(302, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 20:37:30'),
(303, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 20:44:06'),
(304, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 20:47:34'),
(305, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 21:20:33'),
(306, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 22:42:48'),
(307, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 23:35:30'),
(308, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-01 23:45:28'),
(309, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-02 16:22:08'),
(310, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-02 16:36:15'),
(311, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-02 17:25:24'),
(312, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-02 17:43:45'),
(313, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-02 20:05:31'),
(314, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-03 22:17:21'),
(315, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 10:39:34'),
(316, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 11:44:16'),
(317, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 12:06:36'),
(318, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 12:26:29'),
(319, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 12:34:26'),
(320, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 12:40:55'),
(321, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-04 13:30:21'),
(322, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-06 05:43:21'),
(323, '1', 'Register corporate customer MCM Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-02-06 05:46:54'),
(324, '1', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-02-06 05:48:08'),
(325, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-06 05:51:36'),
(326, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-06 05:53:04'),
(327, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-02-06 05:55:16'),
(328, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-06 17:22:55'),
(329, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-06 22:28:12'),
(330, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-06 22:47:21'),
(331, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 15:58:48'),
(332, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 16:14:11'),
(333, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 16:25:31'),
(334, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 16:41:32'),
(335, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 17:16:04'),
(336, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 17:40:25'),
(337, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 17:53:19'),
(338, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 17:59:55'),
(339, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 18:07:08'),
(340, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 18:16:13'),
(341, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 18:38:16'),
(342, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 19:27:45'),
(343, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 19:39:28'),
(344, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 20:10:38'),
(345, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-07 20:20:34'),
(346, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 07:04:48'),
(347, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 08:23:39'),
(348, '1', 'Paid a loan, loan ID:19', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 09:35:25'),
(349, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-08 09:36:19'),
(350, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-08 09:36:28'),
(351, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-02-08 09:36:49'),
(352, '1', 'Paid a loan, loan ID: 19  payment number 2 amount 2000', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 09:37:38'),
(353, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 09:42:35'),
(354, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 09:46:59'),
(355, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 09:59:21'),
(356, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:02:03'),
(357, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:05:59'),
(358, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:14:42'),
(359, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:48:03'),
(360, '1', 'Paid a loan, loan ID: 19  payment number 3 amount 1060.00', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 10:48:30'),
(361, '1', 'Paid a loan, loan ID: 19  payment number 4 amount 1060.00', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 10:49:14'),
(362, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:49:19'),
(363, '1', 'Paid a loan, loan ID:19', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 10:50:54'),
(364, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 10:53:22'),
(365, '1', 'Paid a loan, loan ID:19', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-08 11:09:11'),
(366, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 11:18:44'),
(367, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 16:57:24'),
(368, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 17:13:44'),
(369, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 17:24:35'),
(370, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 17:24:50'),
(371, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 17:38:49'),
(372, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-08 18:17:32'),
(373, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-09 04:11:44'),
(374, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-09 04:22:28'),
(375, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-09 04:28:11'),
(376, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-09 04:46:49'),
(377, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-09 05:10:15'),
(378, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-09 05:13:10'),
(379, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-02-09 05:14:50'),
(380, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-13 01:51:09'),
(381, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-13 02:13:41'),
(382, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-13 02:26:25'),
(383, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-13 02:30:00'),
(384, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-13 02:31:52'),
(385, '1', 'Paid a loan, loan ID:14', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-13 02:38:00'),
(386, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-13 02:44:45'),
(387, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 02:19:56'),
(388, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 02:25:05'),
(389, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 02:35:37'),
(390, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 02:49:22'),
(391, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 04:10:59'),
(392, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 04:30:14'),
(393, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 04:33:38'),
(394, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 04:43:41'),
(395, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 05:05:55'),
(396, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 16:17:34'),
(397, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-14 16:24:59'),
(398, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-14 16:25:06'),
(399, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 17:02:32'),
(400, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-14 17:15:22'),
(401, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-17 11:24:56'),
(402, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-17 11:35:58'),
(403, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-17 13:36:37'),
(404, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-17 14:06:45'),
(405, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-17 14:10:15'),
(406, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-17 14:10:24'),
(407, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-02-17 14:11:19'),
(408, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-17 14:32:11'),
(409, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-22 07:58:07'),
(410, '1', 'Paid a loan, loan ID:16', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-22 08:07:10'),
(411, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-22 08:15:41'),
(412, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-22 08:45:56'),
(413, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-26 17:26:27'),
(414, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 04:58:36'),
(415, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 04:58:55'),
(416, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 09:51:40'),
(417, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 19:50:20'),
(418, '1', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-28 20:02:17'),
(419, '1', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-02-28 20:02:24'),
(420, '1', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-02-28 20:06:06'),
(421, '1', 'Paid a loan, loan ID:25', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-28 20:07:21'),
(422, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 20:23:29'),
(423, '1', 'Paid a loan, loan ID:24', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-28 20:32:05'),
(424, '1', 'Paid a loan, loan ID:24', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-02-28 20:37:29'),
(425, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-02-28 21:15:47'),
(426, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 09:35:03'),
(427, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 10:23:37'),
(428, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 10:31:35'),
(429, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 10:44:21'),
(430, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 10:55:11'),
(431, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 11:08:30'),
(432, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 12:14:59'),
(433, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 13:09:26'),
(434, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 13:39:15'),
(435, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:09:37'),
(436, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-02 14:14:17'),
(437, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-02 14:14:33'),
(438, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:26:45'),
(439, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:40:29'),
(440, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:45:27'),
(441, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:48:31'),
(442, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:51:02'),
(443, '1', 'Paid a loan, loan ID:25', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-02 14:53:07'),
(444, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:53:10'),
(445, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 14:55:55'),
(446, '1', 'Paid a loan, loan ID:15', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-02 15:01:14'),
(447, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-02 15:01:17'),
(448, '1', 'Paid a loan, loan ID:25', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-02 15:39:09'),
(449, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-05 16:39:30'),
(450, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-05 17:02:37'),
(451, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 14:31:07'),
(452, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 14:35:09'),
(453, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 17:48:50'),
(454, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:06:12'),
(455, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 19:14:40'),
(456, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 19:14:47'),
(457, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-26 19:15:07'),
(458, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:21:54'),
(459, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:25:47'),
(460, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:34:55'),
(461, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:39:13'),
(462, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:46:20'),
(463, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 19:59:03'),
(464, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:05:11'),
(465, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:11:14'),
(466, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:30:36'),
(467, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 20:32:42'),
(468, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 20:32:50'),
(469, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-26 20:33:01'),
(470, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:38:56'),
(471, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:44:24'),
(472, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:50:33'),
(473, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 20:59:13'),
(474, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 21:03:49'),
(475, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 21:12:30'),
(476, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 21:25:42'),
(477, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 21:34:20'),
(478, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 21:43:24'),
(479, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 21:44:30'),
(480, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 21:44:38'),
(481, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-26 21:44:49'),
(482, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 22:03:09'),
(483, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 22:04:03'),
(484, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 22:04:09'),
(485, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-26 22:04:26'),
(486, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 22:08:29'),
(487, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 22:14:59'),
(488, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 22:15:11'),
(489, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-26 22:15:17'),
(490, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-26 22:22:11'),
(491, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-26 22:22:20'),
(492, '72', 'Paid a loan, loan ID: 32 payment number: 1 amount: 11000.00', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:22:15'),
(493, '72', 'Paid a loan, loan ID: 31 payment number: 1 amount: 11000.00', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:24:10'),
(494, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-27 00:28:18'),
(495, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-27 00:29:11'),
(496, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-27 00:29:17'),
(497, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-27 00:29:25'),
(498, '72', 'Paid a loan, loan ID:33', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:36:04'),
(499, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-03-27 00:39:47'),
(500, '72', 'Paid a loan, loan ID:30', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:43:39'),
(501, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-27 00:44:45'),
(502, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-27 00:44:51');
INSERT INTO `activity_logger` (`id`, `user_id`, `activity`, `activity_cate`, `old_data`, `new_data`, `system_time`, `server_time`) VALUES
(503, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-03-27 00:44:55'),
(504, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-27 00:45:07'),
(505, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-03-27 00:45:13'),
(506, '72', 'Paid a loan, loan ID:34', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:46:36'),
(507, '72', 'Paid a loan, loan ID:34', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-03-27 00:54:06'),
(508, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 09:09:40'),
(509, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-03 09:14:16'),
(510, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-03 09:14:22'),
(511, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-04-03 09:14:34'),
(512, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 09:33:49'),
(513, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 09:42:51'),
(514, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 09:49:00'),
(515, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 09:54:15'),
(516, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 10:31:11'),
(517, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 10:43:30'),
(518, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 15:50:58'),
(519, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 15:58:32'),
(520, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 16:21:41'),
(521, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 16:26:10'),
(522, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 16:28:51'),
(523, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 16:40:44'),
(524, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 16:43:12'),
(525, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 18:12:22'),
(526, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 19:05:15'),
(527, '72', 'Paid a loan, loan ID:35', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-04-03 19:12:29'),
(528, '72', 'Paid a loan, loan ID:35', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-04-03 19:13:39'),
(529, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 19:16:53'),
(530, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 21:05:54'),
(531, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 21:21:06'),
(532, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 21:27:55'),
(533, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 21:36:39'),
(534, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:01:27'),
(535, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:05:23'),
(536, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:08:12'),
(537, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-03 22:34:26'),
(538, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-03 22:34:33'),
(539, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-04-03 22:34:40'),
(540, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:47:16'),
(541, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:53:44'),
(542, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 22:58:05'),
(543, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 23:02:14'),
(544, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 23:25:36'),
(545, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 23:41:48'),
(546, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 23:45:49'),
(547, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-03 23:53:04'),
(548, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-04 00:00:26'),
(549, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-04 00:02:40'),
(550, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-04 00:08:20'),
(551, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-04 00:24:11'),
(552, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-04 00:41:29'),
(553, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 10:11:25'),
(554, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 10:16:54'),
(555, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:17:05'),
(556, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:17:12'),
(557, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:17:18'),
(558, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:17:24'),
(559, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:17:30'),
(560, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:18:09'),
(561, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:19:06'),
(562, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:19:21'),
(563, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:19:23'),
(564, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:23:24'),
(565, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:25:58'),
(566, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-05 21:26:12'),
(567, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-05 21:43:29'),
(568, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-05 21:43:35'),
(569, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-04-05 21:43:44'),
(570, '72', 'Paid a loan, loan ID:37', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-04-05 21:49:12'),
(571, '72', 'Paid a loan, loan ID:37', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-04-05 21:56:19'),
(572, '72', 'Paid a loan, loan ID:37', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-04-05 21:57:50'),
(573, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:02:35'),
(574, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:14:52'),
(575, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:23:58'),
(576, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:32:12'),
(577, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:39:41'),
(578, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:46:06'),
(579, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 11:57:55'),
(580, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:10:42'),
(581, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:23:19'),
(582, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:32:19'),
(583, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:47:20'),
(584, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:49:27'),
(585, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 12:58:23'),
(586, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 13:04:07'),
(587, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 13:59:57'),
(588, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 14:32:32'),
(589, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 14:40:53'),
(590, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 14:49:41'),
(591, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 15:09:26'),
(592, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 15:27:04'),
(593, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 15:30:52'),
(594, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 16:00:15'),
(595, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-09 20:53:49'),
(596, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-10 08:32:52'),
(597, '72', 'Create employeeMisheck Kamuloni', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-10 08:34:46'),
(598, '141', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-10 08:37:08'),
(599, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-10 09:09:13'),
(600, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-11 20:46:13'),
(601, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-19 15:06:25'),
(602, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 09:09:26'),
(603, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 09:35:37'),
(604, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 11:05:56'),
(605, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 11:07:10'),
(606, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 11:21:05'),
(607, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 16:43:08'),
(608, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-20 16:51:48'),
(609, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-25 10:59:45'),
(610, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-25 11:27:30'),
(611, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-25 12:53:46'),
(612, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-25 13:43:36'),
(613, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:23:09'),
(614, '72', 'Delete role name', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:38:49'),
(615, '72', 'Delete role name', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:38:58'),
(616, '72', 'Create role  Chief Services Officer', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:41:03'),
(617, '72', 'Create role  Credit Manager', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:43:02'),
(618, '72', 'Update role name to: Relationship Manager', '', '', '', '0000-00-00 00:00:00', '2025-04-25 14:44:31'),
(619, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 14:52:03'),
(620, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:00:17'),
(621, '72', 'Create role  Credit Committee', '', '', '', '0000-00-00 00:00:00', '2025-04-25 15:02:57'),
(622, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:10:58'),
(623, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:14:15'),
(624, '72', 'Create role  Chief Strategy Officer', '', '', '', '0000-00-00 00:00:00', '2025-04-25 15:15:03'),
(625, '72', 'Create role  Chief Executive Officer', '', '', '', '0000-00-00 00:00:00', '2025-04-25 15:15:21'),
(626, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:17:30'),
(627, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:19:31'),
(628, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:19:42'),
(629, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:20:13'),
(630, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:21:00'),
(631, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:23:42'),
(632, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:25:29'),
(633, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:25:59'),
(634, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-25 15:29:22'),
(635, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-26 19:51:42'),
(636, '1', 'Create employeePhilip Bwembya', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-26 19:58:38'),
(637, '1', 'Create employeePaul Kalubale', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-26 20:01:31'),
(638, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-29 08:33:01'),
(639, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-29 08:49:33'),
(640, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-29 21:42:48'),
(641, '1', 'Update Employee info of Paul Kalubale', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-29 21:52:36'),
(642, '1', 'Create employeeMutinta Lunda', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-29 22:01:57'),
(643, '1', 'Create employeeLubumbe Matani', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-29 22:05:18'),
(644, '1', 'Create employeeCurtis Banda', 'adding', '', '', '0000-00-00 00:00:00', '2025-04-29 22:12:46'),
(645, '1', 'Update role name to: Admin Officer', '', '', '', '0000-00-00 00:00:00', '2025-04-29 22:13:28'),
(646, '1', 'Update role name to: Admin Officer', '', '', '', '0000-00-00 00:00:00', '2025-04-29 22:13:55'),
(647, '1', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-29 22:17:39'),
(648, '1', 'Update Employee info of Curtis Banda', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-29 22:18:09'),
(649, '1', 'Update Employee info of Curtis Banda', 'updating', '', '', '0000-00-00 00:00:00', '2025-04-29 22:20:06'),
(650, '142', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-04-30 14:59:44'),
(651, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-05 13:55:06'),
(652, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-05 14:40:38'),
(653, '72', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-05 14:40:42'),
(654, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:05:18'),
(655, '72', 'APPROVED_FIRST  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:05:39'),
(656, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:06:31'),
(657, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-05 15:06:42'),
(658, '72', 'APPROVED_SECOND  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:14:19'),
(659, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:14:35'),
(660, '72', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-05-05 15:19:07'),
(661, '72', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-05 15:35:23'),
(662, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-05 15:35:32'),
(663, '72', 'Paid a loan, loan ID:1', 'loan_repayment', '', '', '0000-00-00 00:00:00', '2025-05-05 15:39:16'),
(664, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-08 09:12:41'),
(665, '72', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-08 11:44:15'),
(666, '72', 'APPROVED_FIRST  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-08 11:44:30'),
(667, '72', 'APPROVED_SECOND  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-08 11:44:38'),
(668, '72', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-08 11:44:44'),
(669, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:19:42'),
(670, '144', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:20:18'),
(671, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:23:39'),
(672, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:28:41'),
(673, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:28:50'),
(674, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:30:25'),
(675, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:34:20'),
(676, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 08:41:04'),
(677, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-13 12:43:16'),
(678, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-14 11:59:28'),
(679, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-14 16:59:05'),
(680, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-15 13:20:53'),
(681, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-15 13:33:03'),
(682, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-16 10:01:21'),
(683, '139', 'Updated corporate customer Perez Investments Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-16 10:03:26'),
(684, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-16 10:08:23'),
(685, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-19 14:22:21'),
(686, '146', 'Updated corporate customer KENPORT LOGISTICS', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-19 14:25:25'),
(687, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-20 08:05:36'),
(688, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-20 13:23:12'),
(689, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-20 14:15:09'),
(690, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-21 08:03:08'),
(691, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-21 08:28:29'),
(692, '146', 'Updated corporate customer QUORN FLOORING LIMITED', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-21 08:30:01'),
(693, '146', 'Updated corporate customer QUORN FLOORING LIMITED', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-21 10:35:44'),
(694, '146', 'Updated corporate customer QUORN FLOORING LIMITED', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-21 10:36:22'),
(695, '146', 'Updated corporate customer QUORN FLOORING LIMITED', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-21 10:37:03'),
(696, '146', 'Updated corporate customer QUORN FLOORING LIMITED', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-21 10:38:10'),
(697, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-21 10:46:47'),
(698, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-21 10:47:24'),
(699, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-21 13:02:12'),
(700, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-21 13:37:01'),
(701, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-22 09:07:16'),
(702, '139', 'Updated corporate customer KENPORT LOGISTICS', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-22 10:09:15'),
(703, '139', 'Updated corporate customer KENPORT LOGISTICS', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-22 10:10:42'),
(704, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-22 10:37:01'),
(705, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-22 10:41:57'),
(706, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-22 10:45:06'),
(707, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-22 15:03:30'),
(708, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-23 08:04:36'),
(709, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-23 08:25:10'),
(710, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-26 15:50:35'),
(711, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 09:03:49'),
(712, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-27 09:08:31'),
(713, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-27 09:08:41'),
(714, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 09:34:28'),
(715, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 09:34:28'),
(716, '139', 'Updated corporate customer RGPM Chemicals Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 09:50:06'),
(717, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-27 09:50:28'),
(718, '146', 'Updated corporate customer Quorn Flooring Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 09:51:21'),
(719, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-27 09:52:05'),
(720, '146', 'Updated corporate customer Wheals Construction Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 09:52:34'),
(721, '146', 'Updated corporate customer Kenport Logistics', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 09:53:50'),
(722, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-27 10:14:31'),
(723, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 11:37:33'),
(724, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 11:49:10'),
(725, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-27 13:03:36'),
(726, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-05-27 13:11:18'),
(727, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-05-27 13:33:24'),
(728, '146', 'Updated corporate customer Lumwana Copper Mines', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 14:09:22'),
(729, '146', 'Updated corporate customer Lumwana Copper Mines', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-27 14:19:29'),
(730, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-28 08:21:58'),
(731, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-28 09:34:21'),
(732, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-28 14:43:39'),
(733, '146', 'Updated corporate customer Fox Foundry Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-28 14:46:06'),
(734, '146', 'Updated corporate customer Devourge Industrial Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-05-28 14:46:51'),
(735, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-29 08:56:14'),
(736, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-29 09:04:24'),
(737, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-29 09:52:55'),
(738, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-29 10:21:52'),
(739, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-05-29 12:39:26'),
(740, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-02 09:30:31'),
(741, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-03 12:20:27'),
(742, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-03 12:21:13'),
(743, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-03 12:26:14'),
(744, '146', 'Updated corporate customer Fox Foundry Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 12:35:04'),
(745, '146', 'Updated corporate customer Fox Foundry Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 12:36:09'),
(746, '146', 'Updated corporate customer Valve Corp Zambia Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 12:43:03'),
(747, '146', 'Updated corporate customer Valve Corp Zambia Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 12:44:23'),
(748, '146', 'Updated corporate customer Fox Foundry Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 12:48:01'),
(749, '146', 'Updated corporate customer Fox Foundry Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-03 13:02:37'),
(750, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-03 14:47:00'),
(751, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-04 07:03:30'),
(752, '139', 'Updated corporate customer Hemiward Investments Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-04 07:15:10'),
(753, '139', 'Updated corporate customer Hemiward Investments Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-04 07:19:53'),
(754, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 07:20:47'),
(755, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-04 08:55:53'),
(756, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:52:41'),
(757, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:52:51'),
(758, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:53:10'),
(759, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:53:20'),
(760, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:53:30'),
(761, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:53:35'),
(762, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 09:53:43'),
(763, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:00:35'),
(764, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:00:44'),
(765, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:00:50'),
(766, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:01:02'),
(767, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:01:55'),
(768, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-04 10:21:32'),
(769, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-04 10:25:49'),
(770, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-04 11:04:59'),
(771, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-05 09:06:18'),
(772, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-05 11:12:27'),
(773, '146', 'Updated corporate customer Brassco Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-05 11:33:54'),
(774, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-06 08:52:27'),
(775, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-06 08:53:43'),
(776, '146', 'Updated corporate customer Brassco Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-06 08:54:21'),
(777, '146', 'Updated corporate customer Brassco Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-06 08:58:14'),
(778, '146', 'Updated corporate customer Brassco Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-06 08:58:53'),
(779, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-06 11:28:47'),
(780, '146', 'Updated corporate customer BIG Brands  Chain of  General Suppliers', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-06 11:42:23'),
(781, '146', 'Updated corporate customer Big Brands  Chain of  General Suppliers', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-06 11:46:45'),
(782, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-08 20:12:32'),
(783, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-09 08:49:49'),
(784, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-09 08:53:34'),
(785, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-09 12:08:10'),
(786, '146', 'Updated corporate customer ZAMBIA Breweries Distribution Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-09 12:31:16'),
(787, '146', 'Updated corporate customer Konkola Copper Mine', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-09 12:32:41'),
(788, '146', 'Updated corporate customer Zambia Breweries Distribution Limited', 'corporate_customer_registration', '', '', '0000-00-00 00:00:00', '2025-06-09 12:34:20'),
(789, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-09 16:00:19'),
(790, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-10 10:12:32'),
(791, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-11 09:03:22'),
(792, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-11 09:18:21'),
(793, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-11 09:21:37'),
(794, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-11 09:25:33'),
(795, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-11 09:25:46'),
(796, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-11 09:26:01'),
(797, '139', 'corporate customer approved', 'customer_approval', '', '', '0000-00-00 00:00:00', '2025-06-11 09:26:07'),
(798, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-11 09:36:04'),
(799, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-11 14:15:19'),
(800, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-11 14:18:04'),
(801, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-11 14:22:45'),
(802, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-13 09:12:29'),
(803, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-13 14:43:39'),
(804, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-13 17:59:17'),
(805, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 08:10:08'),
(806, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 08:15:52'),
(807, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 08:36:00'),
(808, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 13:18:15'),
(809, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 15:44:01'),
(810, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-16 16:56:59'),
(811, '1', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 06:47:44'),
(812, '1', 'Create role  Chief Financial Officer', '', '', '', '0000-00-00 00:00:00', '2025-06-17 06:50:05'),
(813, '1', 'updated role access', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-17 06:53:36'),
(814, '1', 'Create employeeChileshe Mwamba', 'adding', '', '', '0000-00-00 00:00:00', '2025-06-17 06:59:20'),
(815, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 08:22:55'),
(816, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 08:49:59'),
(817, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 11:20:29'),
(818, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 13:52:19'),
(819, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 14:22:40'),
(820, '138', 'APPROVED_FIRST  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-17 14:23:24'),
(821, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 14:25:23'),
(822, '139', 'APPROVED_SECOND  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-17 14:25:55'),
(823, '139', 'APPROVED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-17 14:26:46'),
(824, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-17 15:01:06'),
(825, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-18 13:15:02'),
(826, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-18 13:35:49'),
(827, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-18 14:50:08'),
(828, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-19 08:40:01'),
(829, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-19 14:00:27'),
(830, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 06:00:51'),
(831, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 07:57:52'),
(832, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 08:00:09'),
(833, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 08:50:13'),
(834, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 11:39:50'),
(835, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-20 12:45:22'),
(836, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-21 17:02:31'),
(837, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-21 17:02:44'),
(838, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 08:01:19'),
(839, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 08:20:54'),
(840, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-06-23 08:39:22'),
(841, '72', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 09:25:17'),
(842, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 12:15:11'),
(843, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 15:01:59'),
(844, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-23 15:17:09'),
(845, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-24 08:21:05'),
(846, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-24 13:26:28'),
(847, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-24 13:26:29'),
(848, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-24 15:11:43'),
(849, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-25 08:57:35'),
(850, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-25 10:03:16'),
(851, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-26 09:44:03'),
(852, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-26 10:15:55'),
(853, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-26 15:43:10'),
(854, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-27 08:38:29'),
(855, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-27 09:17:00'),
(856, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-06-30 09:58:04'),
(857, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-01 09:30:10'),
(858, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-01 14:37:10'),
(859, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-02 08:48:20'),
(860, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-02 09:24:03'),
(861, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:29:40'),
(862, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:30:01'),
(863, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:30:13'),
(864, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:30:29'),
(865, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:30:40'),
(866, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:30:51'),
(867, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:31:01'),
(868, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:36:08'),
(869, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:36:17'),
(870, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:36:31'),
(871, '138', 'Disbursed a loan', 'loan_disbursement', '', '', '0000-00-00 00:00:00', '2025-07-02 09:36:43'),
(872, '138', 'APPROVED_FIRST  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-02 10:27:25'),
(873, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-03 08:36:47'),
(874, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-04 15:50:27'),
(875, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-09 08:21:25'),
(876, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-11 11:17:59'),
(877, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-11 15:43:41'),
(878, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-11 15:49:25'),
(879, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-14 09:51:35'),
(880, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-14 09:59:40'),
(881, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-15 10:08:58'),
(882, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-15 13:39:41'),
(883, '146', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-16 10:25:01'),
(884, '147', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-17 15:16:32'),
(885, '138', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-21 15:51:42'),
(886, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-23 10:40:54'),
(887, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:07'),
(888, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:15'),
(889, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:23'),
(890, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:34'),
(891, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:42'),
(892, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:42:48'),
(893, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:43:11'),
(894, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:43:16'),
(895, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:43:23'),
(896, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:43:30'),
(897, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:44:13'),
(898, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:44:20'),
(899, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:44:30'),
(900, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:44:37'),
(901, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:48:05'),
(902, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:48:33'),
(903, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:49:24'),
(904, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:49:50'),
(905, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:50:16'),
(906, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 10:50:50'),
(907, '139', 'RECOMMENDED  a loan', 'updating', '', '', '0000-00-00 00:00:00', '2025-07-23 11:14:45'),
(908, '139', 'logged in the system', '', '', '', '0000-00-00 00:00:00', '2025-07-29 09:03:18');

-- --------------------------------------------------------

--
-- Table structure for table `approval_edits`
--

CREATE TABLE `approval_edits` (
  `approval_edits_id` int(11) NOT NULL,
  `old_info` text NOT NULL,
  `new_info` text NOT NULL,
  `type` enum('Loan edit') NOT NULL,
  `id` varchar(200) NOT NULL,
  `summary` varchar(200) DEFAULT NULL,
  `state` enum('Initiated','recommended','Approved') NOT NULL DEFAULT 'Initiated',
  `Initiated_by` varchar(200) NOT NULL,
  `Initiated_date` timestamp NOT NULL DEFAULT current_timestamp(),
  `recommended_by` varchar(200) NOT NULL,
  `recommended_date` date NOT NULL,
  `recommend_comment` text NOT NULL,
  `approved_by` varchar(200) NOT NULL,
  `approved_date` date NOT NULL,
  `approval_comment` text NOT NULL,
  `stamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `approval_edits`
--

INSERT INTO `approval_edits` (`approval_edits_id`, `old_info`, `new_info`, `type`, `id`, `summary`, `state`, `Initiated_by`, `Initiated_date`, `recommended_by`, `recommended_date`, `recommend_comment`, `approved_by`, `approved_date`, `approval_comment`, `stamp`) VALUES
(1, '{\"loan_id\":\"20\",\"loan_number\":\"loan456\",\"loan_product\":\"Yachangu\",\"loan_customer\":\"Misheck Kamuloni\",\"customer_type\":\"individual\",\"preview_url\":\"Individual_customers\\/view\\/\",\"customer_id\":\"1290\",\"loan_date\":\"2024-08-01\",\"loan_principal\":\"100000.00\",\"loan_period\":\"6\",\"loan_worthness_file\":null,\"narration\":\"<p>bhhhhh<\\/p>\\r\\n\",\"loan_added_by\":\"Administrator Account\"}', '{\"loan_id\":\"20\",\"loan_number\":\"loan456\",\"sy_loan_product\":\"16\",\"loan_product\":\"Yachangu\",\"sy_loan_customer\":\"1290\",\"loan_customer\":\"Misheck Kamuloni\",\"customer_type\":\"individual\",\"preview_url\":\"Individual_customers\\/view\\/\",\"customer_id\":\"1290\",\"loan_date\":\"2024-08-01\",\"loan_principal\":\"100000.00\",\"loan_period\":\"7\",\"loan_worthness_file\":null,\"narration\":null,\"sy_added_by\":\"7\",\"added_by\":\"Administrator Account\"}', 'Loan edit', '20', 'loan456', 'Approved', '7', '2024-08-09 15:43:59', '7', '2024-08-09', 'cttx', '7', '2024-08-09', 'sss', '2024-08-09 15:43:59');

-- --------------------------------------------------------

--
-- Table structure for table `authorized_signitories`
--

CREATE TABLE `authorized_signitories` (
  `id` int(11) NOT NULL,
  `ClientId` int(11) NOT NULL,
  `FullLegalName` varchar(250) NOT NULL,
  `IDType` enum('PASSPORT','NATIONAL_IDENTITY_CARD','DRIVER_LICENCE') NOT NULL,
  `IDNumber` varchar(20) NOT NULL,
  `DocImageURL` varchar(200) NOT NULL,
  `SignatureImageURL` varchar(255) NOT NULL,
  `Stamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bank_details`
--

CREATE TABLE `bank_details` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `account_name` varchar(50) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `bank_name` varchar(50) NOT NULL,
  `bank_branch` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `bank_details`
--

INSERT INTO `bank_details` (`id`, `customer_id`, `account_name`, `account_number`, `bank_name`, `bank_branch`) VALUES
(0, 0, 'NB', '1006768', 'NB', 10),
(0, 0, 'NBS', '*********', 'NBS', 2),
(0, 0, 'Customer1SME', '**********', 'FNB', 2),
(0, 1, 'Customer1SME', '**********', 'FNB', 36);

-- --------------------------------------------------------

--
-- Table structure for table `borrowed`
--

CREATE TABLE `borrowed` (
  `borrowed_id` int(11) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `total_interest` decimal(18,2) NOT NULL,
  `borrowed_from` varchar(200) NOT NULL,
  `date_borrowed` date NOT NULL,
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `borrowed`
--

INSERT INTO `borrowed` (`borrowed_id`, `amount`, `total_interest`, `borrowed_from`, `date_borrowed`, `stamp`) VALUES
(1, '1000000.00', '0.00', 'Kalgho', '2022-11-08', '2022-11-06 15:49:05');

-- --------------------------------------------------------

--
-- Table structure for table `borrowed_repayements`
--

CREATE TABLE `borrowed_repayements` (
  `b_id` int(11) NOT NULL,
  `borrowed_id` int(11) NOT NULL,
  `interest_paid` decimal(18,2) NOT NULL,
  `principal_paid` decimal(18,2) NOT NULL,
  `paid_by` int(11) NOT NULL,
  `date_of_repaymet` date NOT NULL,
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE `branches` (
  `id` int(11) NOT NULL,
  `Code` varchar(200) NOT NULL,
  `BranchCode` int(11) DEFAULT NULL,
  `BranchName` varchar(255) NOT NULL,
  `AddressLine1` varchar(200) NOT NULL,
  `AddressLine2` varchar(200) NOT NULL,
  `City` varchar(255) NOT NULL,
  `Stamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `system_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `branches`
--

INSERT INTO `branches` (`id`, `Code`, `BranchCode`, `BranchName`, `AddressLine1`, `AddressLine2`, `City`, `Stamp`, `system_date`) VALUES
(6, '89', 6743, 'Kitwe Branch', '', '', 'Lusaka', '2025-05-20 11:46:29', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `cashier_vault_pends`
--

CREATE TABLE `cashier_vault_pends` (
  `cvpid` int(11) NOT NULL,
  `vault_account` varchar(200) NOT NULL,
  `teller_account` varchar(200) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `creator` varchar(200) NOT NULL,
  `teller` varchar(200) DEFAULT NULL,
  `system_date` date NOT NULL,
  `status` enum('Initiated','Approved','Denied') NOT NULL DEFAULT 'Initiated',
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `charges`
--

CREATE TABLE `charges` (
  `charge_id` int(11) NOT NULL,
  `name` varchar(200) DEFAULT NULL,
  `charge_type` enum('Fixed','Variable') NOT NULL,
  `fixed_amount` decimal(18,2) NOT NULL,
  `variable_value` float NOT NULL,
  `is_active` enum('Yes','No') NOT NULL DEFAULT 'Yes'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `charges`
--

INSERT INTO `charges` (`charge_id`, `name`, `charge_type`, `fixed_amount`, `variable_value`, `is_active`) VALUES
(1, 'Loan Processing fee', 'Variable', '2000.00', 0.5, 'Yes'),
(2, 'Late payment fee', 'Fixed', '1000.00', 0.5, 'Yes'),
(3, 'New Customer Registration Fee', 'Fixed', '10000.00', 0, 'Yes'),
(4, 'Old Customer Registration Fee', 'Fixed', '5000.00', 0, 'Yes');

-- --------------------------------------------------------

--
-- Table structure for table `close_loan`
--

CREATE TABLE `close_loan` (
  `close_loan_id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `is_initiated` enum('yes','no') NOT NULL DEFAULT 'no',
  `reason_for_closing` text NOT NULL,
  `is_rejected` enum('yes','no') NOT NULL DEFAULT 'no',
  `rejected_date` date NOT NULL,
  `initiated_date` date NOT NULL,
  `is_recommended` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_date` date NOT NULL,
  `initiated_by` int(11) NOT NULL,
  `close_loan_status` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_by` int(11) DEFAULT NULL,
  `close_by` int(11) DEFAULT NULL,
  `closed_loan_date` date DEFAULT NULL,
  `rejected_by` int(11) DEFAULT NULL,
  `rejection_reasons` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `corporate_customers`
--

CREATE TABLE `corporate_customers` (
  `id` int(11) NOT NULL,
  `EntityName` varchar(250) NOT NULL,
  `DateOfIncorporation` date NOT NULL,
  `RegistrationNumber` varchar(50) DEFAULT NULL,
  `entity_type` enum('Sole Proprietorship','Partnership','Limited Company') DEFAULT NULL,
  `nature_of_business` varchar(100) NOT NULL,
  `industry_sector` varchar(100) NOT NULL,
  `street` varchar(50) DEFAULT NULL,
  `city_town` varchar(50) NOT NULL,
  `province` varchar(50) DEFAULT NULL,
  `postal_code` varchar(50) DEFAULT NULL,
  `website` varchar(100) DEFAULT NULL,
  `ClientId` varchar(255) NOT NULL,
  `TaxIdentificationNumber` varchar(100) DEFAULT NULL,
  `company_certificate` varchar(200) DEFAULT NULL,
  `tax_id_doc` varchar(200) DEFAULT NULL,
  `proof_physical_address` varchar(100) DEFAULT NULL,
  `financial_statement` varchar(100) DEFAULT NULL,
  `Country` varchar(2) NOT NULL,
  `Branch` varchar(255) NOT NULL,
  `Status` enum('ACTIVE','SUSPENDED') NOT NULL DEFAULT 'ACTIVE',
  `category` enum('client','off_taker') NOT NULL,
  `phone_number` varchar(50) DEFAULT NULL,
  `contact_email` varchar(50) DEFAULT NULL,
  `LastUpdatedOn` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `CreatedOn` timestamp NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL,
  `approval_status` enum('Approved','Not Approved','Rejected') NOT NULL DEFAULT 'Not Approved',
  `added_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `corporate_customers`
--

INSERT INTO `corporate_customers` (`id`, `EntityName`, `DateOfIncorporation`, `RegistrationNumber`, `entity_type`, `nature_of_business`, `industry_sector`, `street`, `city_town`, `province`, `postal_code`, `website`, `ClientId`, `TaxIdentificationNumber`, `company_certificate`, `tax_id_doc`, `proof_physical_address`, `financial_statement`, `Country`, `Branch`, `Status`, `category`, `phone_number`, `contact_email`, `LastUpdatedOn`, `CreatedOn`, `system_date`, `approval_status`, `added_by`) VALUES
(1, 'Kenport Logistics', '2021-02-13', '120150134681', 'Limited Company', 'Limited Liability Company (LLC)', 'Transportation', 'CHANDWE MUSONDA ROAD ', 'LUSAKA', 'Lusaka', '10101', 'N/A', '', '1003356888', '', '', '', '', '89', 'LUSAKA', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-05-27 07:53:50', '2025-05-13 06:50:01', NULL, 'Approved', 146),
(2, 'Perez Investments Limited', '2025-05-15', '120050060227', '', 'Limited Liability Company (LLC)', 'Mining', 'Dvid Mwila House Room # 211,Kitwe', 'Kitwe', 'Copperbelt', '101010', 'TBA', '', 'TBA', '', '', '', '', '89', '6', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-05-16 08:08:23', '2025-05-15 11:44:43', NULL, 'Approved', 139),
(8, 'Mopani Copper Mines PLC', '1900-02-12', 'TBA', '', '', '', '', '', NULL, NULL, '', '2010003', 'TBA', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'off_taker', '&lt;div style=', '', '2025-05-21 08:47:24', '2025-05-16 08:24:25', NULL, 'Approved', 139),
(10, 'Quorn Flooring Limited', '2008-08-29', '120080074596', 'Limited Company', 'Limited Liability Company (LLC)', 'Construction', 'SUITE 24 DAVID MWILA HOUSE, PRESIDENT AVENUE ', 'KITWE', 'COPPERBELT ', '10101', '', '', '1001923532', '', '', '', '', '89', 'KITWE', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-04 07:52:41', '2025-05-21 06:28:05', NULL, 'Approved', 146),
(12, 'Spekport Limited', '2007-02-11', '120070069768', 'Limited Company', 'Limited Liability Company (LLC)', 'Mining', 'Suite-8-2nd Floor, Kalilungu House, Obote Avenue', 'Kitwe', NULL, NULL, 'N/A', '2010005', '1001883113', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0955710055', '<EMAIL>', '2025-05-21 08:46:47', '2025-05-21 07:13:18', NULL, 'Approved', 139),
(14, 'Inkaba Zambia Limited', '2022-11-22', '78400', 'Limited Company', 'Limited Liability Company (LLC)', 'Mining', 'Plot no 123 ZSIC Mufulira', 'Mufulira', NULL, NULL, 'N/A', '2010006', '1001946936', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0968600794', '<EMAIL>', '2025-05-21 11:37:01', '2025-05-21 11:18:23', NULL, 'Approved', 139),
(15, 'Wheals Construction Limited', '2001-01-31', '120010046490', 'Limited Company', 'Limited Liability Company (LLC)', 'Construction', 'PLOT NO 3065 , KITWE - CHINGOLA DUAL CARRIAGE WAY ', 'CHAMBISHI', 'COPPERBELT ', '10101', 'N/A', '', '1001734803', '', '', '', '', '89', 'KITWE', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-05-27 07:52:34', '2025-05-23 06:28:40', NULL, 'Approved', 146),
(17, 'Kuyesa', '2025-05-14', 'NA', 'Sole Proprietorship', 'Agriculture', 'Agriculture', '', '', 'Muchinga', NULL, '', '2010008', 'NA', NULL, NULL, NULL, NULL, '71', '6', 'ACTIVE', 'client', '0994099461', '', '2025-05-26 14:10:14', '2025-05-26 14:10:14', NULL, 'Not Approved', 72),
(18, 'Kansanshi Copper Mines Plc', '1964-02-01', '', 'Limited Company', '', '', '', '', 'Copperbelt', NULL, '', '2010009', 'TBA', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'off_taker', '&lt;div style=', '', '2025-05-27 07:52:05', '2025-05-27 07:33:09', NULL, 'Approved', 139),
(21, 'RGPM Chemicals Limited', '2020-06-08', '120200004392', 'Limited Company', 'Energy', 'Mining', '3rd Floor, ECL Office Park, Block 2', 'Kitwe', 'Copperbelt', '', 'www.rgpm-group.com', '', '2699589680', '', '', '', '', '89', '6', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-05-27 07:50:28', '2025-05-27 07:48:35', NULL, 'Approved', 139),
(24, 'Flabene Engineering Limitted', '2008-02-26', '71269', 'Limited Company', 'Other', 'Mining', 'Room 19 first Floor , Kitwe Chambers', 'Kitwe', 'Copperbelt', NULL, 'N/A', '2010011', '1000007126903', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0955 852400                 ', '<EMAIL>', '2025-05-27 11:11:18', '2025-05-27 11:10:55', NULL, 'Approved', 139),
(28, 'Lumwana Copper Mines', '2009-04-01', 'N/A', 'Limited Company', 'Mining', 'Mining', 'SOLWEZI', 'SOLWEZI', 'North-Western', '10101', 'N/A', '', '1001582192.', '', '', '', '', '89', 'NORTH WESTERN PROVINCE', 'ACTIVE', 'off_taker', NULL, '<EMAIL>', '2025-06-04 07:52:51', '2025-05-27 12:07:36', NULL, 'Approved', 146),
(29, 'Fox Foundry Limited', '2006-07-17', '62945', 'Partnership', 'Mining', 'Mining', 'PLOT NUMBER 6364', 'KITWE', 'Copperbelt', '10101', 'N/A', '', '1242535627', '', '', '', '', '89', 'KITWE', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-04 07:53:10', '2025-05-28 08:21:40', NULL, 'Approved', 146),
(30, 'Shamuchisha', '2010-10-28', '1245678900', 'Limited Company', 'Mining', 'Other', 'PLOT NO 2 , 12th STREET , NKANA WEST', 'KITWE', 'Copperbelt', NULL, 'N/A', '2010014', '1001964258', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0977305978', '<EMAIL>', '2025-06-04 07:53:30', '2025-05-28 08:32:29', NULL, 'Approved', 146),
(31, 'Chimwe Investments Limited', '2009-06-16', '*********', 'Limited Company', 'Other', 'Other', 'STADIUM AREA , PLOT 8118 ', 'SOLWEZI', 'North-Western', NULL, 'N/A', '2010015', '1002107801', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0966750694', '<EMAIL>', '2025-06-04 07:53:43', '2025-05-28 09:09:57', NULL, 'Approved', 146),
(33, 'Devourge Industrial Limited', '2009-11-28', '*********', 'Limited Company', 'Retail Trade', 'Retail Trade', '7479, ZEBRA / CHIWALA , NKANA EAST', 'KITWE', 'Copperbelt', '10101', 'N/A', '', '1001872595', '', '', '', '', '89', '6', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-04 07:53:20', '2025-05-28 11:45:08', NULL, 'Approved', 146),
(34, 'Hemiward Investments Limited', '2006-11-14', '65928', 'Limited Company', 'Mining', 'Manufacturing', 'Plot No. 417 Euclid Road Congar Engineering Yard, ', 'Kitwe', 'Copperbelt', '10101', 'N/A', '', '1001869595', '', '', '', '', '89', 'Kitwe', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-04 05:20:47', '2025-05-28 12:00:40', NULL, 'Approved', 146),
(35, 'Valve Corp Zambia Limited', '2004-11-20', '56785', 'Limited Company', 'Mining', 'Mining', 'POLYSTRENE HOUSE PLOT NO 3944, ROOM 4 , FREETOWN', 'KITWE', 'Copperbelt', '10101', 'N/A', '', '1003074618', '', '', '', '', '89', 'KITWE', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-04 07:53:35', '2025-05-28 12:09:12', NULL, 'Approved', 146),
(38, 'Westmead Limited', '2013-06-25', '*********', 'Limited Company', 'Mining', 'Mining', '10 Katanta Street , Nkana East', 'Kitwe', 'Copperbelt', NULL, 'N/A', '2010019', '*********', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0963407289', '<EMAIL>', '2025-06-03 10:26:14', '2025-06-02 07:59:16', NULL, 'Approved', 146),
(39, 'Brassco Limited', '2005-08-09', '*********566', 'Limited Company', 'Mining', 'Mining', 'Plot 4967 Lyoneno Road Heavy Industrial Area', 'Kitwe', 'Copperbelt', '10101', 'N/A', '', '1001811067', '', '', '', '', '89', '6', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-05 09:33:54', '2025-06-04 08:18:26', NULL, 'Rejected', 139),
(42, 'Brassco Limited', '2011-06-30', '*********', 'Limited Company', 'Mining', 'Mining', 'Plot 4967 Lyoneno Road Heavy Industrial Area', 'Kitwe', 'Copperbelt', '10101', 'N/A', '', '1001811067', '', '', '', '', '89', 'KITWE', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-06 06:58:53', '2025-06-04 08:19:01', NULL, 'Approved', 139),
(43, 'Big Brands  Chain of  General Suppliers', '2004-08-20', '74397', '', 'Retail Trade', 'Retail Trade', 'PASELI ROAD , NORTHMEAD', 'LUSAKA', 'Lusaka', '10101', 'N/A', '', '1001929100', '', '', '', '', '89', 'LUSAKA', 'ACTIVE', 'client', NULL, '<EMAIL>', '2025-06-11 07:26:07', '2025-06-06 09:33:57', NULL, 'Approved', 146),
(45, 'Zambia Breweries Distribution Limited', '2022-01-10', '120220027577', 'Partnership', 'Food & Beverage', 'Retail Trade', 'Plot 6438 Mungwi Road Heavy Industrial Area', 'LUSAKA', 'Lusaka', '10101', 'N/A', '', '2993120618', '', '', '', '', '89', 'LUSAKA', 'ACTIVE', 'off_taker', NULL, '<EMAIL>', '2025-06-11 07:26:01', '2025-06-09 07:10:57', NULL, 'Approved', 146),
(46, 'Konkola Copper Mine', '1999-12-22', '119990043628', 'Limited Company', 'Mining', 'Mining', ' Konkola Copper Mines Plc - ZCCM Investments Holdi', 'Chingola', 'Copperbelt', '10101', 'www.kcm.co.zm', '', '1001772785', '', '', '', '', '89', '6', 'ACTIVE', 'off_taker', NULL, '<EMAIL>', '2025-06-11 07:21:37', '2025-06-09 07:44:10', NULL, 'Approved', 146),
(47, 'Riverun Enterprises Limited', '2007-11-05', '12007006902', 'Limited Company', 'Mining', 'Mining', '1st Floor Room 10 Solidarity House, Oxford Road, T', 'Kitwe', 'Copperbelt', NULL, '', '2010025', '1001907104', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0966 364058', '<EMAIL>', '2025-06-11 07:25:46', '2025-06-09 14:26:54', NULL, 'Approved', 139),
(48, 'Turnkey Investments Limited', '2016-02-08', '120160000988', 'Limited Company', 'Construction', 'Construction', '9 Mayfair Crescent , Parklands .', 'Kitwe', 'Copperbelt', NULL, 'N/A', '2010026', '1003576215', '6022.INCORPORATION_CERTIFICATE.pdf', '255_reports_complianceReports_General_TCC_(10).pdf', NULL, NULL, '89', '6', 'ACTIVE', 'client', '+260955019658', '<EMAIL>', '2025-06-11 07:25:33', '2025-06-11 07:17:32', NULL, 'Approved', 146),
(49, 'Lubambe Copper Mine', '2012-01-02', '119960037227', 'Limited Company', 'Mining', 'Mining', '2nd Shaft Chimfunshi Road', 'Chilambombwe', 'Copperbelt', NULL, 'N/A', '2010027', '1001582192', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'off_taker', '0761150000', '<EMAIL>', '2025-06-16 13:35:45', '2025-06-16 13:35:45', NULL, 'Not Approved', 146),
(50, 'Themfex', '2012-04-17', '*********452', 'Limited Company', 'Other', 'Other', 'Obote Avenue ', 'Kitwe ', 'Copperbelt', NULL, 'N/A', '2010028', '1002088591', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0966203033', '<EMAIL>', '2025-06-17 07:23:40', '2025-06-17 07:23:40', NULL, 'Not Approved', 146),
(51, 'Orica Zambia Limited', '2000-03-01', '119900022027', 'Limited Company', 'Mining', 'Mining', 'Nfca Mine Area, Off Kitwe, Chingola Road', 'Chingola', '', NULL, 'www.orica.com ', '2010029', '1001688716', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'off_taker', '260 2 721 310', '<EMAIL>', '2025-06-17 07:35:32', '2025-06-17 07:35:32', NULL, 'Not Approved', 146),
(52, 'Kagem Gemfields Limited', '1984-12-04', '119850012958', 'Limited Company', 'Mining', 'Mining', 'Corner of Dr Aggrey Road and Kariba Roads , Light ', 'Kitwe', 'Copperbelt', NULL, 'www.gemfields.com', '2010030', '1001612576', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'off_taker', '0962354633', '<EMAIL>', '2025-06-18 12:14:43', '2025-06-18 12:14:43', NULL, 'Not Approved', 146),
(53, 'Supriag Technical and Procurement Limited', '2006-05-02', '1200040054256', 'Limited Company', 'Mining', 'Mining', '149, Luela Street , Nkana East.', 'Kitwe', 'Copperbelt', NULL, '', '2010031', '1001727647', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0955839526', '<EMAIL>', '2025-06-19 09:14:06', '2025-06-19 09:14:06', NULL, 'Not Approved', 146),
(54, 'Thelb Enterprises Limited', '2020-08-25', '12020006814', 'Limited Company', 'Mining', 'Mining', 'Mushitala Area ', 'Solwezi', 'North-Western', NULL, 'N/A ', '2010032', '2184498662', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', 'N/A ', '<EMAIL>', '2025-06-19 09:47:00', '2025-06-19 09:47:00', NULL, 'Not Approved', 146),
(55, 'Oriental Products Limited', '2010-08-29', '120110095920', 'Limited Company', 'Mining', 'Mining', '1413, Station Road , Industrial Area, Mwaiseni ', 'Chingola ', 'Copperbelt', NULL, 'N/A', '2010033', '1002094283', NULL, NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0966284832', '<EMAIL>', '2025-06-19 12:19:59', '2025-06-19 12:19:59', NULL, 'Not Approved', 146),
(56, 'Drill Mech Zambia Limited', '2012-02-08', '120120099534', 'Limited Company', 'Construction', 'Construction', 'Nkana East', 'Kitwe', 'Copperbelt', NULL, '', '2010034', '1000009953408', '330Certificate_of_incorp.pdf', NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0955783836', '<EMAIL>', '2025-06-27 06:57:09', '2025-06-27 06:57:09', NULL, 'Not Approved', 146),
(57, 'Infocus TM Zambia Limited', '2019-12-18', '120190009854', 'Limited Company', 'Construction', 'Construction', '79th Street Nchanga South ', 'Chingola ', 'Copperbelt', NULL, '', '2010035', '1020762896', '857InFocus_PACRA_Printout.pdf', NULL, NULL, NULL, '89', '6', 'ACTIVE', 'client', '0969848015', '<EMAIL>', '2025-07-03 06:53:37', '2025-07-03 06:53:37', NULL, 'Not Approved', 146);

-- --------------------------------------------------------

--
-- Table structure for table `corporate_shareholders`
--

CREATE TABLE `corporate_shareholders` (
  `id` int(11) NOT NULL,
  `corporate_id` int(11) NOT NULL,
  `shareholder_id` int(11) NOT NULL,
  `percentage_value` float NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `corporate_shareholders`
--

INSERT INTO `corporate_shareholders` (`id`, `corporate_id`, `shareholder_id`, `percentage_value`) VALUES
(1, 17, 3, 100),
(2, 21, 4, 93),
(3, 24, 5, 75),
(4, 24, 6, 25),
(5, 29, 7, 80),
(6, 30, 8, 50),
(7, 31, 9, 50),
(8, 33, 10, 70),
(9, 34, 11, 70),
(10, 35, 12, 80),
(11, 38, 13, 50),
(12, 42, 15, 53),
(13, 47, 16, 41),
(14, 48, 17, 63),
(15, 48, 18, 3),
(16, 50, 19, 75),
(17, 53, 20, 50),
(18, 53, 21, 50),
(19, 54, 22, 33),
(20, 54, 23, 66),
(21, 55, 24, 80),
(22, 55, 25, 20),
(23, 56, 26, 70),
(24, 56, 27, 30),
(25, 57, 28, 75),
(26, 57, 29, 25);

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `currency_id` int(11) NOT NULL,
  `currency_code` char(3) NOT NULL,
  `currency_name` varchar(100) NOT NULL,
  `country_name` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`currency_id`, `currency_code`, `currency_name`, `country_name`) VALUES
(1, 'AED', 'United Arab Emirates Dirham', 'United Arab Emirates'),
(2, 'AFN', 'Afghan Afghani', 'Afghanistan'),
(3, 'ALL', 'Albanian Lek', 'Albania'),
(4, 'AMD', 'Armenian Dram', 'Armenia'),
(5, 'ANG', 'Netherlands Antillean Guilder', 'Netherlands Antilles'),
(6, 'AOA', 'Angolan Kwanza', 'Angola'),
(7, 'ARS', 'Argentine Peso', 'Argentina'),
(8, 'AUD', 'Australian Dollar', 'Australia'),
(9, 'AWG', 'Aruban Florin', 'Aruba'),
(10, 'AZN', 'Azerbaijani Manat', 'Azerbaijan'),
(11, 'BAM', 'Bosnia-Herzegovina Convertible Mark', 'Bosnia and Herzegovina'),
(12, 'BBD', 'Barbadian Dollar', 'Barbados'),
(13, 'BDT', 'Bangladeshi Taka', 'Bangladesh'),
(14, 'BGN', 'Bulgarian Lev', 'Bulgaria'),
(15, 'BHD', 'Bahraini Dinar', 'Bahrain'),
(16, 'BIF', 'Burundian Franc', 'Burundi'),
(17, 'BMD', 'Bermudian Dollar', 'Bermuda'),
(18, 'BND', 'Brunei Dollar', 'Brunei'),
(19, 'BOB', 'Bolivian Boliviano', 'Bolivia'),
(20, 'BRL', 'Brazilian Real', 'Brazil'),
(21, 'BSD', 'Bahamian Dollar', 'Bahamas'),
(22, 'BTN', 'Bhutanese Ngultrum', 'Bhutan'),
(23, 'BWP', 'Botswana Pula', 'Botswana'),
(24, 'BYN', 'Belarusian Ruble', 'Belarus'),
(25, 'BZD', 'Belize Dollar', 'Belize'),
(26, 'CAD', 'Canadian Dollar', 'Canada'),
(27, 'CDF', 'Congolese Franc', 'Democratic Republic of the Congo'),
(28, 'CHF', 'Swiss Franc', 'Switzerland'),
(29, 'CLP', 'Chilean Peso', 'Chile'),
(30, 'CNY', 'Chinese Yuan', 'China'),
(31, 'COP', 'Colombian Peso', 'Colombia'),
(32, 'CRC', 'Costa Rican Colón', 'Costa Rica'),
(33, 'CUP', 'Cuban Peso', 'Cuba'),
(34, 'CVE', 'Cape Verdean Escudo', 'Cape Verde'),
(35, 'CZK', 'Czech Koruna', 'Czech Republic'),
(36, 'DJF', 'Djiboutian Franc', 'Djibouti'),
(37, 'DKK', 'Danish Krone', 'Denmark'),
(38, 'DOP', 'Dominican Peso', 'Dominican Republic'),
(39, 'DZD', 'Algerian Dinar', 'Algeria'),
(40, 'EGP', 'Egyptian Pound', 'Egypt'),
(41, 'ERN', 'Eritrean Nakfa', 'Eritrea'),
(42, 'ETB', 'Ethiopian Birr', 'Ethiopia'),
(43, 'EUR', 'Euro', 'Eurozone'),
(44, 'FJD', 'Fijian Dollar', 'Fiji'),
(45, 'FKP', 'Falkland Islands Pound', 'Falkland Islands'),
(46, 'FOK', 'Faroese Króna', 'Faroe Islands'),
(47, 'GBP', 'British Pound Sterling', 'United Kingdom'),
(48, 'GEL', 'Georgian Lari', 'Georgia'),
(49, 'GGP', 'Guernsey Pound', 'Guernsey'),
(50, 'GHS', 'Ghanaian Cedi', 'Ghana'),
(51, 'GIP', 'Gibraltar Pound', 'Gibraltar'),
(52, 'GMD', 'Gambian Dalasi', 'Gambia'),
(53, 'GNF', 'Guinean Franc', 'Guinea'),
(54, 'GTQ', 'Guatemalan Quetzal', 'Guatemala'),
(55, 'GYD', 'Guyanese Dollar', 'Guyana'),
(56, 'HKD', 'Hong Kong Dollar', 'Hong Kong'),
(57, 'HNL', 'Honduran Lempira', 'Honduras'),
(58, 'HRK', 'Croatian Kuna', 'Croatia'),
(59, 'HTG', 'Haitian Gourde', 'Haiti'),
(60, 'HUF', 'Hungarian Forint', 'Hungary'),
(61, 'IDR', 'Indonesian Rupiah', 'Indonesia'),
(62, 'ILS', 'Israeli New Shekel', 'Israel'),
(63, 'IMP', 'Isle of Man Pound', 'Isle of Man'),
(64, 'INR', 'Indian Rupee', 'India'),
(65, 'IQD', 'Iraqi Dinar', 'Iraq'),
(66, 'IRR', 'Iranian Rial', 'Iran'),
(67, 'ISK', 'Icelandic Króna', 'Iceland'),
(68, 'JEP', 'Jersey Pound', 'Jersey'),
(69, 'JMD', 'Jamaican Dollar', 'Jamaica'),
(70, 'JOD', 'Jordanian Dinar', 'Jordan'),
(71, 'JPY', 'Japanese Yen', 'Japan'),
(72, 'KES', 'Kenyan Shilling', 'Kenya'),
(73, 'KGS', 'Kyrgyzstani Som', 'Kyrgyzstan'),
(74, 'KHR', 'Cambodian Riel', 'Cambodia'),
(75, 'KID', 'Kiribati Dollar', 'Kiribati'),
(76, 'KMF', 'Comorian Franc', 'Comoros'),
(77, 'KRW', 'South Korean Won', 'South Korea'),
(78, 'KWD', 'Kuwaiti Dinar', 'Kuwait'),
(79, 'KYD', 'Cayman Islands Dollar', 'Cayman Islands'),
(80, 'KZT', 'Kazakhstani Tenge', 'Kazakhstan'),
(81, 'LAK', 'Lao Kip', 'Laos'),
(82, 'LBP', 'Lebanese Pound', 'Lebanon'),
(83, 'LKR', 'Sri Lankan Rupee', 'Sri Lanka'),
(84, 'LRD', 'Liberian Dollar', 'Liberia'),
(85, 'LSL', 'Lesotho Loti', 'Lesotho'),
(86, 'LYD', 'Libyan Dinar', 'Libya'),
(87, 'MAD', 'Moroccan Dirham', 'Morocco'),
(88, 'MDL', 'Moldovan Leu', 'Moldova'),
(89, 'MGA', 'Malagasy Ariary', 'Madagascar'),
(90, 'MKD', 'Macedonian Denar', 'North Macedonia'),
(91, 'MMK', 'Burmese Kyat', 'Myanmar'),
(92, 'MNT', 'Mongolian Tögrög', 'Mongolia'),
(93, 'MOP', 'Macanese Pataca', 'Macau'),
(94, 'MRU', 'Mauritanian Ouguiya', 'Mauritania'),
(95, 'MUR', 'Mauritian Rupee', 'Mauritius'),
(96, 'MVR', 'Maldivian Rufiyaa', 'Maldives'),
(97, 'MWK', 'Malawian Kwacha', 'Malawi'),
(98, 'MXN', 'Mexican Peso', 'Mexico'),
(99, 'MYR', 'Malaysian Ringgit', 'Malaysia'),
(100, 'MZN', 'Mozambican Metical', 'Mozambique'),
(101, 'NAD', 'Namibian Dollar', 'Namibia'),
(102, 'NGN', 'Nigerian Naira', 'Nigeria'),
(103, 'NIO', 'Nicaraguan Córdoba', 'Nicaragua'),
(104, 'NOK', 'Norwegian Krone', 'Norway'),
(105, 'NPR', 'Nepalese Rupee', 'Nepal'),
(106, 'NZD', 'New Zealand Dollar', 'New Zealand'),
(107, 'OMR', 'Omani Rial', 'Oman'),
(108, 'PAB', 'Panamanian Balboa', 'Panama'),
(109, 'PEN', 'Peruvian Sol', 'Peru'),
(110, 'PGK', 'Papua New Guinean Kina', 'Papua New Guinea'),
(111, 'PHP', 'Philippine Peso', 'Philippines'),
(112, 'PKR', 'Pakistani Rupee', 'Pakistan'),
(113, 'PLN', 'Polish Złoty', 'Poland'),
(114, 'PYG', 'Paraguayan Guaraní', 'Paraguay'),
(115, 'QAR', 'Qatari Riyal', 'Qatar'),
(116, 'RON', 'Romanian Leu', 'Romania'),
(117, 'RSD', 'Serbian Dinar', 'Serbia'),
(118, 'RUB', 'Russian Ruble', 'Russia'),
(119, 'RWF', 'Rwandan Franc', 'Rwanda'),
(120, 'SAR', 'Saudi Riyal', 'Saudi Arabia'),
(121, 'SBD', 'Solomon Islands Dollar', 'Solomon Islands'),
(122, 'SCR', 'Seychellois Rupee', 'Seychelles'),
(123, 'SDG', 'Sudanese Pound', 'Sudan'),
(124, 'SEK', 'Swedish Krona', 'Sweden'),
(125, 'SGD', 'Singapore Dollar', 'Singapore'),
(126, 'SHP', 'Saint Helena Pound', 'Saint Helena'),
(127, 'SLL', 'Sierra Leonean Leone', 'Sierra Leone'),
(128, 'SOS', 'Somali Shilling', 'Somalia'),
(129, 'SRD', 'Surinamese Dollar', 'Suriname'),
(130, 'SSP', 'South Sudanese Pound', 'South Sudan'),
(131, 'STN', 'São Tomé and Príncipe Dobra', 'São Tomé and Príncipe'),
(132, 'SYP', 'Syrian Pound', 'Syria'),
(133, 'SZL', 'Eswatini Lilangeni', 'Eswatini'),
(134, 'THB', 'Thai Baht', 'Thailand'),
(135, 'TJS', 'Tajikistani Somoni', 'Tajikistan'),
(136, 'TMT', 'Turkmenistani Manat', 'Turkmenistan'),
(137, 'TND', 'Tunisian Dinar', 'Tunisia'),
(138, 'TOP', 'Tongan Paʻanga', 'Tonga'),
(139, 'TRY', 'Turkish Lira', 'Turkey'),
(140, 'TTD', 'Trinidad and Tobago Dollar', 'Trinidad and Tobago'),
(141, 'TVD', 'Tuvaluan Dollar', 'Tuvalu'),
(142, 'TWD', 'New Taiwan Dollar', 'Taiwan'),
(143, 'TZS', 'Tanzanian Shilling', 'Tanzania'),
(144, 'UAH', 'Ukrainian Hryvnia', 'Ukraine'),
(145, 'UGX', 'Ugandan Shilling', 'Uganda'),
(146, 'USD', 'United States Dollar', 'United States'),
(147, 'UYU', 'Uruguayan Peso', 'Uruguay'),
(148, 'UZS', 'Uzbekistani Soʻm', 'Uzbekistan'),
(149, 'VES', 'Venezuelan Bolívar', 'Venezuela'),
(150, 'VND', 'Vietnamese Đồng', 'Vietnam'),
(151, 'VUV', 'Vanuatu Vatu', 'Vanuatu'),
(152, 'WST', 'Samoan Tālā', 'Samoa'),
(153, 'XAF', 'Central African CFA Franc', 'Central African States'),
(154, 'XCD', 'East Caribbean Dollar', 'East Caribbean States'),
(155, 'XOF', 'West African CFA Franc', 'West African States'),
(156, 'XPF', 'CFP Franc', 'French Territories of the Pacific'),
(157, 'YER', 'Yemeni Rial', 'Yemen'),
(158, 'ZAR', 'South African Rand', 'South Africa'),
(159, 'ZMW', 'Zambian Kwacha', 'Zambia'),
(160, 'ZWL', 'Zimbabwean Dollar', 'Zimbabwe');

-- --------------------------------------------------------

--
-- Table structure for table `currency`
--

CREATE TABLE `currency` (
  `id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `symbol` varchar(200) NOT NULL,
  `code` varchar(20) NOT NULL,
  `is_local` enum('Yes','No') NOT NULL DEFAULT 'No'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `currency`
--

INSERT INTO `currency` (`id`, `name`, `symbol`, `code`, `is_local`) VALUES
(5, 'Malawi Kwacha', 'MK', '05', 'No');

-- --------------------------------------------------------

--
-- Table structure for table `customer_access`
--

CREATE TABLE `customer_access` (
  `id` int(11) NOT NULL,
  `customer_id` varchar(200) NOT NULL,
  `phone_number` varchar(200) NOT NULL,
  `created_by` varchar(200) NOT NULL,
  `approved_by` varchar(200) NOT NULL,
  `denied_by` varchar(200) NOT NULL,
  `status` enum('Active','Rejected','Deactivated','Initiated') NOT NULL DEFAULT 'Initiated',
  `password` varchar(200) NOT NULL,
  `stamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_groups`
--

CREATE TABLE `customer_groups` (
  `customer_group_id` int(11) NOT NULL,
  `customer` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `date_joined` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `delete_payments`
--

CREATE TABLE `delete_payments` (
  `delete_loan_id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `is_initiated` enum('yes','no') NOT NULL DEFAULT 'no',
  `reason_for_deleting` text NOT NULL,
  `is_rejected` enum('yes','no') NOT NULL DEFAULT 'no',
  `rejected_date` datetime NOT NULL DEFAULT current_timestamp(),
  `initiated_date` datetime NOT NULL DEFAULT current_timestamp(),
  `is_recommended` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_date` datetime NOT NULL DEFAULT current_timestamp(),
  `initiated_by` int(11) NOT NULL,
  `delete_payment_status` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_by` int(11) DEFAULT NULL,
  `deleted_by` int(11) DEFAULT NULL,
  `delete_payment_date` date NOT NULL,
  `rejected_by` int(11) DEFAULT NULL,
  `rejection_reasons` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `districts`
--

CREATE TABLE `districts` (
  `district_id` int(11) NOT NULL,
  `district_name` varchar(20) NOT NULL,
  `district_code` varchar(5) NOT NULL,
  `parent` int(11) NOT NULL DEFAULT 1,
  `rcode` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `districts`
--

INSERT INTO `districts` (`district_id`, `district_name`, `district_code`, `parent`, `rcode`) VALUES
(1, 'BALAKA', 'BLK', 35, '01'),
(2, 'BLANTYRE', 'BT', 34, '02'),
(3, 'CHITIPA', 'CTP', 37, '01'),
(4, 'CHIKWAWA', 'CKW', 34, '01'),
(5, 'CHIRADZULU', 'CLD', 35, '03'),
(6, 'DEDZA', 'DZ', 36, '03'),
(7, 'DOWA', 'DW', 36, '02'),
(8, 'KASUNGU', 'KU', 36, '04'),
(9, 'KARONGA', 'KRG', 37, '01'),
(10, 'LILONGWE', 'LL', 36, '01'),
(11, 'LIWONDE', 'LWD', 35, '04'),
(12, 'MACHINGA', 'MCG', 35, '05'),
(13, 'MANGOCHI', 'MGH', 35, '06'),
(14, 'MULANJE', 'MJ', 34, '02'),
(15, 'MCHINJI', 'MCJ', 36, '05'),
(16, 'NSANJE', 'NSJ', 34, '03'),
(17, 'NTCHEU', 'NCHU', 36, '06'),
(18, 'NTCHISI', 'NTS', 36, '07'),
(19, 'NKHOTAKOTA', 'KK', 36, '08'),
(20, 'NKHATABAY', 'NKB', 37, '0'),
(21, 'NENO', 'NN', 34, '04'),
(22, 'RUMPHI', 'RH', 37, '0'),
(23, 'SALIMA', 'SLM', 36, '09'),
(24, 'THYOLO', 'TYL', 34, '05'),
(25, 'ZOMBA', 'ZA', 35, '07'),
(26, 'MZUZU', 'MZ', 37, '0'),
(27, 'MZIMBA', 'MZB', 37, '0'),
(28, 'NGABU', 'NGB', 34, '0'),
(29, 'MWANZA', 'MWZ', 34, '06'),
(34, 'Southern region', 'sr', 0, '1'),
(35, 'Eastern Region', 'er', 0, '4'),
(36, 'Central region', 'cr', 0, '2'),
(37, 'Northern  region', 'nr', 0, '3');

-- --------------------------------------------------------

--
-- Table structure for table `documents`
--

CREATE TABLE `documents` (
  `id` int(11) NOT NULL,
  `p_lid` int(11) NOT NULL,
  `document_name` varchar(200) NOT NULL,
  `file_link` varchar(200) NOT NULL,
  `stamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `edit_loan`
--

CREATE TABLE `edit_loan` (
  `edit_loan_id` int(11) NOT NULL,
  `loan_id` int(11) NOT NULL,
  `old_loan_number` varchar(50) NOT NULL,
  `new_loan_number` varchar(50) NOT NULL,
  `is_initiated` enum('yes','no') NOT NULL DEFAULT 'no',
  `reason_for_editing` text NOT NULL,
  `is_rejected` enum('yes','no') NOT NULL DEFAULT 'no',
  `rejected_date` datetime NOT NULL DEFAULT current_timestamp(),
  `initiated_date` datetime NOT NULL DEFAULT current_timestamp(),
  `is_recommended` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_date` datetime NOT NULL DEFAULT current_timestamp(),
  `initiated_by` int(11) NOT NULL,
  `edit_loan_status` enum('yes','no') NOT NULL DEFAULT 'no',
  `recomended_by` int(11) DEFAULT NULL,
  `edit_by` int(11) DEFAULT NULL,
  `edit_loan_date` date NOT NULL,
  `rejected_by` int(11) DEFAULT NULL,
  `rejection_reasons` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `edit_loan`
--

INSERT INTO `edit_loan` (`edit_loan_id`, `loan_id`, `old_loan_number`, `new_loan_number`, `is_initiated`, `reason_for_editing`, `is_rejected`, `rejected_date`, `initiated_date`, `is_recommended`, `recomended_date`, `initiated_by`, `edit_loan_status`, `recomended_by`, `edit_by`, `edit_loan_date`, `rejected_by`, `rejection_reasons`) VALUES
(1, 1601, 'SB0038/2023', 'SB0038/2023', 'yes', 'SB0038/2023', 'no', '2023-09-07 00:00:00', '2023-09-07 00:00:00', 'yes', '2023-09-10 00:00:00', 7, 'yes', 7, 7, '2023-09-10', NULL, ''),
(2, 1596, 'GSMELLIN054/20222', 'GSMELLIN054/20222', 'yes', 'GSMELLIN054/20222', 'no', '2023-09-07 00:00:00', '2023-09-07 00:00:00', 'yes', '2023-09-10 00:00:00', 7, 'yes', 7, 7, '2023-09-10', NULL, ''),
(3, 1603, 'ISME0079/2023', 'ISME0079/2023', 'yes', 'ISME0079/2023', 'no', '2023-09-10 00:00:00', '2023-09-10 00:00:00', 'yes', '2023-09-10 00:00:00', 7, 'yes', 7, 7, '2023-09-10', NULL, ''),
(4, 1848, 'GSMELLIN02/2022', '', 'yes', '', 'no', '2023-11-05 00:00:00', '2023-11-06 00:00:00', 'yes', '2023-11-06 00:00:00', 7, 'yes', 7, 7, '2023-11-06', NULL, ''),
(5, 904, 'SME000137/23', 'ISME0006/2023', 'yes', '', 'no', '2023-12-08 00:00:00', '2023-12-08 00:00:00', 'no', '2023-12-08 09:22:12', 76, 'no', NULL, NULL, '0000-00-00', NULL, '');

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `Firstname` varchar(50) NOT NULL,
  `Middlename` varchar(50) DEFAULT NULL,
  `Lastname` varchar(50) NOT NULL,
  `Gender` enum('MALE','FEMALE','OTHER') NOT NULL,
  `DateOfBirth` date NOT NULL,
  `EmailAddress` varchar(60) NOT NULL,
  `PhoneNumber` varchar(15) NOT NULL,
  `AddressLine1` varchar(250) DEFAULT NULL,
  `AddressLine2` varchar(250) DEFAULT NULL,
  `Province` varchar(200) NOT NULL,
  `City` varchar(200) NOT NULL,
  `Country` varchar(2) NOT NULL,
  `Role` int(11) NOT NULL,
  `BranchCode` varchar(200) NOT NULL,
  `Branch` int(11) DEFAULT NULL,
  `EmploymentStatus` enum('CURRENT','FORMER') NOT NULL DEFAULT 'CURRENT',
  `profile_photo` varchar(200) NOT NULL DEFAULT 'avatar-3.png',
  `LastUpdatedOn` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `CreatedOn` timestamp NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`id`, `Firstname`, `Middlename`, `Lastname`, `Gender`, `DateOfBirth`, `EmailAddress`, `PhoneNumber`, `AddressLine1`, `AddressLine2`, `Province`, `City`, `Country`, `Role`, `BranchCode`, `Branch`, `EmploymentStatus`, `profile_photo`, `LastUpdatedOn`, `CreatedOn`, `system_date`) VALUES
(1, 'Chipasha', '', 'Daka', 'MALE', '1990-12-25', '<EMAIL>', '+260 966793334', '<p>Zambia</p>', '<p>zambia</p>\r\n', 'Lusaka', 'Lusaka', 'ZM', 1, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-03-02 15:29:31', '2024-12-25 04:17:34', NULL),
(72, 'Administrator', '', '(superuser)', 'MALE', '2222-01-02', '<EMAIL>', '0998225500', '<p>w2</p>', '<p>w2</p>\r\n', 'blkantyre', 'blantyre', 'UK', 1, '7042', NULL, 'CURRENT', 'avatar-3.png', '2023-11-08 12:19:36', '2022-11-24 12:20:07', NULL),
(138, 'Philip', '', 'Bwembya', 'MALE', '1980-05-08', '<EMAIL>', '0962000978', '<p>House number 17 Zambezi Road Riverside, Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 1, '89', NULL, 'CURRENT', 'avatar-3.png', '2024-12-27 14:03:18', '2024-12-27 14:03:18', NULL),
(139, 'Paul', '', 'Kalubale', 'MALE', '1985-10-12', '<EMAIL>', '0967998648', '<p>Plot 29 Kalulushi</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 1, '89', NULL, 'CURRENT', 'avatar-3.png', '2024-12-27 14:05:34', '2024-12-27 14:05:34', NULL),
(140, 'Mutinta', '', 'Lunda', 'MALE', '1988-10-29', '<EMAIL>', '0967880912', '<p>Avondale, Lusaka</p>', '', 'Lusaka', 'Lusaka', 'ZM', 1, '89', NULL, 'CURRENT', 'avatar-3.png', '2024-12-27 14:08:33', '2024-12-27 14:08:33', NULL),
(141, 'Misheck', 'M', 'Kamuloni', 'MALE', '2025-04-10', '<EMAIL>', '0994099461', '<p>kaya</p>', '<p>kaya</p>\r\n', 'LIlongwe', 'Lilongwe', 'ZM', 1, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-10 06:34:46', '2025-04-10 06:34:46', NULL),
(142, 'Philip', '', 'Bwembya', 'MALE', '1986-03-17', '<EMAIL>', '260962000978', '<p>Fundit Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 45, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-26 17:58:38', '2025-04-26 17:58:38', NULL),
(143, 'Paul', '', 'Kalubale', 'MALE', '1988-05-26', '<EMAIL>', '260967998648', '<p>Fundit Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 30, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-26 18:01:31', '2025-04-26 18:01:31', NULL),
(144, 'Mutinta', 'Mateyo', 'Lunda', 'MALE', '1988-10-29', '<EMAIL>', '260966333888', '<p>Fundit, Unit 11 Soho Park, Kanyanta Avenue, Parklands, Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 44, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-29 20:01:57', '2025-04-29 20:01:57', NULL),
(145, 'Lubumbe', '', 'Matani', 'MALE', '1985-12-13', '<EMAIL>', '260979507050', '<p>Fundit, Unit 11 Soho Park, Kanyanta Avenue, Parklands, Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 41, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-29 20:05:18', '2025-04-29 20:05:18', NULL),
(146, 'Curtis', '', 'Banda', 'MALE', '1992-11-28', '<EMAIL>', '260978114381', '<p>Fundit, Unit 11 Soho Park, Kanyanta Avenue, Parklands, Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 29, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-04-29 20:12:46', '2025-04-29 20:12:46', NULL),
(147, 'Chileshe', '', 'Mwamba', 'MALE', '1993-09-06', '<EMAIL>', '+260968519833', '<p>26-3rd Nkana West, Kitwe</p>', '', 'Copperbelt', 'Kitwe', 'ZM', 46, '89', NULL, 'CURRENT', 'avatar-3.png', '2025-06-17 04:59:20', '2025-06-17 04:59:20', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `file_folders`
--

CREATE TABLE `file_folders` (
  `id` int(11) NOT NULL,
  `folder_name` varchar(100) NOT NULL COMMENT 'Name of the folder',
  `parent_folder_id` int(11) DEFAULT NULL COMMENT 'ID of parent folder (NULL for root folders)',
  `owner_id` int(11) NOT NULL COMMENT 'User ID who owns the folder',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'Whether the folder is public (1) or private (0)',
  `date_created` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Date and time of creation',
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Date and time of last modification',
  `description` text DEFAULT NULL COMMENT 'Optional description of the folder',
  `is_system_folder` enum('No','Yes') NOT NULL DEFAULT 'No'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `file_folders`
--

INSERT INTO `file_folders` (`id`, `folder_name`, `parent_folder_id`, `owner_id`, `is_public`, `date_created`, `date_modified`, `description`, `is_system_folder`) VALUES
(10, 'Loan Files', NULL, 72, 1, '2025-04-19 15:31:56', '2025-04-19 15:31:56', NULL, 'Yes'),
(11, 'Person KYC Files', NULL, 72, 1, '2025-04-19 15:31:56', '2025-04-19 15:31:56', NULL, 'Yes'),
(12, 'Corporate Files', NULL, 72, 1, '2025-04-19 15:31:56', '2025-04-19 15:31:56', NULL, 'Yes'),
(41, 'Kamuloni', NULL, 72, 1, '2025-04-29 08:33:40', '2025-04-29 08:33:40', 'This is my folder', 'No'),
(42, 'Infocus Techlogy', 12, 1, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 'Corporate Customer folder', 'No'),
(43, 'Infocus Techlogy Corporate files', 42, 1, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 'Corporate files folder', 'No'),
(44, 'Infocus Techlogy Shareholders files', 42, 1, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 'Corporate shareholders files folder', 'No'),
(45, 'Fundit', 12, 2, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 'Corporate Customer folder', 'No'),
(46, 'Fundit Corporate files', 45, 2, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 'Corporate files folder', 'No'),
(47, 'Fundit Shareholders files', 45, 2, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 'Corporate shareholders files folder', 'No'),
(48, 'NBT0001-25', 10, 1, 1, '2025-05-05 14:56:26', '2025-05-05 14:56:26', 'Loan folder', 'No'),
(49, 'NBT0001-25 loan files', 48, 1, 1, '2025-05-05 14:56:26', '2025-05-05 14:56:26', 'Loan files folder', 'No'),
(50, 'NBT0001-25 loan collateral files', 48, 1, 1, '2025-05-05 14:56:26', '2025-05-05 14:56:26', 'Loan collateral files folder', 'No'),
(51, 'Fundit General Files', NULL, 72, 1, '2025-05-05 15:02:13', '2025-05-05 15:02:13', 'hgsdgsdg', 'No'),
(52, 'Infovits', 12, 3, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 'Corporate Customer folder', 'No'),
(53, 'Infovits Corporate files', 52, 3, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 'Corporate files folder', 'No'),
(54, 'Infovits Shareholders files', 52, 3, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 'Corporate shareholders files folder', 'No'),
(55, 'Infovits', 12, 5, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 'Corporate Customer folder', 'No'),
(56, 'Infovits Corporate files', 55, 5, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 'Corporate files folder', 'No'),
(57, 'Infovits Shareholders files', 55, 5, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 'Corporate shareholders files folder', 'No'),
(58, 'Infovits', 12, 7, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 'Corporate Customer folder', 'No'),
(59, 'Infovits Corporate files', 58, 7, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 'Corporate files folder', 'No'),
(60, 'Infovits Shareholders files', 58, 7, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 'Corporate shareholders files folder', 'No'),
(61, 'Infovits', 12, 8, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 'Corporate Customer folder', 'No'),
(62, 'Infovits Corporate files', 61, 8, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 'Corporate files folder', 'No'),
(63, 'Infovits Shareholders files', 61, 8, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 'Corporate shareholders files folder', 'No'),
(64, 'NBT0002-25', 10, 2, 1, '2025-05-08 11:44:05', '2025-05-08 11:44:05', 'Loan folder', 'No'),
(65, 'NBT0002-25 loan files', 64, 2, 1, '2025-05-08 11:44:05', '2025-05-08 11:44:05', 'Loan files folder', 'No'),
(66, 'NBT0002-25 loan collateral files', 64, 2, 1, '2025-05-08 11:44:05', '2025-05-08 11:44:05', 'Loan collateral files folder', 'No'),
(67, 'KENPORT LOGISTICS', 12, 1, 1, '2025-05-13 08:50:01', '2025-05-13 08:50:01', 'Corporate Customer folder', 'No'),
(68, 'KENPORT LOGISTICS Corporate files', 67, 1, 1, '2025-05-13 08:50:01', '2025-05-13 08:50:01', 'Corporate files folder', 'No'),
(69, 'KENPORT LOGISTICS Shareholders files', 67, 1, 1, '2025-05-13 08:50:01', '2025-05-13 08:50:01', 'Corporate shareholders files folder', 'No'),
(70, 'Perez Investments Limited', 12, 2, 1, '2025-05-15 13:44:43', '2025-05-15 13:44:43', 'Corporate Customer folder', 'No'),
(71, 'Perez Investments Limited Corporate files', 70, 2, 1, '2025-05-15 13:44:43', '2025-05-15 13:44:43', 'Corporate files folder', 'No'),
(72, 'Perez Investments Limited Shareholders files', 70, 2, 1, '2025-05-15 13:44:43', '2025-05-15 13:44:43', 'Corporate shareholders files folder', 'No'),
(73, 'Mopani Copper Mines PLC', 12, 8, 1, '2025-05-16 10:24:25', '2025-05-16 10:24:25', 'Corporate Customer folder', 'No'),
(74, 'Mopani Copper Mines PLC Corporate files', 73, 8, 1, '2025-05-16 10:24:25', '2025-05-16 10:24:25', 'Corporate files folder', 'No'),
(75, 'Mopani Copper Mines PLC Shareholders files', 73, 8, 1, '2025-05-16 10:24:25', '2025-05-16 10:24:25', 'Corporate shareholders files folder', 'No'),
(76, 'INVODis0001-25', 10, 1, 1, '2025-05-16 10:26:21', '2025-05-16 10:26:21', 'Loan folder', 'No'),
(77, 'INVODis0001-25 loan files', 76, 1, 1, '2025-05-16 10:26:21', '2025-05-16 10:26:21', 'Loan files folder', 'No'),
(78, 'INVODis0001-25 loan collateral files', 76, 1, 1, '2025-05-16 10:26:21', '2025-05-16 10:26:21', 'Loan collateral files folder', 'No'),
(79, 'QUORN FLOORING LIMITED', 12, 10, 1, '2025-05-21 08:28:05', '2025-05-21 08:28:05', 'Corporate Customer folder', 'No'),
(80, 'QUORN FLOORING LIMITED Corporate files', 79, 10, 1, '2025-05-21 08:28:05', '2025-05-21 08:28:05', 'Corporate files folder', 'No'),
(81, 'QUORN FLOORING LIMITED Shareholders files', 79, 10, 1, '2025-05-21 08:28:05', '2025-05-21 08:28:05', 'Corporate shareholders files folder', 'No'),
(82, 'INVODis0002-25', 10, 2, 1, '2025-05-21 08:32:21', '2025-05-21 08:32:21', 'Loan folder', 'No'),
(83, 'INVODis0002-25 loan files', 82, 2, 1, '2025-05-21 08:32:21', '2025-05-21 08:32:21', 'Loan files folder', 'No'),
(84, 'INVODis0002-25 loan collateral files', 82, 2, 1, '2025-05-21 08:32:21', '2025-05-21 08:32:21', 'Loan collateral files folder', 'No'),
(85, 'Spekport Limited', 12, 12, 1, '2025-05-21 09:13:18', '2025-05-21 09:13:18', 'Corporate Customer folder', 'No'),
(86, 'Spekport Limited Corporate files', 85, 12, 1, '2025-05-21 09:13:18', '2025-05-21 09:13:18', 'Corporate files folder', 'No'),
(87, 'Spekport Limited Shareholders files', 85, 12, 1, '2025-05-21 09:13:18', '2025-05-21 09:13:18', 'Corporate shareholders files folder', 'No'),
(88, 'INVODis0003-25', 10, 3, 1, '2025-05-21 10:22:18', '2025-05-21 10:22:18', 'Loan folder', 'No'),
(89, 'INVODis0003-25 loan files', 88, 3, 1, '2025-05-21 10:22:18', '2025-05-21 10:22:18', 'Loan files folder', 'No'),
(90, 'INVODis0003-25 loan collateral files', 88, 3, 1, '2025-05-21 10:22:18', '2025-05-21 10:22:18', 'Loan collateral files folder', 'No'),
(91, 'INVODis0004-25', 10, 4, 1, '2025-05-21 10:27:49', '2025-05-21 10:27:49', 'Loan folder', 'No'),
(92, 'INVODis0004-25 loan files', 91, 4, 1, '2025-05-21 10:27:49', '2025-05-21 10:27:49', 'Loan files folder', 'No'),
(93, 'INVODis0004-25 loan collateral files', 91, 4, 1, '2025-05-21 10:27:49', '2025-05-21 10:27:49', 'Loan collateral files folder', 'No'),
(94, 'Inkaba Zambia Limited', 12, 14, 1, '2025-05-21 13:18:23', '2025-05-21 13:18:23', 'Corporate Customer folder', 'No'),
(95, 'Inkaba Zambia Limited Corporate files', 94, 14, 1, '2025-05-21 13:18:23', '2025-05-21 13:18:23', 'Corporate files folder', 'No'),
(96, 'Inkaba Zambia Limited Shareholders files', 94, 14, 1, '2025-05-21 13:18:23', '2025-05-21 13:18:23', 'Corporate shareholders files folder', 'No'),
(97, 'INVODis0005-25', 10, 5, 1, '2025-05-21 13:27:16', '2025-05-21 13:27:16', 'Loan folder', 'No'),
(98, 'INVODis0005-25 loan files', 97, 5, 1, '2025-05-21 13:27:16', '2025-05-21 13:27:16', 'Loan files folder', 'No'),
(99, 'INVODis0005-25 loan collateral files', 97, 5, 1, '2025-05-21 13:27:16', '2025-05-21 13:27:16', 'Loan collateral files folder', 'No'),
(100, 'WHEALS CONSTRUCTION LIMITED', 12, 15, 1, '2025-05-23 08:28:40', '2025-05-23 08:28:40', 'Corporate Customer folder', 'No'),
(101, 'WHEALS CONSTRUCTION LIMITED Corporate files', 100, 15, 1, '2025-05-23 08:28:40', '2025-05-23 08:28:40', 'Corporate files folder', 'No'),
(102, 'WHEALS CONSTRUCTION LIMITED Shareholders files', 100, 15, 1, '2025-05-23 08:28:40', '2025-05-23 08:28:40', 'Corporate shareholders files folder', 'No'),
(103, 'Kuyesa', 12, 17, 1, '2025-05-26 16:10:14', '2025-05-26 16:10:14', 'Corporate Customer folder', 'No'),
(104, 'Kuyesa Corporate files', 103, 17, 1, '2025-05-26 16:10:14', '2025-05-26 16:10:14', 'Corporate files folder', 'No'),
(105, 'Kuyesa Shareholders files', 103, 17, 1, '2025-05-26 16:10:14', '2025-05-26 16:10:14', 'Corporate shareholders files folder', 'No'),
(106, 'Kansanshi Copper Mines Plc', 12, 18, 1, '2025-05-27 09:33:09', '2025-05-27 09:33:09', 'Corporate Customer folder', 'No'),
(107, 'Kansanshi Copper Mines Plc Corporate files', 106, 18, 1, '2025-05-27 09:33:09', '2025-05-27 09:33:09', 'Corporate files folder', 'No'),
(108, 'Kansanshi Copper Mines Plc Shareholders files', 106, 18, 1, '2025-05-27 09:33:09', '2025-05-27 09:33:09', 'Corporate shareholders files folder', 'No'),
(109, 'RGPM Chemicals Limited', 12, 21, 1, '2025-05-27 09:48:35', '2025-05-27 09:48:35', 'Corporate Customer folder', 'No'),
(110, 'RGPM Chemicals Limited Corporate files', 109, 21, 1, '2025-05-27 09:48:35', '2025-05-27 09:48:35', 'Corporate files folder', 'No'),
(111, 'RGPM Chemicals Limited Shareholders files', 109, 21, 1, '2025-05-27 09:48:35', '2025-05-27 09:48:35', 'Corporate shareholders files folder', 'No'),
(112, 'Flabene Engineering Limitted', 12, 24, 1, '2025-05-27 13:10:55', '2025-05-27 13:10:55', 'Corporate Customer folder', 'No'),
(113, 'Flabene Engineering Limitted Corporate files', 112, 24, 1, '2025-05-27 13:10:55', '2025-05-27 13:10:55', 'Corporate files folder', 'No'),
(114, 'Flabene Engineering Limitted Shareholders files', 112, 24, 1, '2025-05-27 13:10:55', '2025-05-27 13:10:55', 'Corporate shareholders files folder', 'No'),
(115, 'OrderFi0001-25', 10, 6, 1, '2025-05-27 13:31:43', '2025-05-27 13:31:43', 'Loan folder', 'No'),
(116, 'OrderFi0001-25 loan files', 115, 6, 1, '2025-05-27 13:31:43', '2025-05-27 13:31:43', 'Loan files folder', 'No'),
(117, 'OrderFi0001-25 loan collateral files', 115, 6, 1, '2025-05-27 13:31:43', '2025-05-27 13:31:43', 'Loan collateral files folder', 'No'),
(118, 'Lumwana Copper Mines', 12, 28, 1, '2025-05-27 14:07:36', '2025-05-27 14:07:36', 'Corporate Customer folder', 'No'),
(119, 'Lumwana Copper Mines Corporate files', 118, 28, 1, '2025-05-27 14:07:36', '2025-05-27 14:07:36', 'Corporate files folder', 'No'),
(120, 'Lumwana Copper Mines Shareholders files', 118, 28, 1, '2025-05-27 14:07:36', '2025-05-27 14:07:36', 'Corporate shareholders files folder', 'No'),
(121, 'Fox Foundry', 12, 29, 1, '2025-05-28 10:21:40', '2025-05-28 10:21:40', 'Corporate Customer folder', 'No'),
(122, 'Fox Foundry Corporate files', 121, 29, 1, '2025-05-28 10:21:40', '2025-05-28 10:21:40', 'Corporate files folder', 'No'),
(123, 'Fox Foundry Shareholders files', 121, 29, 1, '2025-05-28 10:21:40', '2025-05-28 10:21:40', 'Corporate shareholders files folder', 'No'),
(124, 'Shamuchisha', 12, 30, 1, '2025-05-28 10:32:29', '2025-05-28 10:32:29', 'Corporate Customer folder', 'No'),
(125, 'Shamuchisha Corporate files', 124, 30, 1, '2025-05-28 10:32:29', '2025-05-28 10:32:29', 'Corporate files folder', 'No'),
(126, 'Shamuchisha Shareholders files', 124, 30, 1, '2025-05-28 10:32:29', '2025-05-28 10:32:29', 'Corporate shareholders files folder', 'No'),
(127, 'Chimwe Investments Limited', 12, 31, 1, '2025-05-28 11:09:57', '2025-05-28 11:09:57', 'Corporate Customer folder', 'No'),
(128, 'Chimwe Investments Limited Corporate files', 127, 31, 1, '2025-05-28 11:09:57', '2025-05-28 11:09:57', 'Corporate files folder', 'No'),
(129, 'Chimwe Investments Limited Shareholders files', 127, 31, 1, '2025-05-28 11:09:57', '2025-05-28 11:09:57', 'Corporate shareholders files folder', 'No'),
(130, 'Devourge', 12, 33, 1, '2025-05-28 13:45:08', '2025-05-28 13:45:08', 'Corporate Customer folder', 'No'),
(131, 'Devourge Corporate files', 130, 33, 1, '2025-05-28 13:45:08', '2025-05-28 13:45:08', 'Corporate files folder', 'No'),
(132, 'Devourge Shareholders files', 130, 33, 1, '2025-05-28 13:45:08', '2025-05-28 13:45:08', 'Corporate shareholders files folder', 'No'),
(133, 'Hemiward Investments Limited', 12, 34, 1, '2025-05-28 14:00:40', '2025-05-28 14:00:40', 'Corporate Customer folder', 'No'),
(134, 'Hemiward Investments Limited Corporate files', 133, 34, 1, '2025-05-28 14:00:40', '2025-05-28 14:00:40', 'Corporate files folder', 'No'),
(135, 'Hemiward Investments Limited Shareholders files', 133, 34, 1, '2025-05-28 14:00:40', '2025-05-28 14:00:40', 'Corporate shareholders files folder', 'No'),
(136, 'Valve Corp Zambia Limited', 12, 35, 1, '2025-05-28 14:09:12', '2025-05-28 14:09:12', 'Corporate Customer folder', 'No'),
(137, 'Valve Corp Zambia Limited Corporate files', 136, 35, 1, '2025-05-28 14:09:12', '2025-05-28 14:09:12', 'Corporate files folder', 'No'),
(138, 'Valve Corp Zambia Limited Shareholders files', 136, 35, 1, '2025-05-28 14:09:12', '2025-05-28 14:09:12', 'Corporate shareholders files folder', 'No'),
(139, 'Westmead Limited', 12, 38, 1, '2025-06-02 09:59:16', '2025-06-02 09:59:16', 'Corporate Customer folder', 'No'),
(140, 'Westmead Limited Corporate files', 139, 38, 1, '2025-06-02 09:59:16', '2025-06-02 09:59:16', 'Corporate files folder', 'No'),
(141, 'Westmead Limited Shareholders files', 139, 38, 1, '2025-06-02 09:59:16', '2025-06-02 09:59:16', 'Corporate shareholders files folder', 'No'),
(142, 'SP100001-25', 10, 7, 1, '2025-06-03 12:33:16', '2025-06-03 12:33:16', 'Loan folder', 'No'),
(143, 'SP100001-25 loan files', 142, 7, 1, '2025-06-03 12:33:16', '2025-06-03 12:33:16', 'Loan files folder', 'No'),
(144, 'SP100001-25 loan collateral files', 142, 7, 1, '2025-06-03 12:33:16', '2025-06-03 12:33:16', 'Loan collateral files folder', 'No'),
(145, 'INVODis0006-25', 10, 8, 1, '2025-06-04 07:25:56', '2025-06-04 07:25:56', 'Loan folder', 'No'),
(146, 'INVODis0006-25 loan files', 145, 8, 1, '2025-06-04 07:25:56', '2025-06-04 07:25:56', 'Loan files folder', 'No'),
(147, 'INVODis0006-25 loan collateral files', 145, 8, 1, '2025-06-04 07:25:56', '2025-06-04 07:25:56', 'Loan collateral files folder', 'No'),
(148, 'INVODis0007-25', 10, 9, 1, '2025-06-04 09:58:27', '2025-06-04 09:58:27', 'Loan folder', 'No'),
(149, 'INVODis0007-25 loan files', 148, 9, 1, '2025-06-04 09:58:27', '2025-06-04 09:58:27', 'Loan files folder', 'No'),
(150, 'INVODis0007-25 loan collateral files', 148, 9, 1, '2025-06-04 09:58:27', '2025-06-04 09:58:27', 'Loan collateral files folder', 'No'),
(151, 'Brassco Limited', 12, 39, 1, '2025-06-04 10:18:26', '2025-06-04 10:18:26', 'Corporate Customer folder', 'No'),
(152, 'Brassco Limited Corporate files', 151, 39, 1, '2025-06-04 10:18:26', '2025-06-04 10:18:26', 'Corporate files folder', 'No'),
(153, 'Brassco Limited Shareholders files', 151, 39, 1, '2025-06-04 10:18:26', '2025-06-04 10:18:26', 'Corporate shareholders files folder', 'No'),
(154, 'Brassco Limited', 12, 42, 1, '2025-06-04 10:19:01', '2025-06-04 10:19:01', 'Corporate Customer folder', 'No'),
(155, 'Brassco Limited Corporate files', 154, 42, 1, '2025-06-04 10:19:01', '2025-06-04 10:19:01', 'Corporate files folder', 'No'),
(156, 'Brassco Limited Shareholders files', 154, 42, 1, '2025-06-04 10:19:01', '2025-06-04 10:19:01', 'Corporate shareholders files folder', 'No'),
(157, 'INVODis0008-25', 10, 10, 1, '2025-06-04 10:25:22', '2025-06-04 10:25:22', 'Loan folder', 'No'),
(158, 'INVODis0008-25 loan files', 157, 10, 1, '2025-06-04 10:25:22', '2025-06-04 10:25:22', 'Loan files folder', 'No'),
(159, 'INVODis0008-25 loan collateral files', 157, 10, 1, '2025-06-04 10:25:22', '2025-06-04 10:25:22', 'Loan collateral files folder', 'No'),
(160, 'BIG Brands  Chain f  General Suppliers', 12, 43, 1, '2025-06-06 11:33:57', '2025-06-06 11:33:57', 'Corporate Customer folder', 'No'),
(161, 'BIG Brands  Chain f  General Suppliers Corporate files', 160, 43, 1, '2025-06-06 11:33:57', '2025-06-06 11:33:57', 'Corporate files folder', 'No'),
(162, 'BIG Brands  Chain f  General Suppliers Shareholders files', 160, 43, 1, '2025-06-06 11:33:57', '2025-06-06 11:33:57', 'Corporate shareholders files folder', 'No'),
(163, 'ZAMBIA BREWERIES PLC', 12, 45, 1, '2025-06-09 09:10:57', '2025-06-09 09:10:57', 'Corporate Customer folder', 'No'),
(164, 'ZAMBIA BREWERIES PLC Corporate files', 163, 45, 1, '2025-06-09 09:10:57', '2025-06-09 09:10:57', 'Corporate files folder', 'No'),
(165, 'ZAMBIA BREWERIES PLC Shareholders files', 163, 45, 1, '2025-06-09 09:10:57', '2025-06-09 09:10:57', 'Corporate shareholders files folder', 'No'),
(166, 'INVODis0009-25', 10, 11, 1, '2025-06-09 09:21:11', '2025-06-09 09:21:11', 'Loan folder', 'No'),
(167, 'INVODis0009-25 loan files', 166, 11, 1, '2025-06-09 09:21:11', '2025-06-09 09:21:11', 'Loan files folder', 'No'),
(168, 'INVODis0009-25 loan collateral files', 166, 11, 1, '2025-06-09 09:21:11', '2025-06-09 09:21:11', 'Loan collateral files folder', 'No'),
(169, 'INVODis00010-25', 10, 12, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 'Loan folder', 'No'),
(170, 'INVODis00010-25 loan files', 169, 12, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 'Loan files folder', 'No'),
(171, 'INVODis00010-25 loan collateral files', 169, 12, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 'Loan collateral files folder', 'No'),
(172, 'OrderFi0002-25', 10, 13, 1, '2025-06-09 09:27:20', '2025-06-09 09:27:20', 'Loan folder', 'No'),
(173, 'OrderFi0002-25 loan files', 172, 13, 1, '2025-06-09 09:27:20', '2025-06-09 09:27:20', 'Loan files folder', 'No'),
(174, 'OrderFi0002-25 loan collateral files', 172, 13, 1, '2025-06-09 09:27:20', '2025-06-09 09:27:20', 'Loan collateral files folder', 'No'),
(175, 'Konkola Copper Mine', 12, 46, 1, '2025-06-09 09:44:10', '2025-06-09 09:44:10', 'Corporate Customer folder', 'No'),
(176, 'Konkola Copper Mine Corporate files', 175, 46, 1, '2025-06-09 09:44:10', '2025-06-09 09:44:10', 'Corporate files folder', 'No'),
(177, 'Konkola Copper Mine Shareholders files', 175, 46, 1, '2025-06-09 09:44:10', '2025-06-09 09:44:10', 'Corporate shareholders files folder', 'No'),
(178, 'INVODis00011-25', 10, 14, 1, '2025-06-09 09:53:24', '2025-06-09 09:53:24', 'Loan folder', 'No'),
(179, 'INVODis00011-25 loan files', 178, 14, 1, '2025-06-09 09:53:24', '2025-06-09 09:53:24', 'Loan files folder', 'No'),
(180, 'INVODis00011-25 loan collateral files', 178, 14, 1, '2025-06-09 09:53:24', '2025-06-09 09:53:24', 'Loan collateral files folder', 'No'),
(181, 'Riverun Enterprises Limited', 12, 47, 1, '2025-06-09 16:26:54', '2025-06-09 16:26:54', 'Corporate Customer folder', 'No'),
(182, 'Riverun Enterprises Limited Corporate files', 181, 47, 1, '2025-06-09 16:26:54', '2025-06-09 16:26:54', 'Corporate files folder', 'No'),
(183, 'Riverun Enterprises Limited Shareholders files', 181, 47, 1, '2025-06-09 16:26:54', '2025-06-09 16:26:54', 'Corporate shareholders files folder', 'No'),
(184, 'Turnkey Investments Limited', 12, 48, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 'Corporate Customer folder', 'No'),
(185, 'Turnkey Investments Limited Corporate files', 184, 48, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 'Corporate files folder', 'No'),
(186, 'Turnkey Investments Limited Shareholders files', 184, 48, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 'Corporate shareholders files folder', 'No'),
(187, 'INVODis00012-25', 10, 15, 1, '2025-06-11 09:35:09', '2025-06-11 09:35:09', 'Loan folder', 'No'),
(188, 'INVODis00012-25 loan files', 187, 15, 1, '2025-06-11 09:35:09', '2025-06-11 09:35:09', 'Loan files folder', 'No'),
(189, 'INVODis00012-25 loan collateral files', 187, 15, 1, '2025-06-11 09:35:09', '2025-06-11 09:35:09', 'Loan collateral files folder', 'No'),
(190, 'INVODis00013-25', 10, 16, 1, '2025-06-11 14:17:39', '2025-06-11 14:17:39', 'Loan folder', 'No'),
(191, 'INVODis00013-25 loan files', 190, 16, 1, '2025-06-11 14:17:39', '2025-06-11 14:17:39', 'Loan files folder', 'No'),
(192, 'INVODis00013-25 loan collateral files', 190, 16, 1, '2025-06-11 14:17:39', '2025-06-11 14:17:39', 'Loan collateral files folder', 'No'),
(193, 'INVODis00014-25', 10, 17, 1, '2025-06-11 14:21:17', '2025-06-11 14:21:17', 'Loan folder', 'No'),
(194, 'INVODis00014-25 loan files', 193, 17, 1, '2025-06-11 14:21:17', '2025-06-11 14:21:17', 'Loan files folder', 'No'),
(195, 'INVODis00014-25 loan collateral files', 193, 17, 1, '2025-06-11 14:21:17', '2025-06-11 14:21:17', 'Loan collateral files folder', 'No'),
(196, 'INVODis00015-25', 10, 18, 1, '2025-06-16 15:20:44', '2025-06-16 15:20:44', 'Loan folder', 'No'),
(197, 'INVODis00015-25 loan files', 196, 18, 1, '2025-06-16 15:20:44', '2025-06-16 15:20:44', 'Loan files folder', 'No'),
(198, 'INVODis00015-25 loan collateral files', 196, 18, 1, '2025-06-16 15:20:44', '2025-06-16 15:20:44', 'Loan collateral files folder', 'No'),
(199, 'Lubambe Copper Mine', 12, 49, 1, '2025-06-16 15:35:45', '2025-06-16 15:35:45', 'Corporate Customer folder', 'No'),
(200, 'Lubambe Copper Mine Corporate files', 199, 49, 1, '2025-06-16 15:35:45', '2025-06-16 15:35:45', 'Corporate files folder', 'No'),
(201, 'Lubambe Copper Mine Shareholders files', 199, 49, 1, '2025-06-16 15:35:45', '2025-06-16 15:35:45', 'Corporate shareholders files folder', 'No'),
(202, 'INVODis00016-25', 10, 19, 1, '2025-06-16 15:45:50', '2025-06-16 15:45:50', 'Loan folder', 'No'),
(203, 'INVODis00016-25 loan files', 202, 19, 1, '2025-06-16 15:45:50', '2025-06-16 15:45:50', 'Loan files folder', 'No'),
(204, 'INVODis00016-25 loan collateral files', 202, 19, 1, '2025-06-16 15:45:50', '2025-06-16 15:45:50', 'Loan collateral files folder', 'No'),
(205, 'INVODis00017-25', 10, 20, 1, '2025-06-16 16:08:39', '2025-06-16 16:08:39', 'Loan folder', 'No'),
(206, 'INVODis00017-25 loan files', 205, 20, 1, '2025-06-16 16:08:39', '2025-06-16 16:08:39', 'Loan files folder', 'No'),
(207, 'INVODis00017-25 loan collateral files', 205, 20, 1, '2025-06-16 16:08:39', '2025-06-16 16:08:39', 'Loan collateral files folder', 'No'),
(208, 'INVODis00018-25', 10, 21, 1, '2025-06-17 09:02:48', '2025-06-17 09:02:48', 'Loan folder', 'No'),
(209, 'INVODis00018-25 loan files', 208, 21, 1, '2025-06-17 09:02:48', '2025-06-17 09:02:48', 'Loan files folder', 'No'),
(210, 'INVODis00018-25 loan collateral files', 208, 21, 1, '2025-06-17 09:02:48', '2025-06-17 09:02:48', 'Loan collateral files folder', 'No'),
(211, 'Themfex', 12, 50, 1, '2025-06-17 09:23:40', '2025-06-17 09:23:40', 'Corporate Customer folder', 'No'),
(212, 'Themfex Corporate files', 211, 50, 1, '2025-06-17 09:23:40', '2025-06-17 09:23:40', 'Corporate files folder', 'No'),
(213, 'Themfex Shareholders files', 211, 50, 1, '2025-06-17 09:23:40', '2025-06-17 09:23:40', 'Corporate shareholders files folder', 'No'),
(214, 'Orica Zambia Limited', 12, 51, 1, '2025-06-17 09:35:32', '2025-06-17 09:35:32', 'Corporate Customer folder', 'No'),
(215, 'Orica Zambia Limited Corporate files', 214, 51, 1, '2025-06-17 09:35:32', '2025-06-17 09:35:32', 'Corporate files folder', 'No'),
(216, 'Orica Zambia Limited Shareholders files', 214, 51, 1, '2025-06-17 09:35:32', '2025-06-17 09:35:32', 'Corporate shareholders files folder', 'No'),
(217, 'INVODis00019-25', 10, 22, 1, '2025-06-17 09:37:37', '2025-06-17 09:37:37', 'Loan folder', 'No'),
(218, 'INVODis00019-25 loan files', 217, 22, 1, '2025-06-17 09:37:37', '2025-06-17 09:37:37', 'Loan files folder', 'No'),
(219, 'INVODis00019-25 loan collateral files', 217, 22, 1, '2025-06-17 09:37:37', '2025-06-17 09:37:37', 'Loan collateral files folder', 'No'),
(220, 'INVODis00020-25', 10, 23, 1, '2025-06-17 09:44:15', '2025-06-17 09:44:15', 'Loan folder', 'No'),
(221, 'INVODis00020-25 loan files', 220, 23, 1, '2025-06-17 09:44:15', '2025-06-17 09:44:15', 'Loan files folder', 'No'),
(222, 'INVODis00020-25 loan collateral files', 220, 23, 1, '2025-06-17 09:44:15', '2025-06-17 09:44:15', 'Loan collateral files folder', 'No'),
(223, 'INVODis00021-25', 10, 24, 1, '2025-06-17 11:22:43', '2025-06-17 11:22:43', 'Loan folder', 'No'),
(224, 'INVODis00021-25 loan files', 223, 24, 1, '2025-06-17 11:22:43', '2025-06-17 11:22:43', 'Loan files folder', 'No'),
(225, 'INVODis00021-25 loan collateral files', 223, 24, 1, '2025-06-17 11:22:43', '2025-06-17 11:22:43', 'Loan collateral files folder', 'No'),
(226, 'INVODis00022-25', 10, 25, 1, '2025-06-18 13:38:05', '2025-06-18 13:38:05', 'Loan folder', 'No'),
(227, 'INVODis00022-25 loan files', 226, 25, 1, '2025-06-18 13:38:05', '2025-06-18 13:38:05', 'Loan files folder', 'No'),
(228, 'INVODis00022-25 loan collateral files', 226, 25, 1, '2025-06-18 13:38:05', '2025-06-18 13:38:05', 'Loan collateral files folder', 'No'),
(229, 'INVODis00023-25', 10, 26, 1, '2025-06-18 13:40:09', '2025-06-18 13:40:09', 'Loan folder', 'No'),
(230, 'INVODis00023-25 loan files', 229, 26, 1, '2025-06-18 13:40:09', '2025-06-18 13:40:09', 'Loan files folder', 'No'),
(231, 'INVODis00023-25 loan collateral files', 229, 26, 1, '2025-06-18 13:40:09', '2025-06-18 13:40:09', 'Loan collateral files folder', 'No'),
(232, 'INVODis00024-25', 10, 27, 1, '2025-06-18 13:42:06', '2025-06-18 13:42:06', 'Loan folder', 'No'),
(233, 'INVODis00024-25 loan files', 232, 27, 1, '2025-06-18 13:42:06', '2025-06-18 13:42:06', 'Loan files folder', 'No'),
(234, 'INVODis00024-25 loan collateral files', 232, 27, 1, '2025-06-18 13:42:06', '2025-06-18 13:42:06', 'Loan collateral files folder', 'No'),
(235, 'Kagem Gemfields Limited', 12, 52, 1, '2025-06-18 14:14:43', '2025-06-18 14:14:43', 'Corporate Customer folder', 'No'),
(236, 'Kagem Gemfields Limited Corporate files', 235, 52, 1, '2025-06-18 14:14:43', '2025-06-18 14:14:43', 'Corporate files folder', 'No'),
(237, 'Kagem Gemfields Limited Shareholders files', 235, 52, 1, '2025-06-18 14:14:43', '2025-06-18 14:14:43', 'Corporate shareholders files folder', 'No'),
(238, 'INVODis00025-25', 10, 28, 1, '2025-06-18 14:16:46', '2025-06-18 14:16:46', 'Loan folder', 'No'),
(239, 'INVODis00025-25 loan files', 238, 28, 1, '2025-06-18 14:16:46', '2025-06-18 14:16:46', 'Loan files folder', 'No'),
(240, 'INVODis00025-25 loan collateral files', 238, 28, 1, '2025-06-18 14:16:46', '2025-06-18 14:16:46', 'Loan collateral files folder', 'No'),
(241, 'INVODis00026-25', 10, 29, 1, '2025-06-18 14:20:50', '2025-06-18 14:20:50', 'Loan folder', 'No'),
(242, 'INVODis00026-25 loan files', 241, 29, 1, '2025-06-18 14:20:50', '2025-06-18 14:20:50', 'Loan files folder', 'No'),
(243, 'INVODis00026-25 loan collateral files', 241, 29, 1, '2025-06-18 14:20:50', '2025-06-18 14:20:50', 'Loan collateral files folder', 'No'),
(244, 'INVODis00027-25', 10, 30, 1, '2025-06-18 14:27:19', '2025-06-18 14:27:19', 'Loan folder', 'No'),
(245, 'INVODis00027-25 loan files', 244, 30, 1, '2025-06-18 14:27:19', '2025-06-18 14:27:19', 'Loan files folder', 'No'),
(246, 'INVODis00027-25 loan collateral files', 244, 30, 1, '2025-06-18 14:27:19', '2025-06-18 14:27:19', 'Loan collateral files folder', 'No'),
(247, 'INVODis00028-25', 10, 31, 1, '2025-06-18 14:31:13', '2025-06-18 14:31:13', 'Loan folder', 'No'),
(248, 'INVODis00028-25 loan files', 247, 31, 1, '2025-06-18 14:31:13', '2025-06-18 14:31:13', 'Loan files folder', 'No'),
(249, 'INVODis00028-25 loan collateral files', 247, 31, 1, '2025-06-18 14:31:13', '2025-06-18 14:31:13', 'Loan collateral files folder', 'No'),
(250, 'INVODis00029-25', 10, 32, 1, '2025-06-18 14:33:13', '2025-06-18 14:33:13', 'Loan folder', 'No'),
(251, 'INVODis00029-25 loan files', 250, 32, 1, '2025-06-18 14:33:13', '2025-06-18 14:33:13', 'Loan files folder', 'No'),
(252, 'INVODis00029-25 loan collateral files', 250, 32, 1, '2025-06-18 14:33:13', '2025-06-18 14:33:13', 'Loan collateral files folder', 'No'),
(253, 'INVODis00030-25', 10, 33, 1, '2025-06-18 14:35:24', '2025-06-18 14:35:24', 'Loan folder', 'No'),
(254, 'INVODis00030-25 loan files', 253, 33, 1, '2025-06-18 14:35:24', '2025-06-18 14:35:24', 'Loan files folder', 'No'),
(255, 'INVODis00030-25 loan collateral files', 253, 33, 1, '2025-06-18 14:35:24', '2025-06-18 14:35:24', 'Loan collateral files folder', 'No'),
(256, 'INVODis00031-25', 10, 34, 1, '2025-06-18 14:39:36', '2025-06-18 14:39:36', 'Loan folder', 'No'),
(257, 'INVODis00031-25 loan files', 256, 34, 1, '2025-06-18 14:39:36', '2025-06-18 14:39:36', 'Loan files folder', 'No'),
(258, 'INVODis00031-25 loan collateral files', 256, 34, 1, '2025-06-18 14:39:36', '2025-06-18 14:39:36', 'Loan collateral files folder', 'No'),
(259, 'INVODis00032-25', 10, 35, 1, '2025-06-18 15:28:27', '2025-06-18 15:28:27', 'Loan folder', 'No'),
(260, 'INVODis00032-25 loan files', 259, 35, 1, '2025-06-18 15:28:27', '2025-06-18 15:28:27', 'Loan files folder', 'No'),
(261, 'INVODis00032-25 loan collateral files', 259, 35, 1, '2025-06-18 15:28:27', '2025-06-18 15:28:27', 'Loan collateral files folder', 'No'),
(262, 'INVODis00033-25', 10, 36, 1, '2025-06-18 15:32:27', '2025-06-18 15:32:27', 'Loan folder', 'No'),
(263, 'INVODis00033-25 loan files', 262, 36, 1, '2025-06-18 15:32:27', '2025-06-18 15:32:27', 'Loan files folder', 'No'),
(264, 'INVODis00033-25 loan collateral files', 262, 36, 1, '2025-06-18 15:32:27', '2025-06-18 15:32:27', 'Loan collateral files folder', 'No'),
(265, 'INVODis00034-25', 10, 37, 1, '2025-06-18 15:36:59', '2025-06-18 15:36:59', 'Loan folder', 'No'),
(266, 'INVODis00034-25 loan files', 265, 37, 1, '2025-06-18 15:36:59', '2025-06-18 15:36:59', 'Loan files folder', 'No'),
(267, 'INVODis00034-25 loan collateral files', 265, 37, 1, '2025-06-18 15:36:59', '2025-06-18 15:36:59', 'Loan collateral files folder', 'No'),
(268, 'OrderFi0003-25', 10, 38, 1, '2025-06-19 09:22:04', '2025-06-19 09:22:04', 'Loan folder', 'No'),
(269, 'OrderFi0003-25 loan files', 268, 38, 1, '2025-06-19 09:22:04', '2025-06-19 09:22:04', 'Loan files folder', 'No'),
(270, 'OrderFi0003-25 loan collateral files', 268, 38, 1, '2025-06-19 09:22:04', '2025-06-19 09:22:04', 'Loan collateral files folder', 'No'),
(271, 'INVODis00035-25', 10, 39, 1, '2025-06-19 09:30:01', '2025-06-19 09:30:01', 'Loan folder', 'No'),
(272, 'INVODis00035-25 loan files', 271, 39, 1, '2025-06-19 09:30:01', '2025-06-19 09:30:01', 'Loan files folder', 'No'),
(273, 'INVODis00035-25 loan collateral files', 271, 39, 1, '2025-06-19 09:30:01', '2025-06-19 09:30:01', 'Loan collateral files folder', 'No'),
(274, 'INVODis00036-25', 10, 40, 1, '2025-06-19 09:31:56', '2025-06-19 09:31:56', 'Loan folder', 'No'),
(275, 'INVODis00036-25 loan files', 274, 40, 1, '2025-06-19 09:31:56', '2025-06-19 09:31:56', 'Loan files folder', 'No'),
(276, 'INVODis00036-25 loan collateral files', 274, 40, 1, '2025-06-19 09:31:56', '2025-06-19 09:31:56', 'Loan collateral files folder', 'No'),
(277, 'INVODis00037-25', 10, 41, 1, '2025-06-19 09:34:08', '2025-06-19 09:34:08', 'Loan folder', 'No'),
(278, 'INVODis00037-25 loan files', 277, 41, 1, '2025-06-19 09:34:08', '2025-06-19 09:34:08', 'Loan files folder', 'No'),
(279, 'INVODis00037-25 loan collateral files', 277, 41, 1, '2025-06-19 09:34:08', '2025-06-19 09:34:08', 'Loan collateral files folder', 'No'),
(280, 'INVODis00038-25', 10, 42, 1, '2025-06-19 09:37:22', '2025-06-19 09:37:22', 'Loan folder', 'No'),
(281, 'INVODis00038-25 loan files', 280, 42, 1, '2025-06-19 09:37:22', '2025-06-19 09:37:22', 'Loan files folder', 'No'),
(282, 'INVODis00038-25 loan collateral files', 280, 42, 1, '2025-06-19 09:37:22', '2025-06-19 09:37:22', 'Loan collateral files folder', 'No'),
(283, 'INVODis00039-25', 10, 43, 1, '2025-06-19 09:43:42', '2025-06-19 09:43:42', 'Loan folder', 'No'),
(284, 'INVODis00039-25 loan files', 283, 43, 1, '2025-06-19 09:43:42', '2025-06-19 09:43:42', 'Loan files folder', 'No'),
(285, 'INVODis00039-25 loan collateral files', 283, 43, 1, '2025-06-19 09:43:42', '2025-06-19 09:43:42', 'Loan collateral files folder', 'No'),
(286, 'OrderFi0004-25', 10, 44, 1, '2025-06-19 09:47:49', '2025-06-19 09:47:49', 'Loan folder', 'No'),
(287, 'OrderFi0004-25 loan files', 286, 44, 1, '2025-06-19 09:47:49', '2025-06-19 09:47:49', 'Loan files folder', 'No'),
(288, 'OrderFi0004-25 loan collateral files', 286, 44, 1, '2025-06-19 09:47:49', '2025-06-19 09:47:49', 'Loan collateral files folder', 'No'),
(289, 'INVODis00040-25', 10, 45, 1, '2025-06-19 09:53:11', '2025-06-19 09:53:11', 'Loan folder', 'No'),
(290, 'INVODis00040-25 loan files', 289, 45, 1, '2025-06-19 09:53:11', '2025-06-19 09:53:11', 'Loan files folder', 'No'),
(291, 'INVODis00040-25 loan collateral files', 289, 45, 1, '2025-06-19 09:53:11', '2025-06-19 09:53:11', 'Loan collateral files folder', 'No'),
(292, 'INVODis00041-25', 10, 46, 1, '2025-06-19 09:55:18', '2025-06-19 09:55:18', 'Loan folder', 'No'),
(293, 'INVODis00041-25 loan files', 292, 46, 1, '2025-06-19 09:55:18', '2025-06-19 09:55:18', 'Loan files folder', 'No'),
(294, 'INVODis00041-25 loan collateral files', 292, 46, 1, '2025-06-19 09:55:18', '2025-06-19 09:55:18', 'Loan collateral files folder', 'No'),
(295, 'INVODis00042-25', 10, 47, 1, '2025-06-19 10:19:38', '2025-06-19 10:19:38', 'Loan folder', 'No'),
(296, 'INVODis00042-25 loan files', 295, 47, 1, '2025-06-19 10:19:38', '2025-06-19 10:19:38', 'Loan files folder', 'No'),
(297, 'INVODis00042-25 loan collateral files', 295, 47, 1, '2025-06-19 10:19:38', '2025-06-19 10:19:38', 'Loan collateral files folder', 'No'),
(298, 'Supriag Technical and Procurement Limited', 12, 53, 1, '2025-06-19 11:14:06', '2025-06-19 11:14:06', 'Corporate Customer folder', 'No'),
(299, 'Supriag Technical and Procurement Limited Corporate files', 298, 53, 1, '2025-06-19 11:14:06', '2025-06-19 11:14:06', 'Corporate files folder', 'No'),
(300, 'Supriag Technical and Procurement Limited Shareholders files', 298, 53, 1, '2025-06-19 11:14:06', '2025-06-19 11:14:06', 'Corporate shareholders files folder', 'No'),
(301, 'INVODis00043-25', 10, 48, 1, '2025-06-19 11:26:03', '2025-06-19 11:26:03', 'Loan folder', 'No'),
(302, 'INVODis00043-25 loan files', 301, 48, 1, '2025-06-19 11:26:03', '2025-06-19 11:26:03', 'Loan files folder', 'No'),
(303, 'INVODis00043-25 loan collateral files', 301, 48, 1, '2025-06-19 11:26:03', '2025-06-19 11:26:03', 'Loan collateral files folder', 'No'),
(304, 'Thelb Enterprises Limited', 12, 54, 1, '2025-06-19 11:47:00', '2025-06-19 11:47:00', 'Corporate Customer folder', 'No'),
(305, 'Thelb Enterprises Limited Corporate files', 304, 54, 1, '2025-06-19 11:47:00', '2025-06-19 11:47:00', 'Corporate files folder', 'No'),
(306, 'Thelb Enterprises Limited Shareholders files', 304, 54, 1, '2025-06-19 11:47:00', '2025-06-19 11:47:00', 'Corporate shareholders files folder', 'No'),
(307, 'INVODis00044-25', 10, 49, 1, '2025-06-19 11:51:47', '2025-06-19 11:51:47', 'Loan folder', 'No'),
(308, 'INVODis00044-25 loan files', 307, 49, 1, '2025-06-19 11:51:47', '2025-06-19 11:51:47', 'Loan files folder', 'No'),
(309, 'INVODis00044-25 loan collateral files', 307, 49, 1, '2025-06-19 11:51:47', '2025-06-19 11:51:47', 'Loan collateral files folder', 'No'),
(310, 'INVODis00045-25', 10, 50, 1, '2025-06-19 12:03:50', '2025-06-19 12:03:50', 'Loan folder', 'No'),
(311, 'INVODis00045-25 loan files', 310, 50, 1, '2025-06-19 12:03:50', '2025-06-19 12:03:50', 'Loan files folder', 'No'),
(312, 'INVODis00045-25 loan collateral files', 310, 50, 1, '2025-06-19 12:03:50', '2025-06-19 12:03:50', 'Loan collateral files folder', 'No'),
(313, 'INVODis00046-25', 10, 51, 1, '2025-06-19 12:09:47', '2025-06-19 12:09:47', 'Loan folder', 'No'),
(314, 'INVODis00046-25 loan files', 313, 51, 1, '2025-06-19 12:09:47', '2025-06-19 12:09:47', 'Loan files folder', 'No'),
(315, 'INVODis00046-25 loan collateral files', 313, 51, 1, '2025-06-19 12:09:47', '2025-06-19 12:09:47', 'Loan collateral files folder', 'No'),
(316, 'INVODis00047-25', 10, 52, 1, '2025-06-19 12:43:23', '2025-06-19 12:43:23', 'Loan folder', 'No'),
(317, 'INVODis00047-25 loan files', 316, 52, 1, '2025-06-19 12:43:23', '2025-06-19 12:43:23', 'Loan files folder', 'No'),
(318, 'INVODis00047-25 loan collateral files', 316, 52, 1, '2025-06-19 12:43:23', '2025-06-19 12:43:23', 'Loan collateral files folder', 'No'),
(319, 'INVODis00048-25', 10, 53, 1, '2025-06-19 12:46:33', '2025-06-19 12:46:33', 'Loan folder', 'No'),
(320, 'INVODis00048-25 loan files', 319, 53, 1, '2025-06-19 12:46:33', '2025-06-19 12:46:33', 'Loan files folder', 'No'),
(321, 'INVODis00048-25 loan collateral files', 319, 53, 1, '2025-06-19 12:46:33', '2025-06-19 12:46:33', 'Loan collateral files folder', 'No'),
(322, 'Oriental Products Limited', 12, 55, 1, '2025-06-19 14:19:59', '2025-06-19 14:19:59', 'Corporate Customer folder', 'No'),
(323, 'Oriental Products Limited Corporate files', 322, 55, 1, '2025-06-19 14:19:59', '2025-06-19 14:19:59', 'Corporate files folder', 'No'),
(324, 'Oriental Products Limited Shareholders files', 322, 55, 1, '2025-06-19 14:19:59', '2025-06-19 14:19:59', 'Corporate shareholders files folder', 'No'),
(325, 'INVODis00049-25', 10, 54, 1, '2025-06-19 14:25:59', '2025-06-19 14:25:59', 'Loan folder', 'No'),
(326, 'INVODis00049-25 loan files', 325, 54, 1, '2025-06-19 14:25:59', '2025-06-19 14:25:59', 'Loan files folder', 'No'),
(327, 'INVODis00049-25 loan collateral files', 325, 54, 1, '2025-06-19 14:25:59', '2025-06-19 14:25:59', 'Loan collateral files folder', 'No'),
(328, 'INVODis00050-25', 10, 55, 1, '2025-06-19 14:28:14', '2025-06-19 14:28:14', 'Loan folder', 'No'),
(329, 'INVODis00050-25 loan files', 328, 55, 1, '2025-06-19 14:28:14', '2025-06-19 14:28:14', 'Loan files folder', 'No'),
(330, 'INVODis00050-25 loan collateral files', 328, 55, 1, '2025-06-19 14:28:14', '2025-06-19 14:28:14', 'Loan collateral files folder', 'No'),
(331, 'INVODis00051-25', 10, 56, 1, '2025-06-20 08:04:42', '2025-06-20 08:04:42', 'Loan folder', 'No'),
(332, 'INVODis00051-25 loan files', 331, 56, 1, '2025-06-20 08:04:42', '2025-06-20 08:04:42', 'Loan files folder', 'No'),
(333, 'INVODis00051-25 loan collateral files', 331, 56, 1, '2025-06-20 08:04:42', '2025-06-20 08:04:42', 'Loan collateral files folder', 'No'),
(334, 'INVODis00052-25', 10, 57, 1, '2025-06-20 13:54:21', '2025-06-20 13:54:21', 'Loan folder', 'No'),
(335, 'INVODis00052-25 loan files', 334, 57, 1, '2025-06-20 13:54:21', '2025-06-20 13:54:21', 'Loan files folder', 'No'),
(336, 'INVODis00052-25 loan collateral files', 334, 57, 1, '2025-06-20 13:54:21', '2025-06-20 13:54:21', 'Loan collateral files folder', 'No'),
(337, 'INVODis00053-25', 10, 58, 1, '2025-06-23 08:38:25', '2025-06-23 08:38:25', 'Loan folder', 'No'),
(338, 'INVODis00053-25 loan files', 337, 58, 1, '2025-06-23 08:38:25', '2025-06-23 08:38:25', 'Loan files folder', 'No'),
(339, 'INVODis00053-25 loan collateral files', 337, 58, 1, '2025-06-23 08:38:25', '2025-06-23 08:38:25', 'Loan collateral files folder', 'No'),
(340, 'Drill Mech Zambia Limited', 12, 56, 1, '2025-06-27 08:57:09', '2025-06-27 08:57:09', 'Corporate Customer folder', 'No'),
(341, 'Drill Mech Zambia Limited Corporate files', 340, 56, 1, '2025-06-27 08:57:09', '2025-06-27 08:57:09', 'Corporate files folder', 'No'),
(342, 'Drill Mech Zambia Limited Shareholders files', 340, 56, 1, '2025-06-27 08:57:09', '2025-06-27 08:57:09', 'Corporate shareholders files folder', 'No'),
(343, 'INVODis00054-25', 10, 59, 1, '2025-06-27 09:54:35', '2025-06-27 09:54:35', 'Loan folder', 'No'),
(344, 'INVODis00054-25 loan files', 343, 59, 1, '2025-06-27 09:54:35', '2025-06-27 09:54:35', 'Loan files folder', 'No'),
(345, 'INVODis00054-25 loan collateral files', 343, 59, 1, '2025-06-27 09:54:35', '2025-06-27 09:54:35', 'Loan collateral files folder', 'No'),
(346, 'OrderFi0005-25', 10, 60, 1, '2025-07-01 14:40:51', '2025-07-01 14:40:51', 'Loan folder', 'No'),
(347, 'OrderFi0005-25 loan files', 346, 60, 1, '2025-07-01 14:40:51', '2025-07-01 14:40:51', 'Loan files folder', 'No'),
(348, 'OrderFi0005-25 loan collateral files', 346, 60, 1, '2025-07-01 14:40:51', '2025-07-01 14:40:51', 'Loan collateral files folder', 'No'),
(349, 'Infocus TM Zambia Limited', 12, 57, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 'Corporate Customer folder', 'No'),
(350, 'Infocus TM Zambia Limited Corporate files', 349, 57, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 'Corporate files folder', 'No'),
(351, 'Infocus TM Zambia Limited Shareholders files', 349, 57, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 'Corporate shareholders files folder', 'No'),
(352, 'INVODis00055-25', 10, 61, 1, '2025-07-03 08:58:01', '2025-07-03 08:58:01', 'Loan folder', 'No'),
(353, 'INVODis00055-25 loan files', 352, 61, 1, '2025-07-03 08:58:01', '2025-07-03 08:58:01', 'Loan files folder', 'No'),
(354, 'INVODis00055-25 loan collateral files', 352, 61, 1, '2025-07-03 08:58:01', '2025-07-03 08:58:01', 'Loan collateral files folder', 'No'),
(355, 'INVODis00056-25', 10, 62, 1, '2025-07-03 09:49:13', '2025-07-03 09:49:13', 'Loan folder', 'No'),
(356, 'INVODis00056-25 loan files', 355, 62, 1, '2025-07-03 09:49:13', '2025-07-03 09:49:13', 'Loan files folder', 'No'),
(357, 'INVODis00056-25 loan collateral files', 355, 62, 1, '2025-07-03 09:49:13', '2025-07-03 09:49:13', 'Loan collateral files folder', 'No'),
(358, 'INVODis00057-25', 10, 63, 1, '2025-07-03 10:31:53', '2025-07-03 10:31:53', 'Loan folder', 'No'),
(359, 'INVODis00057-25 loan files', 358, 63, 1, '2025-07-03 10:31:53', '2025-07-03 10:31:53', 'Loan files folder', 'No'),
(360, 'INVODis00057-25 loan collateral files', 358, 63, 1, '2025-07-03 10:31:53', '2025-07-03 10:31:53', 'Loan collateral files folder', 'No'),
(361, 'INVODis00058-25', 10, 64, 1, '2025-07-11 15:52:39', '2025-07-11 15:52:39', 'Loan folder', 'No'),
(362, 'INVODis00058-25 loan files', 361, 64, 1, '2025-07-11 15:52:39', '2025-07-11 15:52:39', 'Loan files folder', 'No'),
(363, 'INVODis00058-25 loan collateral files', 361, 64, 1, '2025-07-11 15:52:39', '2025-07-11 15:52:39', 'Loan collateral files folder', 'No'),
(364, 'INVODis00059-25', 10, 65, 1, '2025-07-11 16:06:26', '2025-07-11 16:06:26', 'Loan folder', 'No'),
(365, 'INVODis00059-25 loan files', 364, 65, 1, '2025-07-11 16:06:26', '2025-07-11 16:06:26', 'Loan files folder', 'No'),
(366, 'INVODis00059-25 loan collateral files', 364, 65, 1, '2025-07-11 16:06:26', '2025-07-11 16:06:26', 'Loan collateral files folder', 'No'),
(367, 'INVODis00060-25', 10, 66, 1, '2025-07-11 16:08:20', '2025-07-11 16:08:20', 'Loan folder', 'No'),
(368, 'INVODis00060-25 loan files', 367, 66, 1, '2025-07-11 16:08:20', '2025-07-11 16:08:20', 'Loan files folder', 'No'),
(369, 'INVODis00060-25 loan collateral files', 367, 66, 1, '2025-07-11 16:08:20', '2025-07-11 16:08:20', 'Loan collateral files folder', 'No'),
(370, 'INVODis00061-25', 10, 67, 1, '2025-07-15 13:41:56', '2025-07-15 13:41:56', 'Loan folder', 'No'),
(371, 'INVODis00061-25 loan files', 370, 67, 1, '2025-07-15 13:41:56', '2025-07-15 13:41:56', 'Loan files folder', 'No'),
(372, 'INVODis00061-25 loan collateral files', 370, 67, 1, '2025-07-15 13:41:56', '2025-07-15 13:41:56', 'Loan collateral files folder', 'No'),
(373, 'INVODis00062-25', 10, 68, 1, '2025-07-16 10:26:29', '2025-07-16 10:26:29', 'Loan folder', 'No'),
(374, 'INVODis00062-25 loan files', 373, 68, 1, '2025-07-16 10:26:29', '2025-07-16 10:26:29', 'Loan files folder', 'No'),
(375, 'INVODis00062-25 loan collateral files', 373, 68, 1, '2025-07-16 10:26:29', '2025-07-16 10:26:29', 'Loan collateral files folder', 'No');

-- --------------------------------------------------------

--
-- Table structure for table `file_folder_mapping`
--

CREATE TABLE `file_folder_mapping` (
  `id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL COMMENT 'Foreign key to file_library.id',
  `folder_id` int(11) DEFAULT NULL COMMENT 'Foreign key to file_folders.id',
  `date_added` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Date when file was added to folder'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `file_folder_mapping`
--

INSERT INTO `file_folder_mapping` (`id`, `file_id`, `folder_id`, `date_added`) VALUES
(50, 61, 41, '2025-04-29 08:51:58'),
(51, 62, 43, '2025-05-05 14:33:55'),
(52, 63, 43, '2025-05-05 14:33:55'),
(53, 64, 43, '2025-05-05 14:33:55'),
(54, 65, 43, '2025-05-05 14:33:55'),
(55, 66, 44, '2025-05-05 14:33:55'),
(56, 67, 46, '2025-05-05 14:40:16'),
(57, 68, 46, '2025-05-05 14:40:16'),
(58, 69, 46, '2025-05-05 14:40:16'),
(59, 70, 46, '2025-05-05 14:40:16'),
(60, 71, 47, '2025-05-05 14:40:16'),
(61, 72, 49, '2025-05-05 14:56:26'),
(62, 73, 50, '2025-05-05 14:56:26'),
(63, 74, 44, '2025-05-05 15:01:26'),
(64, 75, 51, '2025-05-05 15:02:24'),
(65, 76, 53, '2025-05-08 11:24:29'),
(66, 77, 53, '2025-05-08 11:24:29'),
(67, 78, 53, '2025-05-08 11:24:29'),
(68, 79, 53, '2025-05-08 11:24:29'),
(69, 80, 56, '2025-05-08 11:30:36'),
(70, 81, 56, '2025-05-08 11:30:36'),
(71, 82, 56, '2025-05-08 11:30:36'),
(72, 83, 56, '2025-05-08 11:30:36'),
(73, 84, 59, '2025-05-08 11:36:42'),
(74, 85, 59, '2025-05-08 11:36:42'),
(75, 86, 59, '2025-05-08 11:36:42'),
(76, 87, 59, '2025-05-08 11:36:42'),
(77, 88, 62, '2025-05-08 11:40:36'),
(78, 89, 62, '2025-05-08 11:40:36'),
(79, 90, 62, '2025-05-08 11:40:36'),
(80, 91, 62, '2025-05-08 11:40:36'),
(81, 92, 65, '2025-05-08 11:44:05'),
(82, 93, 66, '2025-05-08 11:44:05'),
(83, 94, 80, '2025-05-21 08:28:05'),
(84, 95, 98, '2025-05-21 13:27:16'),
(85, 96, 101, '2025-05-23 08:28:40'),
(86, 97, 116, '2025-05-27 13:31:43'),
(87, 98, 149, '2025-06-04 09:58:27'),
(88, 99, 152, '2025-06-04 10:18:26'),
(89, 100, 155, '2025-06-04 10:19:01'),
(90, 101, 156, '2025-06-04 10:19:01'),
(91, 102, 158, '2025-06-04 10:25:22'),
(92, 103, 167, '2025-06-09 09:21:11'),
(93, 104, 170, '2025-06-09 09:24:29'),
(94, 105, 170, '2025-06-09 09:24:29'),
(95, 106, 170, '2025-06-09 09:24:29'),
(96, 107, 173, '2025-06-09 09:27:20'),
(97, 108, 179, '2025-06-09 09:53:24'),
(98, 109, 185, '2025-06-11 09:17:32'),
(99, 110, 185, '2025-06-11 09:17:32'),
(100, 111, 186, '2025-06-11 09:17:32'),
(101, 112, 197, '2025-06-16 15:20:44'),
(102, 113, 203, '2025-06-16 15:45:50'),
(103, 114, 206, '2025-06-16 16:08:39'),
(104, 115, 209, '2025-06-17 09:02:48'),
(105, 116, 218, '2025-06-17 09:37:37'),
(106, 117, 224, '2025-06-17 11:22:43'),
(107, 118, 227, '2025-06-18 13:38:05'),
(108, 119, 230, '2025-06-18 13:40:09'),
(109, 120, 233, '2025-06-18 13:42:06'),
(110, 121, 239, '2025-06-18 14:16:46'),
(111, 122, 242, '2025-06-18 14:20:50'),
(112, 123, 248, '2025-06-18 14:31:13'),
(113, 124, 251, '2025-06-18 14:33:13'),
(114, 125, 254, '2025-06-18 14:35:24'),
(115, 126, 257, '2025-06-18 14:39:36'),
(116, 127, 263, '2025-06-18 15:32:27'),
(117, 128, 266, '2025-06-18 15:36:59'),
(118, 129, 269, '2025-06-19 09:22:04'),
(119, 130, 272, '2025-06-19 09:30:01'),
(120, 131, 275, '2025-06-19 09:31:56'),
(121, 132, 278, '2025-06-19 09:34:08'),
(122, 133, 281, '2025-06-19 09:37:22'),
(123, 134, 290, '2025-06-19 09:53:11'),
(124, 135, 293, '2025-06-19 09:55:18'),
(125, 136, 296, '2025-06-19 10:19:38'),
(126, 137, 302, '2025-06-19 11:26:03'),
(127, 138, 308, '2025-06-19 11:51:47'),
(128, 139, 311, '2025-06-19 12:03:50'),
(129, 140, 314, '2025-06-19 12:09:47'),
(130, 141, 317, '2025-06-19 12:43:23'),
(131, 142, 320, '2025-06-19 12:46:33'),
(132, 143, 326, '2025-06-19 14:25:59'),
(133, 144, 329, '2025-06-19 14:28:14'),
(134, 145, 332, '2025-06-20 08:04:42'),
(135, 146, 335, '2025-06-20 13:54:21'),
(136, 147, 338, '2025-06-23 08:38:25'),
(137, 148, 341, '2025-06-27 08:57:09'),
(138, 149, 344, '2025-06-27 09:54:35'),
(139, 150, 347, '2025-07-01 14:40:51'),
(140, 151, 350, '2025-07-03 08:53:37'),
(141, 152, 351, '2025-07-03 08:53:37'),
(142, 153, 351, '2025-07-03 08:53:37'),
(143, 154, 362, '2025-07-11 15:52:39'),
(144, 155, 365, '2025-07-11 16:06:26'),
(145, 156, 368, '2025-07-11 16:08:20'),
(146, 157, 371, '2025-07-15 13:41:56'),
(147, 158, 374, '2025-07-16 10:26:29');

-- --------------------------------------------------------

--
-- Table structure for table `file_library`
--

CREATE TABLE `file_library` (
  `id` int(11) NOT NULL,
  `owner_type` varchar(50) NOT NULL COMMENT 'Type of owner (system_user, individual_customer, corporate_customer, shareholder, undertaker, group)',
  `owner_id` int(11) NOT NULL COMMENT 'ID of the owner (references respective tables)',
  `file_category` varchar(50) NOT NULL COMMENT 'Category (general_files, loan_collateral, loan_files, kyc_data)',
  `file_type` varchar(100) NOT NULL COMMENT 'MIME type of the file',
  `file_name` varchar(255) NOT NULL COMMENT 'Original name given by user',
  `file_path` varchar(255) NOT NULL COMMENT 'Path to the stored file',
  `file_size` int(11) NOT NULL COMMENT 'Size in bytes',
  `is_public` tinyint(1) DEFAULT 0 COMMENT 'Whether the file is public (1) or private (0)',
  `date_added` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Date and time when file was added',
  `date_modified` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Date and time of last modification',
  `added_by` int(11) NOT NULL COMMENT 'User ID who added the file',
  `description` text DEFAULT NULL COMMENT 'Optional description of the file',
  `tags` varchar(255) DEFAULT NULL COMMENT 'Comma-separated tags for improved searchability'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `file_library`
--

INSERT INTO `file_library` (`id`, `owner_type`, `owner_id`, `file_category`, `file_type`, `file_name`, `file_path`, `file_size`, `is_public`, `date_added`, `date_modified`, `added_by`, `description`, `tags`) VALUES
(61, 'system_user', 72, 'general_files', 'image/png', 'avatar-3.png', '/var/www/html/live.fundit-zm.com/public_html/uploads/files/2025/04/29/bb15b0a13bb4578aa02fa7200dac241d.png', 141, 1, '2025-04-29 08:51:58', '2025-04-29 08:51:58', 72, NULL, NULL),
(62, 'customer', 1, ' kyc_data', 'application/pdf', '973POS_Quote.pdf', 'uploads/Infocus Techlogy/973POS_Quote.pdf', 63, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 72, 'Corporate files', ''),
(63, 'customer', 1, ' kyc_data', 'image/png', '514Desing_and_Build_Invoice.png', 'uploads/Infocus Techlogy/514Desing_and_Build_Invoice.png', 115, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 72, 'Corporate files', ''),
(64, 'customer', 1, ' kyc_data', 'application/pdf', '274POS_Quote.pdf', 'uploads/Infocus Techlogy/274POS_Quote.pdf', 63, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 72, 'Corporate files', ''),
(65, 'customer', 1, ' kyc_data', 'application/pdf', '235POS_Quote.pdf', 'uploads/Infocus Techlogy/235POS_Quote.pdf', 63, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 72, 'Corporate files', ''),
(66, 'corporate', 1, 'kyc_data', 'image/png', '235POS_Quote1.pdf', 'uploads/Infocus Techlogy/235POS_Quote1.pdf', 118989, 1, '2025-05-05 14:33:55', '2025-05-05 14:33:55', 72, 'corporate files', ''),
(67, 'customer', 2, ' kyc_data', 'image/png', '551Desing_and_Build_Invoice.png', 'uploads/Fundit/551Desing_and_Build_Invoice.png', 115, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 72, 'Corporate files', ''),
(68, 'customer', 2, ' kyc_data', 'image/png', '601Daeyang_QUote.png', 'uploads/Fundit/601Daeyang_QUote.png', 116, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 72, 'Corporate files', ''),
(69, 'customer', 2, ' kyc_data', 'image/png', '131Daeyang_QUote.png', 'uploads/Fundit/131Daeyang_QUote.png', 116, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 72, 'Corporate files', ''),
(70, 'customer', 2, ' kyc_data', 'image/png', '102Desing_and_Build_Invoice.png', 'uploads/Fundit/102Desing_and_Build_Invoice.png', 115, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 72, 'Corporate files', ''),
(71, 'corporate', 2, 'kyc_data', 'image/png', '102Desing_and_Build_Invoice1.png', 'uploads/Fundit/102Desing_and_Build_Invoice1.png', 117382, 1, '2025-05-05 14:40:16', '2025-05-05 14:40:16', 72, 'corporate files', ''),
(72, 'loan', 1, 'loan_files', 'application/pdf', 'POS_Quote.pdf', 'uploads/NBT0001-25/POS_Quote.pdf', 64820, 1, '2025-05-05 14:56:26', '2025-05-05 14:56:26', 72, 'loan file for loan', ''),
(73, 'loan', 1, 'loan_files', 'image/png', '462Daeyang_QUote.png', 'uploads/NBT0001-25/462Daeyang_QUote.png', 118989, 1, '2025-05-05 14:56:26', '2025-05-05 14:56:26', 72, 'loan collateral file for loan', ''),
(74, 'system_user', 72, 'general_files', 'application/pdf', 'Desing and Build Invoice.pdf', '/var/www/html/live.fundit-zm.com/public_html/uploads/files/2025/05/05/9035629a49aa6f8742a1fef5ce5c078b.pdf', 63, 1, '2025-05-05 15:01:26', '2025-05-05 15:01:26', 72, NULL, NULL),
(75, 'system_user', 72, 'general_files', 'application/pdf', 'POS_Quote.pdf', '/var/www/html/live.fundit-zm.com/public_html/uploads/files/2025/05/05/2b489cdcff32e61b7b91bcade7aff0f6.pdf', 63, 1, '2025-05-05 15:02:24', '2025-05-05 15:02:24', 72, NULL, NULL),
(76, 'customer', 3, ' kyc_data', 'application/pdf', '112POS_Quote.pdf', 'uploads/Infovits/112POS_Quote.pdf', 63, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 72, 'Corporate files', ''),
(77, 'customer', 3, ' kyc_data', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '970Upcoming_Installment_Report.xlsx', 'uploads/Infovits/970Upcoming_Installment_Report.xlsx', 390, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 72, 'Corporate files', ''),
(78, 'customer', 3, ' kyc_data', 'image/png', '595avatar-3.png', 'uploads/Infovits/595avatar-3.png', 141, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 72, 'Corporate files', ''),
(79, 'customer', 3, ' kyc_data', 'application/pdf', '911POS_Quote.pdf', 'uploads/Infovits/911POS_Quote.pdf', 63, 1, '2025-05-08 11:24:29', '2025-05-08 11:24:29', 72, 'Corporate files', ''),
(80, 'customer', 5, ' kyc_data', 'application/pdf', '191POS_Quote.pdf', 'uploads/Infovits/191POS_Quote.pdf', 63, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 72, 'Corporate files', ''),
(81, 'customer', 5, ' kyc_data', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '939Upcoming_Installment_Report.xlsx', 'uploads/Infovits/939Upcoming_Installment_Report.xlsx', 390, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 72, 'Corporate files', ''),
(82, 'customer', 5, ' kyc_data', 'image/png', '297avatar-3.png', 'uploads/Infovits/297avatar-3.png', 141, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 72, 'Corporate files', ''),
(83, 'customer', 5, ' kyc_data', 'application/pdf', '314POS_Quote.pdf', 'uploads/Infovits/314POS_Quote.pdf', 63, 1, '2025-05-08 11:30:36', '2025-05-08 11:30:36', 72, 'Corporate files', ''),
(84, 'customer', 7, ' kyc_data', 'application/pdf', '579POS_Quote.pdf', 'uploads/Infovits/579POS_Quote.pdf', 63, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 72, 'Corporate files', ''),
(85, 'customer', 7, ' kyc_data', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '928Upcoming_Installment_Report.xlsx', 'uploads/Infovits/928Upcoming_Installment_Report.xlsx', 390, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 72, 'Corporate files', ''),
(86, 'customer', 7, ' kyc_data', 'image/png', '960avatar-3.png', 'uploads/Infovits/960avatar-3.png', 141, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 72, 'Corporate files', ''),
(87, 'customer', 7, ' kyc_data', 'application/pdf', '163POS_Quote.pdf', 'uploads/Infovits/163POS_Quote.pdf', 63, 1, '2025-05-08 11:36:42', '2025-05-08 11:36:42', 72, 'Corporate files', ''),
(88, 'customer', 8, ' kyc_data', 'application/pdf', '262POS_Quote.pdf', 'uploads/Infovits/262POS_Quote.pdf', 63, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 72, 'Corporate files', ''),
(89, 'customer', 8, ' kyc_data', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', '521Upcoming_Installment_Report.xlsx', 'uploads/Infovits/521Upcoming_Installment_Report.xlsx', 390, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 72, 'Corporate files', ''),
(90, 'customer', 8, ' kyc_data', 'image/png', '582avatar-3.png', 'uploads/Infovits/582avatar-3.png', 141, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 72, 'Corporate files', ''),
(91, 'customer', 8, ' kyc_data', 'application/pdf', '728POS_Quote.pdf', 'uploads/Infovits/728POS_Quote.pdf', 63, 1, '2025-05-08 11:40:36', '2025-05-08 11:40:36', 72, 'Corporate files', ''),
(92, 'loan', 2, 'loan_files', 'application/pdf', 'POS_Quote.pdf', 'uploads/NBT0002-25/POS_Quote.pdf', 64820, 1, '2025-05-08 11:44:05', '2025-05-08 11:44:05', 72, 'loan file for loan', ''),
(93, 'loan', 2, 'loan_files', 'application/pdf', '314Infocus_Techlogy(RG6252)_loan_report_as_on2025-05-05_(1).pdf', 'uploads/NBT0002-25/314Infocus_Techlogy(RG6252)_loan_report_as_on2025-05-05_(1).pdf', 20752, 1, '2025-05-08 11:44:05', '2025-05-08 11:44:05', 72, 'loan collateral file for loan', ''),
(94, 'customer', 10, ' kyc_data', 'application/pdf', '675Quorn_Flooring_Certificate_Of_Incorporation_.pdf', 'uploads/QUORN FLOORING LIMITED/675Quorn_Flooring_Certificate_Of_Incorporation_.pdf', 376, 1, '2025-05-21 08:28:05', '2025-05-21 08:28:05', 146, 'Corporate files', ''),
(95, 'loan', 5, 'loan_files', 'application/pdf', 'SMART_INVOICE_44.pdf', 'uploads/INVODis0005-25/SMART_INVOICE_44.pdf', 443908, 1, '2025-05-21 13:27:16', '2025-05-21 13:27:16', 139, 'loan file for loan', ''),
(96, 'customer', 15, ' kyc_data', 'application/pdf', '805CERTIFICATE_OF_INCORPORATION_WHEALS.pdf', 'uploads/WHEALS CONSTRUCTION LIMITED/805CERTIFICATE_OF_INCORPORATION_WHEALS.pdf', 763, 1, '2025-05-23 08:28:40', '2025-05-23 08:28:40', 146, 'Corporate files', ''),
(97, 'loan', 6, 'loan_files', 'application/pdf', 'PO_-_4501194470_-_7870.pdf', 'uploads/OrderFi0001-25/PO_-_4501194470_-_7870.pdf', 33915, 1, '2025-05-27 13:31:43', '2025-05-27 13:31:43', 139, 'loan file for loan', ''),
(98, 'loan', 9, 'loan_files', 'application/pdf', 'INVOICE_114.pdf', 'uploads/INVODis0007-25/INVOICE_114.pdf', 984728, 1, '2025-06-04 09:58:27', '2025-06-04 09:58:27', 139, 'loan file for loan', ''),
(99, 'customer', 39, ' kyc_data', 'application/pdf', '120CERTIFICATE_OF_INCORPORATION-1BRASSCO.pdf', 'uploads/Brassco Limited/120CERTIFICATE_OF_INCORPORATION-1BRASSCO.pdf', 434, 1, '2025-06-04 10:18:26', '2025-06-04 10:18:26', 139, 'Corporate certificate', ''),
(100, 'customer', 42, ' kyc_data', 'application/pdf', '812CERTIFICATE_OF_INCORPORATION-1BRASSCO.pdf', 'uploads/Brassco Limited/812CERTIFICATE_OF_INCORPORATION-1BRASSCO.pdf', 434, 1, '2025-06-04 10:19:01', '2025-06-04 10:19:01', 139, 'Corporate certificate', ''),
(101, 'corporate', 42, 'kyc_data', 'application/pdf', '812CERTIFICATE_OF_INCORPORATION-1BRASSCO1.pdf', 'uploads/Brassco Limited/812CERTIFICATE_OF_INCORPORATION-1BRASSCO1.pdf', 408564, 1, '2025-06-04 10:19:01', '2025-06-04 10:19:01', 139, 'Shareholder ID file', ''),
(102, 'loan', 10, 'loan_files', 'application/zip', 'Invoices.zip', 'uploads/INVODis0008-25/Invoices.zip', 1265005, 1, '2025-06-04 10:25:22', '2025-06-04 10:25:22', 139, 'loan file for loan', ''),
(103, 'loan', 11, 'loan_files', 'application/pdf', 'APPLICATION_TO_BORROW.pdf', 'uploads/INVODis0009-25/APPLICATION_TO_BORROW.pdf', 359384, 1, '2025-06-09 09:21:11', '2025-06-09 09:21:11', 146, 'loan file for loan', ''),
(104, 'loan', 12, 'loan_files', 'application/pdf', 'APPLICATION_TO_BORROW.pdf', 'uploads/INVODis00010-25/APPLICATION_TO_BORROW.pdf', 359384, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 146, 'loan file for loan', ''),
(105, 'loan', 12, 'loan_files', 'application/pdf', 'Board_Meeting_Summary_-_Jun_25.pdf', 'uploads/INVODis00010-25/Board_Meeting_Summary_-_Jun_25.pdf', 645587, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 146, 'loan file for loan', ''),
(106, 'loan', 12, 'loan_files', 'application/pdf', 'Deed_of_Assignment-Big_brand_-_Signed.pdf', 'uploads/INVODis00010-25/Deed_of_Assignment-Big_brand_-_Signed.pdf', 587497, 1, '2025-06-09 09:24:29', '2025-06-09 09:24:29', 146, 'loan file for loan', ''),
(107, 'loan', 13, 'loan_files', 'application/pdf', 'Application_For_Order_Financing_.pdf', 'uploads/OrderFi0002-25/Application_For_Order_Financing_.pdf', 442195, 1, '2025-06-09 09:27:20', '2025-06-09 09:27:20', 146, 'loan file for loan', ''),
(108, 'loan', 14, 'loan_files', 'application/pdf', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'uploads/INVODis00011-25/Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 1603045, 1, '2025-06-09 09:53:24', '2025-06-09 09:53:24', 146, 'loan file for loan', ''),
(109, 'customer', 48, ' kyc_data', 'application/pdf', '6022.INCORPORATION_CERTIFICATE.pdf', 'uploads/Turnkey Investments Limited/6022.INCORPORATION_CERTIFICATE.pdf', 689, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 146, 'Corporate certificate', ''),
(110, 'customer', 48, ' kyc_data', 'application/pdf', '255_reports_complianceReports_General_TCC_(10).pdf', 'uploads/Turnkey Investments Limited/255_reports_complianceReports_General_TCC_(10).pdf', 283, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 146, 'Tax document', ''),
(111, 'corporate', 48, 'kyc_data', 'application/pdf', '255_reports_complianceReports_General_TCC_(10)1.pdf', 'uploads/Turnkey Investments Limited/255_reports_complianceReports_General_TCC_(10)1.pdf', 1105369, 1, '2025-06-11 09:17:32', '2025-06-11 09:17:32', 146, 'Shareholder ID file', ''),
(112, 'loan', 18, 'loan_files', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'Facility_letter-Valve_Corp.docx', 'uploads/INVODis00015-25/Facility_letter-Valve_Corp.docx', 65005, 1, '2025-06-16 15:20:44', '2025-06-16 15:20:44', 146, 'loan file for loan', ''),
(113, 'loan', 19, 'loan_files', 'application/pdf', 'Letter_of_Guarantee_Wheals_.pdf', 'uploads/INVODis00016-25/Letter_of_Guarantee_Wheals_.pdf', 1328490, 1, '2025-06-16 15:45:50', '2025-06-16 15:45:50', 146, 'loan file for loan', ''),
(114, 'loan', 20, 'loan_files', 'application/pdf', 'Directors_Personal_Guarantee_.pdf', 'uploads/INVODis00017-25/Directors_Personal_Guarantee_.pdf', 942096, 1, '2025-06-16 16:08:39', '2025-06-16 16:08:39', 146, 'loan file for loan', ''),
(115, 'loan', 21, 'loan_files', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'Letter_of_guarantee_RGPM.docx', 'uploads/INVODis00018-25/Letter_of_guarantee_RGPM.docx', 56545, 1, '2025-06-17 09:02:48', '2025-06-17 09:02:48', 146, 'loan file for loan', ''),
(116, 'loan', 22, 'loan_files', 'application/pdf', 'APPLICATION_FOR_INVOICE_DISCOUNTING_THEMEFEX_.pdf', 'uploads/INVODis00019-25/APPLICATION_FOR_INVOICE_DISCOUNTING_THEMEFEX_.pdf', 274707, 1, '2025-06-17 09:37:37', '2025-06-17 09:37:37', 146, 'loan file for loan', ''),
(117, 'loan', 24, 'loan_files', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'Facility_letter-Themefex.docx', 'uploads/INVODis00021-25/Facility_letter-Themefex.docx', 70386, 1, '2025-06-17 11:22:43', '2025-06-17 11:22:43', 146, 'loan file for loan', ''),
(118, 'loan', 25, 'loan_files', 'application/pdf', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'uploads/INVODis00022-25/Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 1603045, 1, '2025-06-18 13:38:05', '2025-06-18 13:38:05', 146, 'loan file for loan', ''),
(119, 'loan', 26, 'loan_files', 'application/pdf', 'WESTMEAD_CONSENT_LETTER.pdf', 'uploads/INVODis00023-25/WESTMEAD_CONSENT_LETTER.pdf', 258655, 1, '2025-06-18 13:40:09', '2025-06-18 13:40:09', 146, 'loan file for loan', ''),
(120, 'loan', 27, 'loan_files', 'application/pdf', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'uploads/INVODis00024-25/SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 1777160, 1, '2025-06-18 13:42:06', '2025-06-18 13:42:06', 146, 'loan file for loan', ''),
(121, 'loan', 28, 'loan_files', 'application/pdf', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'uploads/INVODis00025-25/SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 1777160, 1, '2025-06-18 14:16:46', '2025-06-18 14:16:46', 146, 'loan file for loan', ''),
(122, 'loan', 29, 'loan_files', 'application/pdf', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'uploads/INVODis00026-25/Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 1603045, 1, '2025-06-18 14:20:50', '2025-06-18 14:20:50', 146, 'loan file for loan', ''),
(123, 'loan', 31, 'loan_files', 'application/pdf', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'uploads/INVODis00028-25/DIRECTORS_PERSONAL_GUARANTEE.pdf', 808561, 1, '2025-06-18 14:31:13', '2025-06-18 14:31:13', 146, 'loan file for loan', ''),
(124, 'loan', 32, 'loan_files', 'application/pdf', 'FACILITY_LETTER_TURNKEY.pdf', 'uploads/INVODis00029-25/FACILITY_LETTER_TURNKEY.pdf', 323564, 1, '2025-06-18 14:33:13', '2025-06-18 14:33:13', 146, 'loan file for loan', ''),
(125, 'loan', 33, 'loan_files', 'application/pdf', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'uploads/INVODis00030-25/SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 1777160, 1, '2025-06-18 14:35:24', '2025-06-18 14:35:24', 146, 'loan file for loan', ''),
(126, 'loan', 34, 'loan_files', 'application/pdf', 'WESTMEAD_CONSENT_LETTER.pdf', 'uploads/INVODis00031-25/WESTMEAD_CONSENT_LETTER.pdf', 258655, 1, '2025-06-18 14:39:36', '2025-06-18 14:39:36', 146, 'loan file for loan', ''),
(127, 'loan', 36, 'loan_files', 'application/pdf', 'Directors_Guarantee_Hemiwards_.pdf', 'uploads/INVODis00033-25/Directors_Guarantee_Hemiwards_.pdf', 650104, 1, '2025-06-18 15:32:27', '2025-06-18 15:32:27', 146, 'loan file for loan', ''),
(128, 'loan', 37, 'loan_files', 'application/pdf', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'uploads/INVODis00034-25/DIRECTORS_PERSONAL_GUARANTEE.pdf', 808561, 1, '2025-06-18 15:36:59', '2025-06-18 15:36:59', 146, 'loan file for loan', ''),
(129, 'loan', 38, 'loan_files', 'application/pdf', 'Letter_Of_Guarantee_Fox_.pdf', 'uploads/OrderFi0003-25/Letter_Of_Guarantee_Fox_.pdf', 1321834, 1, '2025-06-19 09:22:04', '2025-06-19 09:22:04', 146, 'loan file for loan', ''),
(130, 'loan', 39, 'loan_files', 'application/pdf', 'Personal_Guarantee_Shamuchisha_.pdf', 'uploads/INVODis00035-25/Personal_Guarantee_Shamuchisha_.pdf', 924032, 1, '2025-06-19 09:30:01', '2025-06-19 09:30:01', 146, 'loan file for loan', ''),
(131, 'loan', 40, 'loan_files', 'application/pdf', 'Application_for_Invoice_Discounting_Shamuchisha_.pdf', 'uploads/INVODis00036-25/Application_for_Invoice_Discounting_Shamuchisha_.pdf', 207297, 1, '2025-06-19 09:31:56', '2025-06-19 09:31:56', 146, 'loan file for loan', ''),
(132, 'loan', 41, 'loan_files', 'application/pdf', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'uploads/INVODis00037-25/SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 1777160, 1, '2025-06-19 09:34:08', '2025-06-19 09:34:08', 146, 'loan file for loan', ''),
(133, 'loan', 42, 'loan_files', 'application/pdf', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'uploads/INVODis00038-25/Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 1603045, 1, '2025-06-19 09:37:22', '2025-06-19 09:37:22', 146, 'loan file for loan', ''),
(134, 'loan', 45, 'loan_files', 'application/pdf', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'uploads/INVODis00040-25/DIRECTORS_PERSONAL_GUARANTEE.pdf', 808561, 1, '2025-06-19 09:53:11', '2025-06-19 09:53:11', 146, 'loan file for loan', ''),
(135, 'loan', 46, 'loan_files', 'application/pdf', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'uploads/INVODis00041-25/DIRECTORS_PERSONAL_GUARANTEE.pdf', 808561, 1, '2025-06-19 09:55:18', '2025-06-19 09:55:18', 146, 'loan file for loan', ''),
(136, 'loan', 47, 'loan_files', 'application/pdf', 'APPLICATION_FOR_INVOICE_DISCOUNTING_-DEVOURGE_.pdf', 'uploads/INVODis00042-25/APPLICATION_FOR_INVOICE_DISCOUNTING_-DEVOURGE_.pdf', 163067, 1, '2025-06-19 10:19:38', '2025-06-19 10:19:38', 146, 'loan file for loan', ''),
(137, 'loan', 48, 'loan_files', 'application/pdf', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 'uploads/INVODis00043-25/IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 211970, 1, '2025-06-19 11:26:03', '2025-06-19 11:26:03', 146, 'loan file for loan', ''),
(138, 'loan', 49, 'loan_files', 'application/pdf', 'THELB_Certificate_of_Incorporation-Tax_Clearance_Directors_IDs-4.pdf', 'uploads/INVODis00044-25/THELB_Certificate_of_Incorporation-Tax_Clearance_Directors_IDs-4.pdf', 291472, 1, '2025-06-19 11:51:47', '2025-06-19 11:51:47', 146, 'loan file for loan', ''),
(139, 'loan', 50, 'loan_files', 'application/pdf', 'Thelb_Articles_of_Association_12072023.pdf', 'uploads/INVODis00045-25/Thelb_Articles_of_Association_12072023.pdf', 635117, 1, '2025-06-19 12:03:50', '2025-06-19 12:03:50', 146, 'loan file for loan', ''),
(140, 'loan', 51, 'loan_files', 'application/pdf', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 'uploads/INVODis00046-25/IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 211970, 1, '2025-06-19 12:09:47', '2025-06-19 12:09:47', 146, 'loan file for loan', ''),
(141, 'loan', 52, 'loan_files', 'application/pdf', 'Bank_statement(1).pdf', 'uploads/INVODis00047-25/Bank_statement(1).pdf', 151219, 1, '2025-06-19 12:43:23', '2025-06-19 12:43:23', 146, 'loan file for loan', ''),
(142, 'loan', 53, 'loan_files', 'application/pdf', 'AppraisalReport_51663.pdf', 'uploads/INVODis00048-25/AppraisalReport_51663.pdf', 178038, 1, '2025-06-19 12:46:33', '2025-06-19 12:46:33', 146, 'loan file for loan', ''),
(143, 'loan', 54, 'loan_files', 'application/pdf', 'Oriental_Products_Articles_of_Association_CF2_24022020.pdf', 'uploads/INVODis00049-25/Oriental_Products_Articles_of_Association_CF2_24022020.pdf', 1908743, 1, '2025-06-19 14:25:59', '2025-06-19 14:25:59', 146, 'loan file for loan', ''),
(144, 'loan', 55, 'loan_files', 'application/pdf', 'Oriental_Products_Share_Capital_24022020.pdf', 'uploads/INVODis00050-25/Oriental_Products_Share_Capital_24022020.pdf', 69586, 1, '2025-06-19 14:28:14', '2025-06-19 14:28:14', 146, 'loan file for loan', ''),
(145, 'loan', 56, 'loan_files', 'application/pdf', 'Letter_Of_Guarantee_Fox_.pdf', 'uploads/INVODis00051-25/Letter_Of_Guarantee_Fox_.pdf', 1321834, 1, '2025-06-20 08:04:42', '2025-06-20 08:04:42', 146, 'loan file for loan', ''),
(146, 'loan', 57, 'loan_files', 'application/pdf', 'PEREZ_PERSONAL_GUARANTEE_.pdf', 'uploads/INVODis00052-25/PEREZ_PERSONAL_GUARANTEE_.pdf', 899260, 1, '2025-06-20 13:54:21', '2025-06-20 13:54:21', 146, 'loan file for loan', ''),
(147, 'loan', 58, 'loan_files', 'image/jpeg', 'Invoice.jpg', 'uploads/INVODis00053-25/Invoice.jpg', 43451, 1, '2025-06-23 08:38:25', '2025-06-23 08:38:25', 139, 'loan file for loan', ''),
(148, 'customer', 56, ' kyc_data', 'application/pdf', '330Certificate_of_incorp.pdf', 'uploads/Drill Mech Zambia Limited/330Certificate_of_incorp.pdf', 193, 1, '2025-06-27 08:57:09', '2025-06-27 08:57:09', 146, 'Corporate certificate', ''),
(149, 'loan', 59, 'loan_files', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'Deed_of_Assignment-_DRILL.docx', 'uploads/INVODis00054-25/Deed_of_Assignment-_DRILL.docx', 50679, 1, '2025-06-27 09:54:35', '2025-06-27 09:54:35', 146, 'loan file for loan', ''),
(150, 'loan', 60, 'loan_files', 'application/pdf', 'Order_MCM.pdf', 'uploads/OrderFi0005-25/Order_MCM.pdf', 1350145, 1, '2025-07-01 14:40:51', '2025-07-01 14:40:51', 139, 'loan file for loan', ''),
(151, 'customer', 57, ' kyc_data', 'application/pdf', '857InFocus_PACRA_Printout.pdf', 'uploads/Infocus TM Zambia Limited/857InFocus_PACRA_Printout.pdf', 1341, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 146, 'Corporate certificate', ''),
(152, 'corporate', 57, 'kyc_data', 'application/pdf', '857InFocus_PACRA_Printout1.pdf', 'uploads/Infocus TM Zambia Limited/857InFocus_PACRA_Printout1.pdf', 409418, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 146, 'Shareholder ID file', ''),
(153, 'corporate', 57, 'kyc_data', 'application/pdf', '857InFocus_PACRA_Printout2.pdf', 'uploads/Infocus TM Zambia Limited/857InFocus_PACRA_Printout2.pdf', 409418, 1, '2025-07-03 08:53:37', '2025-07-03 08:53:37', 146, 'Shareholder ID file', ''),
(154, 'loan', 64, 'loan_files', 'application/pdf', 'Deed_of_Assignment_********.pdf', 'uploads/INVODis00058-25/Deed_of_Assignment_********.pdf', 248558, 1, '2025-07-11 15:52:39', '2025-07-11 15:52:39', 146, 'loan file for loan', ''),
(155, 'loan', 65, 'loan_files', 'application/pdf', 'Deed_of_Assignment_********.pdf', 'uploads/INVODis00059-25/Deed_of_Assignment_********.pdf', 248558, 1, '2025-07-11 16:06:26', '2025-07-11 16:06:26', 146, 'loan file for loan', ''),
(156, 'loan', 66, 'loan_files', 'application/pdf', 'Deed_of_Assignment_********.pdf', 'uploads/INVODis00060-25/Deed_of_Assignment_********.pdf', 248558, 1, '2025-07-11 16:08:20', '2025-07-11 16:08:20', 146, 'loan file for loan', ''),
(157, 'loan', 67, 'loan_files', 'application/pdf', 'Deed_of_Assignment_********.pdf', 'uploads/INVODis00061-25/Deed_of_Assignment_********.pdf', 248558, 1, '2025-07-15 13:41:56', '2025-07-15 13:41:56', 146, 'loan file for loan', ''),
(158, 'loan', 68, 'loan_files', 'application/pdf', 'DEED_OF_ASSIGNMENT.pdf', 'uploads/INVODis00062-25/DEED_OF_ASSIGNMENT.pdf', 345668, 1, '2025-07-16 10:26:29', '2025-07-16 10:26:29', 146, 'loan file for loan', '');

-- --------------------------------------------------------

--
-- Table structure for table `file_shares`
--

CREATE TABLE `file_shares` (
  `id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL COMMENT 'Foreign key to file_library.id',
  `shared_by` int(11) NOT NULL COMMENT 'User ID who shared the file',
  `shared_with` int(11) NOT NULL COMMENT 'User ID with whom file is shared',
  `date_shared` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Date and time when sharing occurred',
  `permission_level` varchar(20) NOT NULL DEFAULT 'view' COMMENT 'Permission level (view, edit, etc.)',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'Whether the share is still active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `financial_year`
--

CREATE TABLE `financial_year` (
  `fyid` int(11) NOT NULL,
  `year_start` date NOT NULL,
  `year_end` date NOT NULL,
  `status` enum('ACTIVE','INACTIVE','PASSED') NOT NULL DEFAULT 'INACTIVE'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `financial_year`
--

INSERT INTO `financial_year` (`fyid`, `year_start`, `year_end`, `status`) VALUES
(1, '2021-01-01', '2021-12-31', 'ACTIVE'),
(2, '2022-01-01', '2022-12-31', 'INACTIVE'),
(3, '2023-01-01', '2023-12-31', 'INACTIVE'),
(4, '2025-01-01', '2025-12-31', 'INACTIVE');

-- --------------------------------------------------------

--
-- Table structure for table `fyer_holiday`
--

CREATE TABLE `fyer_holiday` (
  `fyer_id` int(11) NOT NULL,
  `fyr_id` int(11) NOT NULL,
  `date` date NOT NULL,
  `holiday_description` varchar(200) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `fyer_holiday`
--

INSERT INTO `fyer_holiday` (`fyer_id`, `fyr_id`, `date`, `holiday_description`, `date_added`) VALUES
(1, 1, '2021-08-24', 'Misheck Holiday', '2021-08-19 17:45:09'),
(2, 1, '2021-08-25', 'Paul Hopilday', '2021-08-19 17:45:09'),
(6, 2, '2021-08-26', '\r\n      Try it', '2021-08-24 23:13:50'),
(7, 2, '2021-08-27', '\r\nTrue ramadan', '2021-08-24 23:14:09'),
(8, 2, '2021-08-27', '\r\nTrue ramadan', '2021-08-24 23:16:27'),
(9, 2, '2021-08-27', '\r\nTrue ramadan', '2021-08-24 23:16:39'),
(10, 2, '2021-08-21', 'Mothers Day', '2021-08-24 23:16:58'),
(11, 3, '2021-08-21', 'Mothers Day', '2021-08-24 23:17:17'),
(12, 1, '2021-09-28', '\r\n    Mothers daya  ', '2021-08-31 15:05:23'),
(13, 1, '2021-08-31', 'Eid   ', '2021-08-31 15:10:17'),
(14, 1, '2021-09-02', '\r\n   jkhhj   ', '2021-08-31 15:16:24'),
(15, 1, '2021-09-03', '\r\nhblkglhijbhhj   ', '2021-08-31 15:16:41');

-- --------------------------------------------------------

--
-- Table structure for table `geo_countries`
--

CREATE TABLE `geo_countries` (
  `name` varchar(100) NOT NULL,
  `id` varchar(2) NOT NULL DEFAULT '' COMMENT 'ISO 3661-1 alpha-2',
  `abv3` char(3) DEFAULT NULL COMMENT 'ISO 3661-1 alpha-3',
  `abv3_alt` char(3) DEFAULT NULL,
  `code` char(3) DEFAULT NULL COMMENT 'ISO 3661-1 numeric',
  `slug` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `geo_countries`
--

INSERT INTO `geo_countries` (`name`, `id`, `abv3`, `abv3_alt`, `code`, `slug`) VALUES
('Andorra', 'AD', 'AND', NULL, '20', 'andorra'),
('United Arab Emirates', 'AE', 'ARE', NULL, '784', 'united-arab-emirates'),
('Afghanistan', 'AF', 'AFG', NULL, '4', 'afghanistan'),
('Antigua and Barbuda', 'AG', 'ATG', NULL, '28', 'antigua-and-barbuda'),
('Anguilla', 'AI', 'AIA', NULL, '660', 'anguilla'),
('Albania', 'AL', 'ALB', NULL, '8', 'albania'),
('Armenia', 'AM', 'ARM', NULL, '51', 'armenia'),
('Netherlands Antilles', 'AN', 'ANT', NULL, '530', 'netherlands-antilles'),
('Angola', 'AO', 'AGO', NULL, '24', 'angola'),
('Argentina', 'AR', 'ARG', NULL, '32', 'argentina'),
('American Samoa', 'AS', 'ASM', NULL, '16', 'american-samoa'),
('Austria', 'AT', 'AUT', NULL, '40', 'austria'),
('Australia', 'AU', 'AUS', NULL, '36', 'australia'),
('Aruba', 'AW', 'ABW', NULL, '533', 'aruba'),
('Aland Islands', 'AX', 'ALA', NULL, '248', 'aland-islands'),
('Azerbaijan', 'AZ', 'AZE', NULL, '31', 'azerbaijan'),
('Bosnia and Herzegovina', 'BA', 'BIH', NULL, '70', 'bosnia-and-herzegovina'),
('Barbados', 'BB', 'BRB', NULL, '52', 'barbados'),
('Bangladesh', 'BD', 'BGD', NULL, '50', 'bangladesh'),
('Belgium', 'BE', 'BEL', NULL, '56', 'belgium'),
('Burkina Faso', 'BF', 'BFA', NULL, '854', 'burkina-faso'),
('Bulgaria', 'BG', 'BGR', NULL, '100', 'bulgaria'),
('Bahrain', 'BH', 'BHR', NULL, '48', 'bahrain'),
('Burundi', 'BI', 'BDI', NULL, '108', 'burundi'),
('Benin', 'BJ', 'BEN', NULL, '204', 'benin'),
('Saint-Barthelemy', 'BL', 'BLM', NULL, '652', 'saint-barthelemy'),
('Bermuda', 'BM', 'BMU', NULL, '60', 'bermuda'),
('Brunei Darussalam', 'BN', 'BRN', NULL, '96', 'brunei-darussalam'),
('Bolivia', 'BO', 'BOL', NULL, '68', 'bolivia'),
('Brazil', 'BR', 'BRA', NULL, '76', 'brazil'),
('Bahamas', 'BS', 'BHS', NULL, '44', 'bahamas'),
('Bhutan', 'BT', 'BTN', NULL, '64', 'bhutan'),
('Botswana', 'BW', 'BWA', NULL, '72', 'botswana'),
('Belarus', 'BY', 'BLR', NULL, '112', 'belarus'),
('Belize', 'BZ', 'BLZ', NULL, '84', 'belize'),
('Canada', 'CA', 'CAN', NULL, '124', 'canada'),
('Democratic Republic of the Congo', 'CD', 'COD', NULL, '180', 'democratic-republic-of-congo'),
('Central African Republic', 'CF', 'CAF', NULL, '140', 'central-african-republic'),
('Congo', 'CG', 'COG', NULL, '178', 'congo'),
('Switzerland', 'CH', 'CHE', NULL, '756', 'switzerland'),
('Cote d\'Ivoire', 'CI', 'CIV', NULL, '384', 'cote-divoire'),
('Cook Islands', 'CK', 'COK', NULL, '184', 'cook-islands'),
('Chile', 'CL', 'CHL', 'CHI', '152', 'chile'),
('Cameroon', 'CM', 'CMR', NULL, '120', 'cameroon'),
('China', 'CN', 'CHN', NULL, '156', 'china'),
('Colombia', 'CO', 'COL', NULL, '170', 'colombia'),
('Costa Rica', 'CR', 'CRI', NULL, '188', 'costa-rica'),
('Cuba', 'CU', 'CUB', NULL, '192', 'cuba'),
('Cape Verde', 'CV', 'CPV', NULL, '132', 'cape-verde'),
('Cyprus', 'CY', 'CYP', NULL, '196', 'cyprus'),
('Czech Republic', 'CZ', 'CZE', NULL, '203', 'czech-republic'),
('Germany', 'DE', 'DEU', NULL, '276', 'germany'),
('Djibouti', 'DJ', 'DJI', NULL, '262', 'djibouti'),
('Denmark', 'DK', 'DNK', NULL, '208', 'denmark'),
('Dominica', 'DM', 'DMA', NULL, '212', 'dominica'),
('Dominican Republic', 'DO', 'DOM', NULL, '214', 'dominican-republic'),
('Algeria', 'DZ', 'DZA', NULL, '12', 'algeria'),
('Ecuador', 'EC', 'ECU', NULL, '218', 'ecuador'),
('Estonia', 'EE', 'EST', NULL, '233', 'estonia'),
('Egypt', 'EG', 'EGY', NULL, '818', 'egypt'),
('Western Sahara', 'EH', 'ESH', NULL, '732', 'western-sahara'),
('Eritrea', 'ER', 'ERI', NULL, '232', 'eritrea'),
('Spain', 'ES', 'ESP', NULL, '724', 'spain'),
('Ethiopia', 'ET', 'ETH', NULL, '231', 'ethiopia'),
('Finland', 'FI', 'FIN', NULL, '246', 'finland'),
('Fiji', 'FJ', 'FJI', NULL, '242', 'fiji'),
('Falkland Islands', 'FK', 'FLK', NULL, '238', 'falkland-islands'),
('Micronesia', 'FM', 'FSM', NULL, '583', 'micronesia'),
('Faeroe Islands', 'FO', 'FRO', NULL, '234', 'faeroe-islands'),
('France', 'FR', 'FRA', NULL, '250', 'france'),
('Gabon', 'GA', 'GAB', NULL, '266', 'gabon'),
('Grenada', 'GD', 'GRD', NULL, '308', 'grenada'),
('Georgia', 'GE', 'GEO', NULL, '268', 'georgia'),
('French Guiana', 'GF', 'GUF', NULL, '254', 'french-guiana'),
('Guernsey', 'GG', 'GGY', NULL, '831', 'guernsey'),
('Ghana', 'GH', 'GHA', NULL, '288', 'ghana'),
('Gibraltar', 'GI', 'GIB', NULL, '292', 'gibraltar'),
('Greenland', 'GL', 'GRL', NULL, '304', 'greenland'),
('Gambia', 'GM', 'GMB', NULL, '270', 'gambia'),
('Guinea', 'GN', 'GIN', NULL, '324', 'guinea'),
('Guadeloupe', 'GP', 'GLP', NULL, '312', 'guadeloupe'),
('Equatorial Guinea', 'GQ', 'GNQ', NULL, '226', 'equatorial-guinea'),
('Greece', 'GR', 'GRC', NULL, '300', 'greece'),
('Guatemala', 'GT', 'GTM', NULL, '320', 'guatemala'),
('Guam', 'GU', 'GUM', NULL, '316', 'guam'),
('Guinea-Bissau', 'GW', 'GNB', NULL, '624', 'guinea-bissau'),
('Guyana', 'GY', 'GUY', NULL, '328', 'guyana'),
('Hong Kong', 'HK', 'HKG', NULL, '344', 'hong-kong'),
('Honduras', 'HN', 'HND', NULL, '340', 'honduras'),
('Croatia', 'HR', 'HRV', NULL, '191', 'croatia'),
('Haiti', 'HT', 'HTI', NULL, '332', 'haiti'),
('Hungary', 'HU', 'HUN', NULL, '348', 'hungary'),
('Indonesia', 'ID', 'IDN', NULL, '360', 'indonesia'),
('Ireland', 'IE', 'IRL', NULL, '372', 'ireland'),
('Israel', 'IL', 'ISR', NULL, '376', 'israel'),
('Isle of Man', 'IM', 'IMN', NULL, '833', 'isle-of-man'),
('India', 'IN', 'IND', NULL, '356', 'india'),
('Iraq', 'IQ', 'IRQ', NULL, '368', 'iraq'),
('Iran', 'IR', 'IRN', NULL, '364', 'iran'),
('Iceland', 'IS', 'ISL', NULL, '352', 'iceland'),
('Italy', 'IT', 'ITA', NULL, '380', 'italy'),
('Jersey', 'JE', 'JEY', NULL, '832', 'jersey'),
('Jamaica', 'JM', 'JAM', NULL, '388', 'jamaica'),
('Jordan', 'JO', 'JOR', NULL, '400', 'jordan'),
('Japan', 'JP', 'JPN', NULL, '392', 'japan'),
('Kenya', 'KE', 'KEN', NULL, '404', 'kenya'),
('Kyrgyzstan', 'KG', 'KGZ', NULL, '417', 'kyrgyzstan'),
('Cambodia', 'KH', 'KHM', NULL, '116', 'cambodia'),
('Kiribati', 'KI', 'KIR', NULL, '296', 'kiribati'),
('Comoros', 'KM', 'COM', NULL, '174', 'comoros'),
('Saint Kitts and Nevis', 'KN', 'KNA', NULL, '659', 'saint-kitts-and-nevis'),
('North Korea', 'KP', 'PRK', NULL, '408', 'north-korea'),
('South Korea', 'KR', 'KOR', NULL, '410', 'south-korea'),
('Kuwait', 'KW', 'KWT', NULL, '414', 'kuwait'),
('Cayman Islands', 'KY', 'CYM', NULL, '136', 'cayman-islands'),
('Kazakhstan', 'KZ', 'KAZ', NULL, '398', 'kazakhstan'),
('Laos', 'LA', 'LAO', NULL, '418', 'laos'),
('Lebanon', 'LB', 'LBN', NULL, '422', 'lebanon'),
('Saint Lucia', 'LC', 'LCA', NULL, '662', 'saint-lucia'),
('Liechtenstein', 'LI', 'LIE', NULL, '438', 'liechtenstein'),
('Sri Lanka', 'LK', 'LKA', NULL, '144', 'sri-lanka'),
('Liberia', 'LR', 'LBR', NULL, '430', 'liberia'),
('Lesotho', 'LS', 'LSO', NULL, '426', 'lesotho'),
('Lithuania', 'LT', 'LTU', NULL, '440', 'lithuania'),
('Luxembourg', 'LU', 'LUX', NULL, '442', 'luxembourg'),
('Latvia', 'LV', 'LVA', NULL, '428', 'latvia'),
('Libyan Arab Jamahiriya', 'LY', 'LBY', NULL, '434', 'libyan-arab-jamahiriya'),
('Morocco', 'MA', 'MAR', NULL, '504', 'morocco'),
('Monaco', 'MC', 'MCO', NULL, '492', 'monaco'),
('Moldova', 'MD', 'MDA', NULL, '498', 'moldova'),
('Montenegro', 'ME', 'MNE', NULL, '499', 'montenegro'),
('Saint-Martin', 'MF', 'MAF', NULL, '663', 'saint-martin'),
('Madagascar', 'MG', 'MDG', NULL, '450', 'madagascar'),
('Marshall Islands', 'MH', 'MHL', NULL, '584', 'marshall-islands'),
('Macedonia', 'MK', 'MKD', NULL, '807', 'macedonia'),
('Mali', 'ML', 'MLI', NULL, '466', 'mali'),
('Myanmar', 'MM', 'MMR', 'BUR', '104', 'myanmar'),
('Mongolia', 'MN', 'MNG', NULL, '496', 'mongolia'),
('Macao', 'MO', 'MAC', NULL, '446', 'macao'),
('Northern Mariana Islands', 'MP', 'MNP', NULL, '580', 'northern-mariana-islands'),
('Martinique', 'MQ', 'MTQ', NULL, '474', 'martinique'),
('Mauritania', 'MR', 'MRT', NULL, '478', 'mauritania'),
('Montserrat', 'MS', 'MSR', NULL, '500', 'montserrat'),
('Malta', 'MT', 'MLT', NULL, '470', 'malta'),
('Mauritius', 'MU', 'MUS', NULL, '480', 'mauritius'),
('Maldives', 'MV', 'MDV', NULL, '462', 'maldives'),
('Malawi', 'MW', 'MWI', NULL, '454', 'malawi'),
('Mexico', 'MX', 'MEX', NULL, '484', 'mexico'),
('Malaysia', 'MY', 'MYS', NULL, '458', 'malaysia'),
('Mozambique', 'MZ', 'MOZ', NULL, '508', 'mozambique'),
('Namibia', 'NA', 'NAM', NULL, '516', 'namibia'),
('New Caledonia', 'NC', 'NCL', NULL, '540', 'new-caledonia'),
('Niger', 'NE', 'NER', NULL, '562', 'niger'),
('Norfolk Island', 'NF', 'NFK', NULL, '574', 'norfolk-island'),
('Nigeria', 'NG', 'NGA', NULL, '566', 'nigeria'),
('Nicaragua', 'NI', 'NIC', NULL, '558', 'nicaragua'),
('Netherlands', 'NL', 'NLD', NULL, '528', 'netherlands'),
('Norway', 'NO', 'NOR', NULL, '578', 'norway'),
('Nepal', 'NP', 'NPL', NULL, '524', 'nepal'),
('Nauru', 'NR', 'NRU', NULL, '520', 'nauru'),
('Niue', 'NU', 'NIU', NULL, '570', 'niue'),
('New Zealand', 'NZ', 'NZL', NULL, '554', 'new-zealand'),
('Oman', 'OM', 'OMN', NULL, '512', 'oman'),
('Panama', 'PA', 'PAN', NULL, '591', 'panama'),
('Peru', 'PE', 'PER', NULL, '604', 'peru'),
('French Polynesia', 'PF', 'PYF', NULL, '258', 'french-polynesia'),
('Papua New Guinea', 'PG', 'PNG', NULL, '598', 'papua-new-guinea'),
('Philippines', 'PH', 'PHL', NULL, '608', 'philippines'),
('Pakistan', 'PK', 'PAK', NULL, '586', 'pakistan'),
('Poland', 'PL', 'POL', NULL, '616', 'poland'),
('Saint Pierre and Miquelon', 'PM', 'SPM', NULL, '666', 'saint-pierre-and-miquelon'),
('Pitcairn', 'PN', 'PCN', NULL, '612', 'pitcairn'),
('Puerto Rico', 'PR', 'PRI', NULL, '630', 'puerto-rico'),
('Palestine', 'PS', 'PSE', NULL, '275', 'palestine'),
('Portugal', 'PT', 'PRT', NULL, '620', 'portugal'),
('Palau', 'PW', 'PLW', NULL, '585', 'palau'),
('Paraguay', 'PY', 'PRY', NULL, '600', 'paraguay'),
('Qatar', 'QA', 'QAT', NULL, '634', 'qatar'),
('Reunion', 'RE', 'REU', NULL, '638', 'reunion'),
('Romania', 'RO', 'ROU', 'ROM', '642', 'romania'),
('Serbia', 'RS', 'SRB', NULL, '688', 'serbia'),
('Russian Federation', 'RU', 'RUS', NULL, '643', 'russian-federation'),
('Rwanda', 'RW', 'RWA', NULL, '646', 'rwanda'),
('Saudi Arabia', 'SA', 'SAU', NULL, '682', 'saudi-arabia'),
('Solomon Islands', 'SB', 'SLB', NULL, '90', 'solomon-islands'),
('Seychelles', 'SC', 'SYC', NULL, '690', 'seychelles'),
('Sudan', 'SD', 'SDN', NULL, '729', 'sudan'),
('Sweden', 'SE', 'SWE', NULL, '752', 'sweden'),
('Singapore', 'SG', 'SGP', NULL, '702', 'singapore'),
('Saint Helena', 'SH', 'SHN', NULL, '654', 'saint-helena'),
('Slovenia', 'SI', 'SVN', NULL, '705', 'slovenia'),
('Svalbard and Jan Mayen Islands', 'SJ', 'SJM', NULL, '744', 'svalbard-and-jan-mayen-islands'),
('Slovakia', 'SK', 'SVK', NULL, '703', 'slovakia'),
('Sierra Leone', 'SL', 'SLE', NULL, '694', 'sierra-leone'),
('San Marino', 'SM', 'SMR', NULL, '674', 'san-marino'),
('Senegal', 'SN', 'SEN', NULL, '686', 'senegal'),
('Somalia', 'SO', 'SOM', NULL, '706', 'somalia'),
('Suriname', 'SR', 'SUR', NULL, '740', 'suriname'),
('South Sudan', 'SS', 'SSD', NULL, '728', 'south-sudan'),
('Sao Tome and Principe', 'ST', 'STP', NULL, '678', 'sao-tome-and-principe'),
('El Salvador', 'SV', 'SLV', NULL, '222', 'el-salvador'),
('Syrian Arab Republic', 'SY', 'SYR', NULL, '760', 'syrian-arab-republic'),
('Swaziland', 'SZ', 'SWZ', NULL, '748', 'swaziland'),
('Turks and Caicos Islands', 'TC', 'TCA', NULL, '796', 'turks-and-caicos-islands'),
('Chad', 'TD', 'TCD', NULL, '148', 'chad'),
('Togo', 'TG', 'TGO', NULL, '768', 'togo'),
('Thailand', 'TH', 'THA', NULL, '764', 'thailand'),
('Tajikistan', 'TJ', 'TJK', NULL, '762', 'tajikistan'),
('Tokelau', 'TK', 'TKL', NULL, '772', 'tokelau'),
('Turkmenistan', 'TM', 'TKM', NULL, '795', 'turkmenistan'),
('Tunisia', 'TN', 'TUN', NULL, '788', 'tunisia'),
('Tonga', 'TO', 'TON', NULL, '776', 'tonga'),
('Timor-Leste', 'TP', 'TLS', NULL, '626', 'timor-leste'),
('Turkey', 'TR', 'TUR', NULL, '792', 'turkey'),
('Trinidad and Tobago', 'TT', 'TTO', NULL, '780', 'trinidad-and-tobago'),
('Tuvalu', 'TV', 'TUV', NULL, '798', 'tuvalu'),
('Tanzania', 'TZ', 'TZA', NULL, '834', 'tanzania'),
('Ukraine', 'UA', 'UKR', NULL, '804', 'ukraine'),
('Uganda', 'UG', 'UGA', NULL, '800', 'uganda'),
('United Kingdom', 'UK', 'GBR', NULL, '826', 'united-kingdom'),
('United States', 'US', 'USA', NULL, '840', 'united-states'),
('Uruguay', 'UY', 'URY', NULL, '858', 'uruguay'),
('Uzbekistan', 'UZ', 'UZB', NULL, '860', 'uzbekistan'),
('Holy See', 'VA', 'VAT', NULL, '336', 'holy-see'),
('Saint Vincent and the Grenadines', 'VC', 'VCT', NULL, '670', 'saint-vincent-and-grenadines'),
('Venezuela', 'VE', 'VEN', NULL, '862', 'venezuela'),
('British Virgin Islands', 'VG', 'VGB', NULL, '92', 'british-virgin-islands'),
('U.S. Virgin Islands', 'VI', 'VIR', NULL, '850', 'us-virgin-islands'),
('Viet Nam', 'VN', 'VNM', NULL, '704', 'viet-nam'),
('Vanuatu', 'VU', 'VUT', NULL, '548', 'vanuatu'),
('Wallis and Futuna Islands', 'WF', 'WLF', NULL, '876', 'wallis-and-futuna-islands'),
('Samoa', 'WS', 'WSM', NULL, '882', 'samoa'),
('Yemen', 'YE', 'YEM', NULL, '887', 'yemen'),
('Mayotte', 'YT', 'MYT', NULL, '175', 'mayotte'),
('South Africa', 'ZA', 'ZAF', NULL, '710', 'south-africa'),
('Zambia', 'ZM', 'ZMB', NULL, '894', 'zambia'),
('Zimbabwe', 'ZW', 'ZWE', NULL, '716', 'zimbabwe');

-- --------------------------------------------------------

--
-- Table structure for table `global_config`
--

CREATE TABLE `global_config` (
  `id` int(11) NOT NULL,
  `repayment_automatic` enum('Yes','No') NOT NULL DEFAULT 'No',
  `cron_path` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `global_config`
--

INSERT INTO `global_config` (`id`, `repayment_automatic`, `cron_path`) VALUES
(1, 'No', 'Null'),
(2, 'Yes', 'feghgf');

-- --------------------------------------------------------

--
-- Table structure for table `groups`
--

CREATE TABLE `groups` (
  `group_id` int(11) NOT NULL,
  `group_code` varchar(200) NOT NULL,
  `group_name` varchar(200) NOT NULL,
  `group_category` int(11) DEFAULT NULL,
  `branch` int(11) NOT NULL,
  `group_description` text NOT NULL,
  `group_village` varchar(50) NOT NULL,
  `group_ta` varchar(50) NOT NULL,
  `group_district` varchar(50) NOT NULL,
  `group_address` text NOT NULL,
  `group_contact` varchar(50) NOT NULL,
  `group_email` varchar(50) NOT NULL,
  `group_established_date` date NOT NULL,
  `file` varchar(300) DEFAULT NULL,
  `approval_comment` text DEFAULT NULL,
  `reject_comment` text DEFAULT NULL,
  `account_name` varchar(50) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `bank_name` varchar(50) NOT NULL,
  `bank_branch` int(11) NOT NULL,
  `group_added_by` int(11) NOT NULL,
  `group_status` enum('Active','Approved','Pending','Deleted','Closed','Rejected') NOT NULL DEFAULT 'Pending',
  `group_registered_date` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `group_assigned_amount`
--

CREATE TABLE `group_assigned_amount` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `status` enum('Active','Closed','Pending','Rejected') NOT NULL DEFAULT 'Pending',
  `approval_comment` text DEFAULT NULL,
  `reject_comment` text DEFAULT NULL,
  `disbursed_by` varchar(200) NOT NULL,
  `date_disbursed` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `group_categories`
--

CREATE TABLE `group_categories` (
  `group_category_id` int(11) NOT NULL,
  `group_category_name` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `group_categories`
--

INSERT INTO `group_categories` (`group_category_id`, `group_category_name`) VALUES
(1, 'General group category'),
(2, 'Common problem');

-- --------------------------------------------------------

--
-- Table structure for table `group_loan_tracker`
--

CREATE TABLE `group_loan_tracker` (
  `group_loan_tracker_id` int(11) NOT NULL,
  `disbursement_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `group_loan_tracker`
--

INSERT INTO `group_loan_tracker` (`group_loan_tracker_id`, `disbursement_id`, `group_id`, `customer_id`, `amount`, `date_added`) VALUES
(1, 1, 1, 1, '100000.00', '2022-08-23 08:21:28');

-- --------------------------------------------------------

--
-- Table structure for table `individual_customers`
--

CREATE TABLE `individual_customers` (
  `id` int(11) NOT NULL,
  `ClientId` int(11) NOT NULL,
  `Title` varchar(20) DEFAULT NULL,
  `Firstname` varchar(50) DEFAULT NULL,
  `Middlename` varchar(50) DEFAULT NULL,
  `Lastname` varchar(50) DEFAULT NULL,
  `Gender` enum('MALE','FEMALE','OTHER') DEFAULT 'OTHER',
  `DateOfBirth` date DEFAULT NULL,
  `EmailAddress` varchar(60) NOT NULL,
  `PhoneNumber` varchar(50) DEFAULT NULL,
  `kinFullname` varchar(50) NOT NULL,
  `kinPhonenumber` varchar(50) NOT NULL,
  `kinEmailaddress` varchar(50) NOT NULL,
  `AddressLine1` varchar(250) DEFAULT NULL,
  `AddressLine2` varchar(250) DEFAULT NULL,
  `AddressLine3` varchar(200) DEFAULT NULL,
  `village` varchar(200) DEFAULT NULL,
  `marital` varchar(50) NOT NULL,
  `Province` varchar(200) DEFAULT NULL,
  `City` varchar(200) DEFAULT NULL,
  `Country` varchar(100) NOT NULL,
  `account_name` varchar(50) NOT NULL,
  `account_number` varchar(50) NOT NULL,
  `bank_name` varchar(50) NOT NULL,
  `bank_branch` int(11) DEFAULT NULL,
  `ResidentialStatus` varchar(200) DEFAULT NULL,
  `plot_number` varchar(50) NOT NULL,
  `Profession` varchar(250) DEFAULT NULL,
  `SourceOfIncome` varchar(250) DEFAULT NULL,
  `GrossMonthlyIncome` decimal(18,2) DEFAULT NULL,
  `Branch` varchar(200) NOT NULL,
  `customer_type` enum('individual','group') NOT NULL DEFAULT 'group',
  `LastUpdatedOn` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `CreatedOn` timestamp NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL,
  `approval_status` enum('Approved','Not Approved','Rejected') NOT NULL DEFAULT 'Not Approved',
  `added_by` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `inputloansprocessed`
--

CREATE TABLE `inputloansprocessed` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `group_code` int(11) NOT NULL,
  `CLIENTNAME` varchar(39) DEFAULT NULL,
  `Male` varchar(8) DEFAULT NULL,
  `Female` varchar(8) DEFAULT NULL,
  `LOCATION` varchar(10) DEFAULT NULL,
  `PHONENUMBER` varchar(24) DEFAULT NULL,
  `LOANNO` varchar(23) DEFAULT NULL,
  `LOANPRODUCT` varchar(10) DEFAULT NULL,
  `loan_product_id` int(11) NOT NULL DEFAULT 7,
  `disbursed_date` varchar(11) DEFAULT NULL,
  `TO` varchar(10) DEFAULT NULL,
  ` UPFRONTFEES` varchar(12) DEFAULT NULL,
  ` COLLATERAL` varchar(14) DEFAULT NULL,
  `LAMOUNT` varchar(15) DEFAULT NULL,
  ` INTEREST` varchar(14) DEFAULT NULL,
  ` LiNTEREST` varchar(15) DEFAULT NULL,
  `loan_period` int(11) DEFAULT NULL,
  `MREPAYMENT` varchar(14) DEFAULT NULL,
  ` TOTALLOAN` varchar(15) DEFAULT NULL,
  `AMOUNTPAID` varchar(15) DEFAULT NULL,
  ` LOAN BALANCE` varchar(15) DEFAULT NULL,
  `STATUS` varchar(8) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `inputloansprocessed`
--

INSERT INTO `inputloansprocessed` (`id`, `group_id`, `group_code`, `CLIENTNAME`, `Male`, `Female`, `LOCATION`, `PHONENUMBER`, `LOANNO`, `LOANPRODUCT`, `loan_product_id`, `disbursed_date`, `TO`, ` UPFRONTFEES`, ` COLLATERAL`, `LAMOUNT`, ` INTEREST`, ` LiNTEREST`, `loan_period`, `MREPAYMENT`, ` TOTALLOAN`, `AMOUNTPAID`, ` LOAN BALANCE`, `STATUS`) VALUES
(1, 504, 8610, ' MOZI MDUWA JTI CLUB ', ' 9.00 ', '', ' MCHINJI ', ' 0887003210/0997150359 ', ' GSMECG9 IN084/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 63,240.00 ', ' 316,200.00 ', ' 54,482.86 ', ' 370,682.86 ', 10, ' 37,068.29 ', ' 370,682.86 ', ' 442,500.00 ', '-71,817.14 ', 'CLOSED'),
(2, 505, 7921, ' KANYERERE MBWATALIKA JTI CLUB ', ' 21.00 ', '', ' LILONGWE ', ' 0998337842/ ', ' GSMES   IN0112/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 63,240.00 ', ' 316,200.00 ', ' 54,482.86 ', ' 370,682.86 ', 10, ' 37,068.29 ', ' 370,682.86 ', ' 370,685.00 ', '-2.14 ', 'CLOSED'),
(3, 506, 7987, ' NGOLOMI JTI KAWERE ', ' 9.00 ', ' 1.00 ', ' MCHINJI ', ' 0999187305/0996603505 ', ' GSMES   IN0116/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 137,942.81 ', ' 689,714.07 ', ' 118,814.23 ', ' 808,528.30 ', 10, ' 80,852.83 ', ' 808,528.30 ', ' 811,500.00 ', '-2,971.70 ', 'CLOSED'),
(4, 507, 6386, ' TIKONDANE THOMAS FIOSI JTI ', ' 8.00 ', ' 3.00 ', ' MCHINJI ', ' 0999235849/0996577359 ', ' GSMES  IN0118/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', '', ' 368,900.00 ', ' 63,563.34 ', ' 432,463.34 ', 10, ' 43,246.33 ', ' 432,463.34 ', ' 481,545.00 ', '-49,081.66 ', 'CLOSED'),
(5, 508, 909, ' MADZIDZI JTI CLUB MNDEWE ', ' 29.00 ', ' 2.00 ', ' KASUNGU ', ' 0999304206/0999009988 ', ' GSMES IN 108/21 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 179,180.00 ', ' 895,900.00 ', ' 154,368.11 ', ' 1,050,268.11 ', 10, ' 105,026.81 ', ' 1,050,268.11 ', ' 1,068,900.00 ', '-18,631.89 ', 'CLOSED'),
(6, 509, 4517, ' LEMWE JTI KALOLO CLUB ', ' 11.00 ', ' 1.00 ', ' Lilongwe ', ' 0885595947/0995732968 ', ' GSMES IN008/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 969,000.00 ', ' 266,609.54 ', 'ACTIVE'),
(7, 510, 5459, ' KATONDO JTI SITAMBO ', ' 12.00 ', '', ' LILONGWE ', ' 0999782242/0995337428 ', ' GSMES IN009/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 21,080.00 ', ' 105,400.00 ', ' 18,160.95 ', ' 123,560.95 ', 10, ' 12,356.10 ', ' 123,560.95 ', ' 123,600.00 ', '-39.05 ', 'CLOSED'),
(8, 511, 9740, ' MDIKA COOPERATIVE ', ' 31.00 ', ' 6.00 ', ' DOWA ', ' 0999049707/0998217771 ', ' GSMES IN0104/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 453,220.00 ', ' 2,266,100.00 ', ' 390,460.51 ', ' 2,656,560.51 ', 10, ' 265,656.05 ', ' 2,656,560.51 ', ' 2,656,560.51 ', ' -   ', 'CLOSED'),
(9, 512, 9178, ' TIDZIWANE KAMPHATA JTI CLUB ', ' 7.00 ', ' 2.00 ', ' LILONGWE ', ' 0996744029 ', ' GSMES IN0113/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 94,860.00 ', ' 474,300.00 ', ' 81,724.29 ', ' 556,024.29 ', 10, ' 55,602.43 ', ' 556,024.29 ', ' 482,470.00 ', ' 73,554.29 ', 'ACTIVE'),
(10, 513, 3962, ' SANTHE JTI CLUB ', ' 5.00 ', ' 2.00 ', ' KASUNGU ', ' 0991626178/0990719875 ', ' GSMES IN0114/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 189,720.00 ', ' 948,600.00 ', ' 163,448.59 ', ' 1,112,048.59 ', 10, ' 111,204.86 ', ' 1,112,048.59 ', ' 1,092,900.00 ', ' 19,148.59 ', 'ACTIVE'),
(11, 514, 8465, ' CHENGWE COOPERATIVE ', ' 28.00 ', ' 52.00 ', ' KASUNGU ', ' 0999807631/0995229385 ', ' GSMES IN016/2021 ', ' SERENADE ', 7, '25//10/2021', '25/07/2022', ' 181,551.50 ', ' 594,620.00 ', ' 2,793,100.00 ', ' 481,265.28 ', ' 3,274,365.28 ', 10, ' 327,436.53 ', ' 3,274,365.28 ', ' 3,274,365.28 ', ' -   ', 'CLOSED'),
(12, 515, 8075, ' SUNDWE JTI CLUB ', ' 14.00 ', ' 3.00 ', ' MCHINJI ', ' 0998877288/0999660510 ', ' GSMES IN0177/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' 597,580.00 ', ' 20,224.77 ', 'ACTIVE'),
(13, 516, 8545, ' MWALAWANYENJE MALANGANO COOPERATIVE ', ' 22.00 ', ' 23.00 ', ' Dedza ', ' 0998402866/0995346788 ', ' GSMES IN018/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 463,760.00 ', ' 2,318,800.00 ', ' 399,540.99 ', ' 2,718,340.99 ', 10, ' 271,834.10 ', ' 2,718,340.99 ', ' 2,718,340.99 ', ' -   ', 'CLOSED'),
(14, 517, 5741, ' NAMBUMA CHIDEZA COOPERATIVE ', ' 204.00 ', ' 309.00 ', ' DOWA ', ' 0996843853/0998793028 ', ' GSMES IN103/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 1,370,200.00 ', ' 6,851,000.00 ', ' 1,180,462.01 ', ' 8,031,462.01 ', 10, ' 803,146.20 ', ' 8,031,462.01 ', ' 5,000,000.00 ', ' 3,031,462.01 ', 'ACTIVE'),
(15, 518, 1314, ' KAKUDA CHIMGONDA ', ' 9.00 ', ' 2.00 ', ' MCHINJI ', ' 0999113684/0993226973 ', ' GSMES IN106/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 137,020.00 ', ' 685,100.00 ', ' 118,046.20 ', ' 803,146.20 ', 10, ' 80,314.62 ', ' 803,146.20 ', ' 803,150.00 ', '-3.80 ', 'CLOSED'),
(16, 519, 2795, ' CHITU JTI MCHELEKA CLUB ', ' 24.00 ', ' 2.00 ', ' KASUNGU ', ' 0997695232 ', ' GSMES IN109/21 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 126,480.00 ', ' 632,400.00 ', ' 108,965.72 ', ' 741,365.72 ', 10, ' 74,136.57 ', ' 741,365.72 ', ' 741,370.00 ', '-4.28 ', 'CLOSED'),
(17, 520, 204, ' CHIPALA NETWORK ', ' 363.00 ', ' 423.00 ', ' KASUNGU ', ' 0999212143 ', ' GSMES IN17/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', ' 205,530.00 ', ' 632,400.00 ', ' 3,162,000.00 ', ' 544,828.62 ', ' 3,706,828.62 ', 10, ' 370,682.86 ', ' 3,706,828.62 ', ' 2,479,650.00 ', ' 1,227,178.62 ', 'ACTIVE'),
(18, 521, 6328, ' KABWAZI JTI  ', ' 6.00 ', ' 6.00 ', ' DEDZA ', ' 0999809602/0996729140 ', ' GSMESIN001/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 274,040.00 ', ' 1,370,200.00 ', ' 236,092.40 ', ' 1,606,292.40 ', 10, ' 160,629.24 ', ' 1,606,292.40 ', ' 1,606,292.48 ', '-0.08 ', 'CLOSED'),
(19, 522, 1530, ' SAGAWA JTI ZONE ', ' 35.00 ', ' 11.00 ', ' DEDZA ', ' 0992206707/0998360127 ', ' GSMESIN002/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 537,540.00 ', ' 2,687,700.00 ', ' 463,104.33 ', ' 3,150,804.33 ', 10, ' 315,080.43 ', ' 3,150,804.33 ', ' 3,150,650.00 ', ' 154.33 ', 'CLOSED'),
(20, 523, 8608, ' MCHENGA JTI MPHONDE ', ' 10.00 ', ' 1.00 ', ' DEDZA ', ' 0993748233/0991556029 ', ' GSMESIN003/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 126,480.00 ', ' 632,400.00 ', ' 108,965.72 ', ' 741,365.72 ', 10, ' 74,136.57 ', ' 741,365.72 ', ' 741,365.72 ', ' -   ', 'CLOSED'),
(21, 524, 9958, ' MASOMPHENYA COOPERATIVE  ', ' 34.00 ', ' 36.00 ', ' DEDZA ', ' 0994889163/0994083140 ', ' GSMESIN004/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 579,700.00 ', ' 2,898,500.00 ', ' 499,426.23 ', ' 3,397,926.23 ', 10, ' 339,792.62 ', ' 3,397,926.23 ', ' 3,397,926.23 ', ' -   ', 'CLOSED'),
(22, 525, 4792, ' ST DOMINIC TEACHERS  ', '', ' 5.00 ', ' MCHINJI ', ' 0995446871 ', ' GSMESIN008/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 147,560.00 ', ' 737,800.00 ', ' 127,126.68 ', ' 864,926.68 ', 10, ' 86,492.67 ', ' 864,926.68 ', ' 824,700.00 ', ' 40,226.68 ', 'ACTIVE'),
(23, 526, 9974, ' LIFIDZI JTI KACHERE ', ' 27.00 ', ' 5.00 ', ' DEDZA ', ' 0999809602/0996729140 ', ' GSMESIN010/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 94,860.00 ', ' 474,300.00 ', ' 81,724.29 ', ' 556,024.29 ', 10, ' 55,602.43 ', ' 556,024.29 ', ' 556,050.00 ', '-25.71 ', 'CLOSED'),
(24, 527, 9672, ' KALINDE MDUWA JTI ', ' 12.00 ', ' 4.00 ', ' MCHINJI ', ' 0999497041/0999066515 ', ' GSMESIN041/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 147,560.00 ', ' 737,800.00 ', ' 127,126.68 ', ' 864,926.72 ', 10, ' 86,492.67 ', ' 864,926.72 ', ' 721,550.00 ', ' 143,376.72 ', 'ACTIVE'),
(25, 528, 3982, ' KABWINJA JTI CLUB ', ' 14.00 ', ' 1.00 ', ' KASUNGU ', ' 0999311382/0880853710 ', ' GSMs IN0/2021 ', ' SERENADE ', 7, '25/10/2021', '25/07/2022', '', ' 115,940.00 ', ' 579,700.00 ', ' 99,885.25 ', ' 679,585.25 ', 10, ' 67,958.53 ', ' 679,585.25 ', ' 247,250.00 ', ' 432,335.25 ', 'ACTIVE'),
(26, 529, 3284, ' MKANDA COOPERATIVE ', ' 27.00 ', ' 45.00 ', ' MCHINJI ', ' 0991752589/0998150235 ', ' GSME T IN031/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 1,056,343.70 ', ' 179,265.84 ', 'ACTIVE'),
(27, 530, 8035, ' VALLEY OF ABUNDANCE INVESTMENT ', ' 18.00 ', ' 22.00 ', ' LILONGWE ', ' 0992876777/0999258305 ', ' GSME T IN056/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 1,235,700.00 ', '-90.46 ', 'CLOSED'),
(28, 531, 175, ' ANGEL CLUB ', ' 21.00 ', ' 9.00 ', ' MZIMBA ', ' 0992333934 ', ' GSME T IN057/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 316,200.00 ', ' 1,581,000.00 ', ' 272,414.31 ', ' 1,853,414.31 ', 10, ' 185,341.43 ', ' 1,853,414.31 ', ' -   ', ' 1,853,414.31 ', 'ACTIVE'),
(29, 532, 3805, ' JAULANI CLUB ', ' 5.00 ', ' 10.00 ', ' MZIMBA ', ' 0994704050 ', ' GSME T IN60/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' -   ', ' 617,804.77 ', 'ACTIVE'),
(30, 533, 1906, ' MGWIRIZANO PRE COOPERATIVE ', '', '', ' MZIMBA ', ' 0882294563 ', ' GSME T IN63/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,317,500.00 ', ' 227,011.92 ', ' 1,544,511.92 ', 10, ' 154,451.19 ', ' 1,544,511.92 ', ' -   ', ' 1,544,511.92 ', 'ACTIVE'),
(31, 534, 1069, ' MWAZISI NASFAM ', ' 12.00 ', ' 9.00 ', ' RUMPHI ', ' 0992156536 ', ' GSME T IN70/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 79,050.00 ', ' 395,250.00 ', ' 68,103.58 ', ' 463,353.58 ', 10, ' 46,335.36 ', ' 463,353.58 ', ' -   ', ' 463,353.58 ', 'ACTIVE'),
(32, 535, 974, ' MWANGALA ACTION GROUP ', '', ' 8.00 ', ' DOWA ', ' 0885505276/0990194516 ', ' GSMECT IN052/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 42,160.00 ', ' 210,800.00 ', ' 36,321.91 ', ' 247,121.91 ', 10, ' 24,712.19 ', ' 247,121.91 ', ' 245,400.00 ', ' 1,721.91 ', 'ACTIVE'),
(33, 536, 2624, ' PHOKERA MKANTHAMA CLUB ', ' 63.00 ', ' 3.00 ', ' DOWA ', ' 0991967080/0992890988 ', ' GSMELL IN012/2040 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 84,320.00 ', ' 421,600.00 ', ' 72,643.82 ', ' 494,243.82 ', 10, ' 49,424.38 ', ' 494,243.82 ', ' 297,290.00 ', ' 196,953.82 ', 'ACTIVE'),
(34, 537, 2471, ' PHALASITU CHITANDIKA COOPERATIVE ', ' 16.00 ', ' 14.00 ', ' RUMPHI ', ' 0995278862/0884033134 ', ' GSMELL IN068/2029 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 191,037.50 ', ' 955,187.50 ', ' 164,583.65 ', ' 1,119,771.15 ', 10, ' 111,977.12 ', ' 1,119,771.15 ', ' 754,250.00 ', ' 365,521.15 ', 'ACTIVE'),
(35, 538, 636, ' MWALA JTINTHANDO ', ' 9.00 ', ' 1.00 ', ' LILONGWE ', ' 0999412118/0993716103 ', ' GSMES IN 107/21 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 926,750.00 ', '-42.85 ', 'CLOSED'),
(36, 539, 1235, ' KALUNDAMAWE MTHWALO CLUB ', ' 11.00 ', ' 3.00 ', ' MZIMBA ', ' 0884216751/0991743305 ', ' GSMEST IN061/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 177,862.50 ', ' 889,312.50 ', ' 153,233.05 ', ' 1,042,545.55 ', 10, ' 104,254.56 ', ' 1,042,545.55 ', ' 754,300.00 ', ' 288,245.55 ', 'ACTIVE'),
(37, 540, 128, ' KAPINDA COOPERATIVE ', ' 14.00 ', ' 8.00 ', ' MZIMBA ', ' 0888388806/0886896069 ', ' GSMEST IN062/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 899,700.00 ', ' 335,909.54 ', 'ACTIVE'),
(38, 541, 7816, ' TASANGANAPO COOPERATIVE ', ' 5.00 ', ' 7.00 ', ' MZIMBA ', ' 0993597266/0992488004 ', ' GSMEST IN064/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 138,337.50 ', ' 691,687.50 ', ' 119,181.26 ', ' 810,868.76 ', 10, ' 81,086.88 ', ' 810,868.76 ', ' 810,070.00 ', ' 798.76 ', 'ACTIVE'),
(39, 542, 2177, ' MOZERA KATAWA CLUB ', ' 16.00 ', ' 8.00 ', ' MZIMBA ', ' 0883942826/0883941749 ', ' GSMEST IN069/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 717,100.00 ', ' 209,607.15 ', 'ACTIVE'),
(40, 543, 4885, ' KAVIYELE KAPOLO CLUB ', ' 4.00 ', ' 12.00 ', ' KASUNGU ', ' 0991395165/0995382012 ', ' GSMET  IN016/2041 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 168,640.00 ', ' 843,200.00 ', ' 145,287.63 ', ' 988,487.63 ', 10, ' 98,848.76 ', ' 988,487.63 ', ' 265,300.00 ', ' 723,187.63 ', 'ACTIVE'),
(41, 544, 8987, ' NSUNDWE KALOLO CENTRE ', ' 5.00 ', '', ' LILONGWE ', ' 0995338818/0992335033 ', ' GSMET IN 055/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 158,100.00 ', ' 27,241.43 ', ' 185,341.43 ', 10, ' 18,534.14 ', ' 185,341.43 ', ' 261,110.00 ', '-75,768.57 ', 'CLOSED'),
(42, 545, 1767, ' CHIGOMEZGO MWACHIIUTA CLUB ', ' 5.00 ', ' 10.00 ', ' MZIMBA ', ' 0880173521/0886570872 ', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 309,500.00 ', ' 617,207.15 ', 'ACTIVE'),
(43, 546, 7606, ' UMODZI COOPERATIVE ', '', '', ' LILONGWE ', '', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 274,040.00 ', ' 1,370,200.00 ', ' 236,092.40 ', ' 1,606,292.40 ', 10, ' 160,629.24 ', ' 1,606,292.40 ', ' 865,000.00 ', ' 741,292.40 ', 'ACTIVE'),
(44, 547, 821, ' KALAMBE CLUB ', ' 31.00 ', ' 28.00 ', ' Lilongwe ', ' 0999780225/0992994809 ', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 548,080.00 ', ' 2,740,400.00 ', ' 472,184.80 ', ' 3,212,584.80 ', 10, ' 321,258.48 ', ' 3,212,584.80 ', ' 3,212,600.00 ', '-15.20 ', 'CLOSED'),
(45, 548, 3106, ' CHIUNGO COOPERATIVE ', '', '', '', '', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 621,860.00 ', ' 3,109,300.00 ', ' 535,748.14 ', ' 3,645,048.14 ', 10, ' 364,504.81 ', ' 3,645,048.14 ', ' 3,645,048.32 ', '-0.18 ', 'CLOSED'),
(46, 549, 2498, ' SENDWE MAWAWA JTI CLUB ', ' 5.00 ', '', ' KASUNGU ', ' 0993271919/0999261701 ', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 316,200.00 ', ' 54,482.86 ', ' 370,682.86 ', 10, ' 37,068.29 ', ' 370,682.86 ', ' 373,565.00 ', '-2,882.14 ', 'CLOSED'),
(47, 550, 8221, ' KABWINJA JTI CLUB ', ' 14.00 ', ' 1.00 ', ' KASUNGU ', ' 0999311382/0880853710 ', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 316,200.00 ', ' 54,482.86 ', ' 370,682.86 ', 10, ' 37,068.29 ', ' 370,682.86 ', ' 137,100.00 ', ' 233,582.86 ', 'ACTIVE'),
(48, 551, 5029, ' CHIGODI COOPERATIVE ', ' 5.00 ', ' 2.00 ', ' KASUNGU ', ' 0998720106/0995383837 ', ' GSMET IN0/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,897,200.00 ', ' 326,897.28 ', ' 2,224,097.28 ', 10, ' 222,409.73 ', ' 2,224,097.28 ', ' 2,170,650.00 ', ' 53,447.28 ', 'ACTIVE'),
(49, 552, 5236, ' LEMWE JTI KALOLO CLUB ', ' 11.00 ', ' 1.00 ', ' Lilongwe ', ' 0885595947/0995732968 ', ' GSMET IN0029/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 400,520.00 ', ' 2,002,600.00 ', ' 345,058.13 ', ' 2,347,658.13 ', 10, ' 234,765.81 ', ' 2,347,658.13 ', ' 2,261,420.00 ', ' 86,238.13 ', 'ACTIVE'),
(50, 553, 9985, ' CHIGODI SANTHE JTI CLUB ', ' 5.00 ', ' 2.00 ', ' KASUNGU ', ' 0998720106/0995383837 ', ' GSMET IN0078/21 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 73,780.00 ', ' 368,900.00 ', ' 63,563.34 ', ' 432,463.34 ', 10, ' 43,246.33 ', ' 432,463.34 ', ' 432,470.00 ', '-6.66 ', 'CLOSED'),
(51, 554, 2150, ' KALINGULE CLUB ', ' 14.00 ', ' 14.00 ', ' DOWA ', ' 0995852388/0999250758 ', ' GSMET IN010/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 242,420.00 ', ' 1,212,100.00 ', ' 208,850.97 ', ' 1,420,950.97 ', 10, ' 142,095.10 ', ' 1,420,950.97 ', ' 594,462.88 ', ' 826,488.09 ', 'ACTIVE'),
(52, 555, 9118, ' MTAMBALIKA COOPERATIVE ', '', '', ' DOWA ', ' 0999737893/0993191966 ', ' GSMET IN011/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 895,900.00 ', ' 154,368.16 ', ' 1,050,268.16 ', 10, ' 105,026.82 ', ' 1,050,268.16 ', ' 1,118,550.00 ', '-68,281.84 ', 'CLOSED'),
(53, 556, 7064, ' NAMANDA CHIWERE COOPERATIVE  ', ' 62.00 ', ' 110.00 ', ' DOWA ', ' 0993282057/0983422399 ', ' GSMET IN013/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 1,235,609.60 ', '-0.06 ', 'CLOSED'),
(54, 557, 3195, ' MKOMBE COOPERATIVE ', ' 25.00 ', ' 40.00 ', ' DOWA ', ' 0888813346/0991838378 ', ' GSMET IN014/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', ' 30,829.00 ', ' 94,860.00 ', ' 474,300.00 ', ' 81,724.29 ', ' 556,024.29 ', 10, ' 55,602.43 ', ' 556,024.29 ', ' 466,000.00 ', ' 90,024.29 ', 'ACTIVE'),
(55, 558, 6928, ' TIKONDANE CHIMUCHIMBA ', '', '', '', '', ' GSMET IN015/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 790,500.00 ', ' 136,207.20 ', ' 926,707.20 ', 10, ' 92,670.72 ', ' 926,707.20 ', ' 334,000.00 ', ' 592,707.20 ', 'ACTIVE'),
(56, 559, 5856, ' MAFOMB CLUB ', '', '', '', '', ' GSMET IN017/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,212,100.00 ', ' 208,850.97 ', ' 1,420,950.97 ', 10, ' 142,095.10 ', ' 1,420,950.97 ', ' 1,323,000.00 ', ' 97,950.97 ', 'ACTIVE'),
(57, 560, 598, ' SIMULEMBA COOPERATIVE ', '', '', '', '', ' GSMET IN018/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,245,037.50 ', ' 214,526.27 ', ' 1,459,563.77 ', 10, ' 145,956.38 ', ' 1,459,563.77 ', ' 1,459,564.00 ', '-0.23 ', 'CLOSED'),
(58, 561, 3047, ' CHAMALAZA NDAYA CLUB ', ' 9.00 ', ' 11.00 ', ' KASUNGU ', ' 0991663400 ', ' GSMET IN019/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 73,780.00 ', ' 368,900.00 ', ' 63,563.34 ', ' 432,463.34 ', 10, ' 43,246.33 ', ' 432,463.34 ', ' 330,000.00 ', ' 102,463.34 ', 'ACTIVE'),
(59, 562, 1663, ' KAPYERA LUKWA CLUB ', ' 3.00 ', ' 3.00 ', ' KASUNGU ', ' 0992222847/0996809188 ', ' GSMET IN020/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 42,160.00 ', ' 210,800.00 ', ' 36,321.91 ', ' 247,121.91 ', 10, ' 24,712.19 ', ' 247,121.91 ', ' -   ', ' 247,121.91 ', 'ACTIVE'),
(60, 563, 6902, ' CHIWOLA CHIWOKO COOPERATIVE ', ' 76.00 ', ' 66.00 ', ' Lilongwe ', ' 0996672632/0996035499 ', ' GSMET IN021/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 864,280.00 ', ' 4,321,400.00 ', ' 744,599.11 ', ' 5,065,999.11 ', 10, ' 506,599.91 ', ' 5,065,999.11 ', ' 5,066,080.00 ', '-80.89 ', 'CLOSED'),
(61, 564, 6587, ' BOWE JTI CHANKHAZA CLUB ', ' 24.00 ', '', ' DOWA ', ' 0995630430/0991717935 ', ' GSMET IN022/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 400,520.00 ', ' 2,002,600.00 ', ' 345,058.13 ', ' 2,347,658.13 ', 10, ' 234,765.81 ', ' 2,347,658.13 ', ' 2,347,660.00 ', '-1.87 ', 'CLOSED'),
(62, 565, 8688, ' CHISEPO FARMRS NAMBUMA JTI CLUB ', ' 33.00 ', ' 2.00 ', ' DOWA ', ' 0995252060/0994300833 ', ' GSMET IN025/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 368,900.00 ', ' 1,844,500.00 ', ' 317,816.69 ', ' 2,162,316.69 ', 10, ' 216,231.67 ', ' 2,162,316.69 ', ' 2,176,595.00 ', '-14,278.31 ', 'CLOSED'),
(63, 566, 9408, ' NKHUNGUYEMBE COOPERATIVE ', ' 84.00 ', ' 40.00 ', ' LILONGWE ', ' 0998462875/0999405267 ', ' GSMET IN026/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,168,700.00 ', ' 201,372.93 ', ' 1,853,414.40 ', 10, ' 185,341.44 ', ' 1,853,414.40 ', ' 1,853,950.00 ', '-535.60 ', 'CLOSED'),
(64, 567, 7637, ' CHIFUKULA ELISA CHAKHAZA JTI CLUB ', ' 18.00 ', ' 3.00 ', ' DOWA ', ' 0994042993/0991114949 ', ' GSMET IN027/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 316,200.00 ', ' 1,581,000.00 ', ' 272,414.31 ', ' 1,853,414.31 ', 10, ' 185,341.43 ', ' 1,853,414.31 ', ' 922,270.00 ', ' 931,144.31 ', 'ACTIVE'),
(65, 568, 825, ' NAMBUMA JTI ZONE ', ' 24.00 ', '', ' DOWA ', ' 0993955440/0992125743 ', ' GSMET IN028/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 252,960.00 ', ' 1,264,800.00 ', ' 217,931.45 ', ' 1,482,731.45 ', 10, ' 148,273.15 ', ' 1,482,731.45 ', ' 1,482,731.45 ', ' -   ', 'CLOSED'),
(66, 569, 4726, ' KADWALA JTI CLUB ', ' 5.00 ', ' 5.00 ', ' KASUNGU ', ' 0995155312/0995187947 ', ' GSMET IN030/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 486,000.00 ', ' 749,609.54 ', 'ACTIVE'),
(67, 570, 6858, ' NYUTU SAKUDYA CLUB ', ' -   ', ' 19.00 ', ' Lilongwe ', ' 0997078890/0992911034 ', ' GSMET IN032/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 200,260.00 ', ' 1,001,300.00 ', ' 172,529.06 ', ' 1,173,829.06 ', 10, ' 117,382.91 ', ' 1,173,829.06 ', ' 1,173,829.12 ', '-0.06 ', 'CLOSED'),
(68, 571, 4206, ' Tadala 4 Club Nsambila ', ' 10.00 ', ' 7.00 ', ' Lilongwe ', ' 0992022983/0990337590 ', ' GSMET IN033/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 200,260.00 ', ' 1,001,300.00 ', ' 172,529.06 ', ' 1,173,829.06 ', 10, ' 117,382.91 ', ' 1,173,829.06 ', ' 1,042,000.00 ', ' 131,829.06 ', 'ACTIVE'),
(69, 572, 1584, ' TIDZIWANE COOPERATIVE  ', ' 25.00 ', ' 50.00 ', ' Lilongwe ', ' 0999119936/0999061040 ', ' GSMET IN034/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 189,720.00 ', ' 948,600.00 ', ' 163,448.59 ', ' 1,112,048.59 ', 10, ' 111,204.86 ', ' 1,112,048.59 ', ' 1,112,048.59 ', ' -   ', 'CLOSED'),
(70, 573, 462, ' NDAULA JTI CLUB ', ' 15.00 ', '', ' Lilongwe ', ' 0999737893/0993191966 ', ' GSMET IN035/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 960,960.00 ', '-34,252.85 ', 'CLOSED'),
(71, 574, 1177, ' MTAMBALIKA JTI CLUB ', ' 7.00 ', '', ' DOWA ', ' 0991254885/0999705648 ', ' GSMET IN036/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 656,745.00 ', ' 269,962.15 ', 'ACTIVE'),
(72, 575, 5135, ' DEMELA COOPERATIVE ', ' 100.00 ', ' 115.00 ', ' Lilongwe ', ' 0999042275/0997708860 ', ' GSMET IN037/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 158,100.00 ', ' 790,500.00 ', ' 136,207.15 ', ' 926,707.15 ', 10, ' 92,670.72 ', ' 926,707.15 ', ' 926,750.00 ', '-42.85 ', 'CLOSED'),
(73, 576, 987, ' CHITU JTI MCHELEKA CLUB ', ' 25.00 ', ' 1.00 ', ' KASUNGU ', ' 0997695232/0991076059 ', ' GSMET IN038/202 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 137,020.00 ', ' 685,100.00 ', ' 118,046.20 ', ' 803,146.20 ', 10, ' 80,314.62 ', ' 803,146.20 ', ' 803,185.00 ', '-38.80 ', 'CLOSED'),
(74, 577, 3705, ' CHIMANGENI JTI MTIMBULA CLUB ', ' 15.00 ', ' 1.00 ', ' KASUNGU ', ' 0999837992/0999474713 ', ' GSMET IN039/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 137,020.00 ', ' 685,100.00 ', ' 118,046.20 ', ' 803,146.20 ', 10, ' 80,314.62 ', ' 803,146.20 ', ' 679,620.00 ', ' 123,526.20 ', 'ACTIVE'),
(75, 578, 3411, ' CHIDONGO JTI CLU ', ' 12.00 ', ' -   ', ' KASUNGU ', ' 0994487200/0995979310 ', ' GSMET IN040/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 126,480.00 ', ' 632,400.00 ', ' 108,965.72 ', ' 741,365.72 ', 10, ' 74,136.57 ', ' 741,365.72 ', ' 697,890.00 ', ' 43,475.72 ', 'ACTIVE'),
(76, 579, 9718, ' KABUNTHU JTI CLUB ', ' 3.00 ', ' 7.00 ', ' MTCHINJI ', ' 0993464071/0997208099 ', ' GSMET IN041/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 126,480.00 ', ' 632,400.00 ', ' 108,965.72 ', ' 741,365.72 ', 10, ' 74,136.57 ', ' 741,365.72 ', ' 272,000.00 ', ' 469,365.72 ', 'ACTIVE'),
(77, 580, 4851, ' JUMA JTI CLUB  ', '', ' 11.00 ', ' DOWA ', ' 0995973367 ', ' GSMET IN042/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 115,940.00 ', ' 579,700.00 ', ' 99,885.25 ', ' 679,585.25 ', 10, ' 67,958.53 ', ' 679,585.25 ', ' 673,685.00 ', ' 5,900.25 ', 'ACTIVE'),
(78, 581, 6547, ' KAPHASO JTI CLUB ', ' 9.00 ', ' 1.00 ', ' KASUNGU ', ' 0999528384/0999780217 ', ' GSMET IN043/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' 617,810.00 ', '-5.23 ', 'CLOSED'),
(79, 582, 2181, ' MWAVU JTI CLUB  ', ' 9.00 ', ' 3.00 ', ' DOWA ', ' 0999635223 ', ' GSMET IN044/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 73,780.00 ', ' 368,900.00 ', ' 63,563.34 ', ' 432,463.34 ', 10, ' 43,246.33 ', ' 432,463.34 ', ' 253,560.00 ', ' 178,903.34 ', 'ACTIVE'),
(80, 583, 5988, ' KAWATA SANTHE JTI CLUB ', ' 12.00 ', '', ' KASUNGU ', ' 0996766249/0993953945 ', ' GSMET IN045/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 73,780.00 ', ' 368,900.00 ', ' 63,563.34 ', ' 432,463.34 ', 10, ' 43,246.33 ', ' 432,463.34 ', ' 435,895.00 ', '-3,431.66 ', 'CLOSED'),
(81, 584, 9575, ' KAMPHURU JTI CLUB  ', ' 9.00 ', ' 1.00 ', ' KASUNGU ', ' 0999393563/0999474867 ', ' GSMET IN047/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 63,240.00 ', ' 316,200.00 ', ' 54,482.86 ', ' 370,682.86 ', 10, ' 37,068.29 ', ' 370,682.86 ', ' 370,685.00 ', '-2.14 ', 'CLOSED'),
(82, 585, 3376, ' CHIKANDE JTI M\'NKOMA CLUB ', ' 11.00 ', ' 3.00 ', ' KASUNGU ', ' 0998330302/0999420801 ', ' GSMET IN048/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 63,240.00 ', ' 316,200.00 ', ' 316,200.00 ', ' 632,400.00 ', 10, ' 63,240.00 ', ' 632,400.00 ', ' 164,000.00 ', ' 468,400.00 ', 'ACTIVE'),
(83, 586, 3722, ' DAMA JTI CLUB ', ' 5.00 ', ' -   ', ' Lilongwe ', ' 0995374809/0998275141 ', ' GSMET IN049/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 52,700.00 ', ' 263,500.00 ', ' 45,402.38 ', ' 308,902.38 ', 10, ' 30,890.24 ', ' 308,902.38 ', ' 312,123.00 ', '-3,220.62 ', 'CLOSED'),
(84, 587, 3023, ' SANGA COOPERATIVE ', ' 40.00 ', ' 20.00 ', ' DOWA ', ' 0991410428/0996101503 ', ' GSMET IN05/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', ' 54,808.00 ', ' 168,640.00 ', ' 843,200.00 ', ' 145,287.63 ', ' 988,487.63 ', 10, ' 98,848.76 ', ' 988,487.63 ', ' 988,487.63 ', ' -   ', 'CLOSED'),
(85, 588, 1568, ' CHAWANTHA JTI CLUB ', ' 5.00 ', '', ' Lilongwe ', ' 0999645420/0999296506 ', ' GSMET IN050/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 52,700.00 ', ' 263,500.00 ', ' 45,402.38 ', ' 308,902.38 ', 10, ' 30,890.24 ', ' 308,902.38 ', ' 375,120.00 ', '-66,217.62 ', 'CLOSED'),
(86, 589, 3043, ' SIYAYE LUKWA JTI CLUB ', ' 10.00 ', '', ' KASUNGU ', ' 0995723374/0999872872 ', ' GSMET IN051/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 263,500.00 ', ' 45,402.38 ', ' 308,902.38 ', 10, ' 30,890.24 ', ' 308,902.38 ', ' 205,460.00 ', ' 103,442.38 ', 'ACTIVE'),
(87, 590, 3797, ' MKANGA ZOLIRE JTI CLUB ', ' 6.00 ', '', ' LILONGWE ', ' 0999914991/0999492631 ', ' GSMET IN053/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', '', ' 158,100.00 ', ' 27,241.43 ', ' 185,341.43 ', 10, ' 18,534.14 ', ' 185,341.43 ', ' 185,345.00 ', '-3.57 ', 'CLOSED'),
(88, 591, 921, ' ENGUCWINI MTWALO COOPERATIVE ', ' 99.00 ', ' 252.00 ', ' MZIMBA ', ' 0994630668/0884071966 ', ' GSMET IN059/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 790,500.00 ', ' 3,952,500.00 ', ' 681,035.77 ', ' 4,633,535.77 ', 10, ' 463,353.58 ', ' 4,633,535.77 ', ' 1,458,400.00 ', ' 3,175,135.77 ', 'ACTIVE'),
(89, 592, 860, ' TSOGOLO CHAPUMA COOPERATIVE ', ' 10.00 ', ' 24.00 ', ' DOWA ', ' **********/********** ', ' GSMET IN06/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 168,640.00 ', ' 843,200.00 ', ' 145,287.63 ', ' 988,487.63 ', 10, ' 98,848.76 ', ' 988,487.63 ', ' 988,487.63 ', ' -   ', 'CLOSED'),
(90, 593, 632, ' TILITONSE BONGOLOLO BOLERO CLUB ', ' 7.00 ', ' 11.00 ', ' RUMPHI ', ' 0881362306/0881974666 ', ' GSMET IN065/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 52,700.00 ', ' 263,500.00 ', ' 45,402.38 ', ' 308,902.38 ', 10, ' 30,890.24 ', ' 308,902.38 ', ' 300,000.00 ', ' 8,902.38 ', 'ACTIVE'),
(91, 594, 8811, ' VIWEMI KAMALAVINDA BOLERO CLUB ', ' 11.00 ', ' 4.00 ', ' MZIMBA ', ' 0881926466/0993790395 ', ' GSMET IN067/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' 462,000.00 ', ' 155,804.77 ', 'ACTIVE'),
(92, 595, 2608, ' MWALAWANYENJE MALANGANO COOPERATIVE ', '', '', ' KASUNGU ', ' 0997091321/0995511781 ', ' GSMET IN07/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 527,000.00 ', ' 2,635,000.00 ', ' 454,023.85 ', ' 3,089,023.85 ', 10, ' 308,902.39 ', ' 3,089,023.85 ', ' 1,601,000.00 ', ' 1,488,023.85 ', 'ACTIVE'),
(93, 596, 9750, ' KATEMA COOPERATIVE ', ' 20.00 ', ' 26.00 ', ' KASUNGU ', ' 0999729234/0992821899 ', ' GSMET IN08/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', ' 58,233.50 ', ' 176,180.00 ', ' 895,900.00 ', ' 154,368.11 ', ' 1,050,268.11 ', 10, ' 105,026.81 ', ' 1,050,268.11 ', ' 120,000.00 ', ' 930,268.11 ', 'ACTIVE'),
(94, 597, 7628, ' KHAMALIPINDULA WIMBE COOPERATIVE ', ' 22.00 ', ' 17.00 ', ' KASUNGU ', ' 0999233843/0994104065 ', ' GSMET IN09/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 210,800.00 ', ' 1,054,000.00 ', ' 181,609.54 ', ' 1,235,609.54 ', 10, ' 123,560.95 ', ' 1,235,609.54 ', ' 1,235,000.00 ', ' 609.54 ', 'ACTIVE'),
(95, 598, 125, ' THAWALE COOPERATIVE ', ' 78.00 ', ' 152.00 ', ' DEDZA ', ' 0990424380/0999399085 ', ' GSMETIN001/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 263,500.00 ', ' 1,317,500.00 ', ' 227,011.92 ', ' 1,544,511.92 ', 10, ' 154,451.19 ', ' 1,544,511.92 ', ' 1,422,000.00 ', ' 122,511.92 ', 'ACTIVE'),
(96, 599, 3849, ' TCHETSA COOPERATIVE ', ' 168.00 ', ' 156.00 ', ' DEDZA ', ' 0998105810/0990424165 ', ' GSMETIN002/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 31,620.00 ', ' 158,100.00 ', ' 27,241.43 ', ' 185,341.43 ', 10, ' 18,534.14 ', ' 185,341.43 ', ' 185,341.44 ', '-0.01 ', 'CLOSED'),
(97, 600, 400, ' CHUMACHILIMUNTHAKA ASSOCIATION ', ' 71.00 ', ' 29.00 ', ' NTCHEU ', ' 0995281410/0999651290 ', ' GSMETIN003/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 1,054,000.00 ', ' 5,270,000.00 ', ' 908,047.70 ', ' 6,178,047.70 ', 10, ' 617,804.77 ', ' 6,178,047.70 ', ' 6,178,048.00 ', '-0.30 ', 'CLOSED'),
(98, 601, 3138, ' MAYANKHO 8 NKUMBENI CLUB ', ' 3.00 ', ' 7.00 ', ' DEDZA ', ' 0995496135 ', ' GSMETIN004/2021 ', ' TIKOLOLE ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' 617,000.00 ', ' 804.77 ', 'ACTIVE'),
(99, 602, 9505, ' KACHERE CHAMAMA COOPERATIVE MDUNGA ', ' 39.00 ', ' 37.00 ', ' KASUNGU ', ' 0992386652/099262909 ', ' GSMEBIN 01/21 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', '', ' 1,208,400.00 ', ' 6,042,000.00 ', ' 1,041,067.21 ', ' 7,083,067.21 ', 10, ' 708,306.72 ', ' 7,083,067.21 ', ' 7,083,067.21 ', ' -   ', 'CLOSED'),
(100, 603, 9049, ' BOWE JTI CHANKHAZA CLUB ', ' 24.00 ', '', ' DOWA ', ' 0995630430/0991717935 ', ' GSMEBIN IN00/2021 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', '', ' 17,340.00 ', ' 86,700.00 ', ' 14,938.85 ', ' 101,638.85 ', 10, ' 10,163.89 ', ' 101,638.85 ', ' 101,650.00 ', '-11.15 ', 'CLOSED'),
(101, 604, 3433, ' MWALAWANYENJE COOPERATIVE ', '', '', ' KASUNGU ', ' 0997091321/0995511781 ', ' GSMEBIN IN003/2021 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', '', ' 318,000.00 ', ' 1,590,000.00 ', ' 273,965.06 ', ' 1,863,965.06 ', 10, ' 186,396.51 ', ' 1,863,965.06 ', ' -   ', ' 1,863,965.06 ', ' ACTIVE '),
(102, 605, 9339, ' MPHUNZI COOPERATIVE ', ' 13.00 ', ' 13.00 ', ' DEDZA ', ' 0881387805/0991500407 ', ' GSMEBIN IN011/2021 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', '', ' 381,480.00 ', ' 1,907,400.00 ', ' 328,654.68 ', ' 2,236,054.68 ', 10, ' 223,605.47 ', ' 2,236,054.68 ', ' -   ', ' 2,236,054.68 ', ' ACTIVE '),
(103, 606, 782, ' CHIKOKO MTUNTHAMA COOPERATIVE ', ' 12.00 ', ' 6.00 ', ' KASUNGU ', ' 0992666103/0999699175 ', ' GSMEBIN IN02/2021 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', ' 403,065.00 ', ' 1,240,200.00 ', ' 6,201,000.00 ', ' 1,068,463.71 ', ' 7,269,463.71 ', 10, ' 726,946.37 ', ' 6,542,517.34 ', ' -   ', ' 6,542,517.34 ', ' ACTIVE '),
(104, 607, 9656, ' KABUNTHU JTI CLUB ', ' 3.00 ', ' 7.00 ', ' MTCHINJI ', ' 0993464071/0997208099 ', ' GSMENUA 45 IN 50/2021 ', ' NAU 45 ', 7, '25/10/2021', '25/07/2022', '', ' 156,060.00 ', ' 780,300.00 ', ' 30,980.45 ', ' 811,280.45 ', 10, ' 81,128.05 ', ' 811,280.45 ', ' -   ', ' 811,280.45 ', ' ACTIVE '),
(105, 608, 9027, ' CHIDONGO JTI CLU ', ' 12.00 ', ' -   ', ' KASUNGU ', ' 0994487200/0995979310 ', ' GSME IN076/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 413,540.00 ', ' 2,067,700.00 ', ' 356,275.19 ', ' 2,423,975.19 ', 10, ' 242,397.52 ', ' 2,423,975.19 ', ' 1,147,000.00 ', ' 1,276,975.19 ', ' ACTIVE '),
(106, 609, 9418, ' SIYAYE LUKWA JTI CLUB ', ' 10.00 ', '', ' KASUNGU ', ' 0995723374/0999872872 ', ' GSME T IN051/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 215,760.00 ', ' 1,078,800.00 ', ' 185,882.71 ', ' 1,264,682.71 ', 10, ' 126,468.27 ', ' 1,264,682.71 ', ' -   ', ' 1,264,682.71 ', ' ACTIVE '),
(107, 610, 6418, ' MBAMBAWA SALAMBA ', ' 10.00 ', '', ' KASUNGU ', ' 0995383178/0999463863 ', ' GSMECG 9 IN /2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 89,900.00 ', ' 449,500.00 ', ' 77,451.13 ', ' 526,951.13 ', 10, ' 52,695.11 ', ' 526,951.13 ', ' 527,000.00 ', '-48.87 ', 'CLOSED'),
(108, 611, 4571, ' NSUNDWE KALOLO CENTRE ', ' 5.00 ', '', ' LILONGWE ', ' 0995338818/0992335033 ', ' GSMECG 9 IN 052/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 107,880.00 ', ' 539,400.00 ', ' 92,941.35 ', ' 632,341.35 ', 10, ' 63,234.14 ', ' 632,341.35 ', ' 447,000.00 ', ' 185,341.35 ', ' ACTIVE '),
(109, 612, 1092, ' SENDWE MAWAWA JTI CLUB ', ' 5.00 ', '', ' KASUNGU ', ' 0993271919/0999261701 ', ' GSMECG 9 IN0/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 233,740.00 ', ' 1,168,700.00 ', ' 201,372.93 ', ' 1,370,072.93 ', 10, ' 137,007.29 ', ' 1,370,072.93 ', ' 181,312.14 ', ' 1,188,760.79 ', ' ACTIVE '),
(110, 613, 9026, ' KATAWA KAMCHEDZELA CLUB ', ' 8.00 ', ' 4.00 ', ' LILONGWE ', ' 0990858559/0997237836 ', ' GSMECG 9 IN0035/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 125,860.00 ', ' 629,300.00 ', ' 108,431.00 ', ' 737,731.00 ', 10, ' 73,773.10 ', ' 737,731.00 ', ' 803,570.00 ', '-65,839.00 ', 'CLOSED'),
(111, 614, 9343, ' MTAMBALIKA COOPERATIVE ', ' 23.00 ', ' 18.00 ', ' Dowa ', ' 0995791511/0999765256 ', ' GSMECG 9 IN0055/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 359,600.00 ', ' 61,960.90 ', ' 421,560.90 ', 10, ' 42,156.09 ', ' 421,560.90 ', ' 220,000.00 ', ' 201,560.90 ', ' ACTIVE '),
(112, 615, 7955, ' MLOMBA CHISEKA JTI CLUB ', ' 13.00 ', ' 6.00 ', ' LILONGWE ', ' 0994299866/0880040456 ', ' GSMECG 9 IN019/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 305,660.00 ', ' 1,528,300.00 ', ' 263,333.83 ', ' 1,791,633.83 ', 10, ' 179,163.38 ', ' 1,791,633.83 ', ' 1,791,650.00 ', '-16.17 ', 'CLOSED'),
(113, 616, 7356, ' NKHUNGUYEMBE COOPERATIVE ', ' 84.00 ', ' 40.00 ', ' LILONGWE ', ' 0998462875/0999405267 ', ' GSMECG 9 IN026/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 1,168,700.00 ', ' 201,372.93 ', ' 1,370,072.93 ', 10, ' 137,007.29 ', ' 1,370,072.93 ', ' 1,446,150.00 ', '-76,077.07 ', 'CLOSED'),
(114, 617, 6361, ' MKANGA ZOLIRE JTI CLUB ', ' 6.00 ', '', ' LILONGWE ', ' 0999914991/0999492631 ', ' GSMECG 9 IN036/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 125,860.00 ', ' 629,300.00 ', ' 108,431.58 ', ' 737,731.58 ', 10, ' 73,773.16 ', ' 737,731.58 ', ' 635,000.00 ', ' 102,731.58 ', ' ACTIVE '),
(115, 618, 5935, ' KALANGA MPHITA MPHEREMBE ', ' 4.00 ', ' 4.00 ', ' MZIMBA ', ' 0996009016/0998456027 ', ' GSMECG 9 IN056/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 269,700.00 ', ' 1,348,500.00 ', ' 232,353.38 ', ' 1,580,853.38 ', 10, ' 158,085.34 ', ' 1,580,853.38 ', ' 1,158,000.00 ', ' 422,853.38 ', ' ACTIVE '),
(116, 619, 344, ' KAMPONDO MKOSANA NSELE CLUB ', '', ' 6.00 ', ' MZIMBA ', ' 0993325579/0888011781 ', ' GSMECG 9 IN059/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 107,880.00 ', ' 539,400.00 ', ' 92,941.35 ', ' 632,341.35 ', 10, ' 63,234.14 ', ' 632,341.35 ', ' 426,000.00 ', ' 206,341.35 ', ' ACTIVE '),
(117, 620, 2810, ' KUMUHYANYA PRE- COOPERATIVE ', ' 14.00 ', ' 24.00 ', ' MZIMBA ', ' 0888620118/0888397750 ', ' GSMECG 9 IN060/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 3,596,000.00 ', ' 619,609.02 ', ' 4,215,609.02 ', 10, ' 421,560.90 ', ' 4,215,609.02 ', ' -   ', ' 4,215,609.02 ', ' ACTIVE '),
(118, 621, 5367, ' TISANGEMO MPHEREMBE CLUB ', ' 25.00 ', ' 15.00 ', ' MZIMBA ', ' 0997513065/0992641263 ', ' GSMECG 9 IN068/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 449,500.00 ', ' 2,247,500.00 ', ' 387,255.64 ', ' 2,634,755.64 ', 10, ' 263,475.56 ', ' 2,634,755.64 ', ' 787,000.00 ', ' 1,847,755.64 ', ' ACTIVE '),
(119, 622, 3751, ' UCHINDAMI YOUTH CLUB ', ' 2.00 ', ' 1.00 ', ' MZIMBA ', ' 0993505345/0980186338 ', ' GSMECG 9 IN069/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 53,940.00 ', ' 269,700.00 ', ' 46,470.68 ', ' 316,170.68 ', 10, ' 31,617.07 ', ' 316,170.68 ', ' 101,000.00 ', ' 215,170.68 ', ' ACTIVE '),
(120, 623, 1293, ' KABWINJA JTI CLUB ', ' 14.00 ', ' 1.00 ', ' KASUNGU ', ' 0999311382/0880853710 ', ' GSMECG 9 IN069/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 287,680.00 ', ' 1,438,400.00 ', ' 247,843.61 ', ' 1,686,243.61 ', 10, ' 168,624.36 ', ' 1,686,243.61 ', ' 1,216,050.00 ', ' 470,193.61 ', ' ACTIVE '),
(121, 624, 8995, ' KABUNTHU JTI CLUB ', ' 3.00 ', ' 7.00 ', ' MTCHINJI ', ' 0993464071/0997208099 ', ' GSMECG 9 IN105/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.45 ', ' 210,780.45 ', 10, ' 21,078.05 ', ' 210,780.45 ', ' -   ', ' 210,780.45 ', ' ACTIVE '),
(122, 625, 9622, ' JAULANI KABWAFU MPHEREMBE ', ' 3.00 ', ' 7.00 ', ' MZIMBA ', ' 0998283644 ', ' GSMECG 9 IN60/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 105,400.00 ', ' 527,000.00 ', ' 90,804.77 ', ' 617,804.77 ', 10, ' 61,780.48 ', ' 617,804.77 ', ' 340,000.00 ', ' 277,804.77 ', ' ACTIVE '),
(123, 626, 5682, ' KALUNDAMAWE MTHWALO CLUB ', ' 11.00 ', ' 3.00 ', ' MZIMBA ', ' 0884216751/0991743305 ', ' GSMECG IN057/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 611,320.00 ', ' 3,056,600.00 ', ' 526,667.66 ', ' 3,583,267.66 ', 10, ' 358,326.77 ', ' 3,583,267.66 ', ' -   ', ' 3,583,267.66 ', ' ACTIVE '),
(124, 627, 7043, ' KAPANDO YOUTH CLUB ', ' 16.00 ', ' 13.00 ', ' MZIMBA ', ' 0990414285/0996305085 ', ' GSMECG IN061/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 359,600.00 ', ' 1,798,000.00 ', ' 309,804.51 ', ' 2,107,804.51 ', 10, ' 210,780.45 ', ' 2,107,804.51 ', ' 224,000.00 ', ' 1,883,804.51 ', ' ACTIVE '),
(125, 628, 8486, ' TIKONDANE THOMAS FIOSI JTI ', ' 8.00 ', ' 3.00 ', ' MCHINJI ', ' 0999235849/0996577359 ', ' GSMECG9  IN074/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 222,801.34 ', ' 1,114,006.70 ', ' 191,948.99 ', ' 1,305,955.69 ', 10, ' 130,595.57 ', ' 1,305,955.69 ', ' 556,168.00 ', ' 749,787.69 ', ' ACTIVE '),
(126, 629, 413, ' MADZIDZI JTI CLUB MNDEWE ', ' 29.00 ', ' 2.00 ', ' KASUNGU ', ' 0999304206/0999009988 ', ' GSMECG9 IN 024/21 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 251,720.00 ', ' 1,258,600.00 ', ' 216,863.16 ', ' 1,475,463.16 ', 10, ' 147,546.32 ', ' 1,475,463.16 ', ' 1,427,470.00 ', ' 47,993.16 ', ' ACTIVE '),
(127, 630, 6542, ' KAKUDA CHIMGONDA ', ' 9.00 ', ' 2.00 ', ' MCHINJI ', ' 0999113684/0993226973 ', ' GSMECG9 IN 044/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.45 ', ' 210,780.45 ', 10, ' 21,078.05 ', ' 210,780.45 ', ' 210,780.45 ', ' -   ', 'CLOSED'),
(128, 631, 8958, ' DAMA JTI CLUB ', ' 5.00 ', ' -   ', ' LILONGWE ', ' 0995374809/0998275141 ', ' GSMECG9 IN 046/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.00 ', ' 210,780.00 ', 10, ' 21,078.00 ', ' 210,780.00 ', ' 111,700.00 ', ' 99,080.00 ', ' ACTIVE '),
(129, 632, 4805, ' KAMPHURU JTI KALIMBAKATHA CLUB  ', ' 9.00 ', ' 1.00 ', ' KASUNGU ', ' 0999393563/0999474867 ', ' GSMECG9 IN 049/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 161,820.00 ', ' 809,100.00 ', ' 139,412.03 ', ' 948,512.03 ', 10, ' 94,851.20 ', ' 948,512.03 ', ' 948,515.00 ', '-2.97 ', 'CLOSED'),
(130, 633, 5820, ' KABWAZI JTI CHILIKUMWENDO ZONE ', ' 11.00 ', '', ' DEDZA ', ' 0993101573/0999129690 ', ' GSMECG9 IN 049/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 215,760.00 ', ' 1,078,800.00 ', ' 224,910.37 ', ' 1,303,710.37 ', 10, ' 130,371.04 ', ' 1,303,710.37 ', ' 111,300.00 ', ' 1,192,410.37 ', ' ACTIVE '),
(131, 634, 6944, ' CHISOMO CHAWANTHA CLUB ', ' 5.00 ', '', ' LILONGWE ', ' 0999645420/0999296506 ', ' GSMECG9 IN 051/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 17,980.00 ', ' 89,900.00 ', ' 15,490.00 ', ' 105,390.00 ', 10, ' 10,539.00 ', ' 105,390.00 ', ' 50,000.00 ', ' 55,390.00 ', ' ACTIVE '),
(132, 635, 7996, ' MCHENGA JTI MPHONDE ', ' 10.00 ', ' 1.00 ', ' DEDZA ', ' 0993748233/0991556029 ', ' GSMECG9 IN 06/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', ' 23,374.00 ', ' 71,920.00 ', ' 359,600.00 ', ' 61,960.90 ', ' 421,560.90 ', 10, ' 42,156.09 ', ' 434,570.12 ', ' 434,570.12 ', ' -   ', 'CLOSED'),
(133, 636, 2225, ' LIFIDZI JTI KACHERE ', ' 11.00 ', ' 11.00 ', ' DEDZA ', ' 0993101573/0999129690 ', ' GSMECG9 IN 07/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 89,900.00 ', ' 449,500.00 ', ' 93,712.65 ', ' 543,212.65 ', 10, ' 54,321.27 ', ' 543,212.65 ', ' 576,695.45 ', '-33,482.80 ', 'CLOSED'),
(134, 637, 4922, ' SUNDWE JTI ', ' 14.00 ', ' 3.00 ', ' MCHINJI ', ' 0998877288/0999660510 ', ' GSMECG9 IN0/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 31,620.00 ', ' 158,100.00 ', ' 27,241.43 ', ' 185,341.43 ', 10, ' 18,534.14 ', ' 185,341.43 ', ' 90,000.00 ', ' 95,341.43 ', ' ACTIVE '),
(135, 638, 7452, ' CHIMANGENI JTI MTIMBULA CLUB ', ' 15.00 ', ' 1.00 ', ' KASUNGU ', ' 0999837992/0999474713 ', ' GSMECG9 IN0106/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 107,880.00 ', ' 539,400.00 ', ' 92,941.35 ', ' 632,341.35 ', 10, ' 63,234.14 ', ' 632,341.35 ', ' 632,341.35 ', ' -   ', 'CLOSED'),
(136, 639, 9205, ' TSOGOLO CHAPUMA COOPERATIVE ', ' 10.00 ', ' 24.00 ', ' DOWA ', ' **********/********** ', ' GSMECG9 IN014/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 323,640.00 ', ' 1,618,200.00 ', ' 278,824.06 ', ' 1,897,024.06 ', 10, ' 189,702.41 ', ' 1,897,024.06 ', ' 1,539,562.37 ', ' 357,461.69 ', ' ACTIVE '),
(137, 640, 9237, ' KAPYERA LUKWA CLUB ', ' 3.00 ', ' 3.00 ', ' KASUNGU ', ' 0992222847/0996809188 ', ' GSMECG9 IN016/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 863,040.00 ', ' 4,315,200.00 ', ' 743,530.82 ', ' 5,058,730.82 ', 10, ' 505,873.08 ', ' 5,058,730.82 ', ' -   ', ' 5,058,730.82 ', ' ACTIVE '),
(138, 641, 3127, ' CHAMALAZA NDAYA CLUB ', ' 9.00 ', ' 11.00 ', ' KASUNGU ', ' 0991663400 ', ' GSMECG9 IN017/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 53,940.00 ', ' 269,700.00 ', ' 46,470.68 ', ' 316,170.68 ', 10, ' 31,617.07 ', ' 316,170.68 ', ' -   ', ' 316,170.68 ', ' ACTIVE '),
(139, 642, 4135, ' MASOMPHENYA COOPERATIVE  ', ' 34.00 ', ' 36.00 ', ' DEDZA ', ' 0994889163/0994083140 ', ' GSMECG9 IN02/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 215,760.00 ', ' 1,078,800.00 ', ' 224,910.37 ', ' 1,303,710.37 ', 10, ' 130,371.04 ', ' 1,303,710.37 ', ' 1,303,710.37 ', ' -   ', 'CLOSED'),
(140, 643, 480, ' KAPAKO NKHAMBA CHISEKA CLUB ', ' 14.00 ', ' 8.00 ', ' LILONGWE ', ' 0883578949/0995519189 ', ' GSMECG9 IN020/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 449,500.00 ', ' 2,247,500.00 ', ' 387,255.64 ', ' 2,634,755.64 ', 10, ' 263,475.56 ', ' 2,634,755.64 ', ' 884,000.00 ', ' 1,750,755.64 ', ' ACTIVE '),
(141, 644, 782, ' CHIWOLA CHIWOKO COOPERATIVE ', ' 76.00 ', ' 66.00 ', ' Lilongwe ', ' 0996672632/0996035499 ', ' GSMECG9 IN021/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 251,720.00 ', ' 1,258,600.00 ', ' 216,863.16 ', ' 1,475,463.16 ', 10, ' 147,546.32 ', ' 1,475,463.16 ', ' 1,442,300.00 ', ' 33,163.16 ', ' ACTIVE '),
(142, 645, 6335, ' TAYAMBA GWILIZE JTI CLUB ', ' 19.00 ', '', ' LILONGWE ', ' 0998829671/0881723269 ', ' GSMECG9 IN022/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 341,620.00 ', ' 1,708,100.00 ', ' 294,314.28 ', ' 2,002,414.28 ', 10, ' 200,241.43 ', ' 2,002,414.28 ', ' 1,211,000.00 ', ' 791,414.28 ', ' ACTIVE '),
(143, 646, 3153, ' BOWE JTI CHANKHAZA CLUB ', ' 24.00 ', '', ' DOWA ', ' 0995630430/0991717935 ', ' GSMECG9 IN023/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 305,660.00 ', ' 1,528,300.00 ', ' 263,333.83 ', ' 1,791,633.83 ', 10, ' 179,163.38 ', ' 1,791,633.83 ', ' 1,515,230.00 ', ' 276,403.83 ', ' ACTIVE '),
(144, 647, 7156, ' CHIKANDE JTI M\'NKOMA CLUB ', ' 11.00 ', ' 3.00 ', ' KASUNGU ', ' 0998330302/0999420801 ', ' GSMECG9 IN030/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 318,004.63 ', ' 1,590,023.17 ', ' 273,969.05 ', ' 1,863,992.22 ', 10, ' 186,399.22 ', ' 1,863,992.22 ', ' 281,480.00 ', ' 1,582,512.22 ', ' ACTIVE '),
(145, 648, 149, ' MWAVU JTI CLUB  ', ' 9.00 ', ' 3.00 ', ' DOWA ', ' 0999635223 ', ' GSMECG9 IN031/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 197,780.00 ', ' 988,900.00 ', ' 170,392.48 ', ' 1,159,292.48 ', 10, ' 115,929.25 ', ' 1,159,292.48 ', ' 772,000.00 ', ' 387,292.48 ', ' ACTIVE '),
(146, 649, 5411, ' CHIFUKULA ELISA CHAKHEZA JTI CLUB ', ' 18.00 ', ' 3.00 ', ' DOWA ', ' 0994042993/0991114949 ', ' GSMECG9 IN032/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 197,780.00 ', ' 988,900.00 ', ' 170,392.48 ', ' 1,159,292.48 ', 10, ' 115,929.25 ', ' 1,159,292.48 ', ' 284,250.00 ', ' 875,042.48 ', ' ACTIVE '),
(147, 650, 7815, ' KATONDO JTI SITAMBO ', ' 12.00 ', '', ' LILONGWE ', ' 0999782242/0995337428 ', ' GSMECG9 IN033/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 179,980.00 ', ' 899,900.00 ', ' 154,902.25 ', ' 1,054,802.25 ', 10, ' 105,480.23 ', ' 1,054,802.25 ', ' 1,112,680.00 ', '-57,877.75 ', 'CLOSED'),
(148, 651, 3558, ' MTAMBALIKA JTI CLUB ', ' 7.00 ', '', ' DOWA ', ' 0991254885/0999705648 ', ' GSMECG9 IN037/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 125,860.00 ', ' 629,300.00 ', ' 108,431.58 ', ' 737,731.58 ', 10, ' 73,773.16 ', ' 737,731.58 ', ' 240,500.00 ', ' 497,231.58 ', ' ACTIVE '),
(149, 652, 9890, ' CHISEPO JTI KAYEMBE CLUB ', ' 33.00 ', ' 2.00 ', ' DOWA ', ' 0995252060/0881109333 ', ' GSMECG9 IN039/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 71,920.00 ', ' 359,600.00 ', ' 61,960.00 ', ' 421,560.00 ', 10, ' 42,156.00 ', ' 421,560.00 ', ' 421,600.00 ', '-40.00 ', 'CLOSED'),
(150, 653, 3232, ' KALINDE MDUWA JTI ', ' 12.00 ', ' 4.00 ', ' MCHINJI ', ' 0999497041/0999066515 ', ' GSMECG9 IN041/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 147,560.00 ', ' 737,800.00 ', ' 127,126.68 ', ' 864,926.68 ', 10, ' 86,492.67 ', ' 864,926.68 ', ' 80,000.00 ', ' 784,926.68 ', ' ACTIVE '),
(151, 654, 2001, ' CHIGODI SANTHE JTI CLUB ', ' 5.00 ', ' 2.00 ', ' KASUNGU ', ' 0998720106/0995383837 ', ' GSMECG9 IN043/21 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.45 ', ' 210,780.45 ', 10, ' 21,078.05 ', ' 210,780.45 ', ' -   ', ' 210,780.45 ', ' ACTIVE '),
(152, 655, 6089, ' Kasiya Cooperative ', ' 20.00 ', ' 26.00 ', ' Lilongwe ', ' 0999197951/0995781147 ', ' GSMECG9 IN050/21 ', ' CG9 ', 7, '25/10/2020', '25/07/2021', '', ' 1,078,800.00 ', ' 5,394,000.00 ', ' 929,413.53 ', ' 6,323,413.53 ', 10, ' 632,341.35 ', ' 6,323,413.53 ', ' 6,323,413.80 ', '-0.27 ', 'CLOSED'),
(153, 656, 2300, ' ENGUCWINI MTWALO COOPERATIVE ', ' 99.00 ', ' 252.00 ', ' MZIMBA ', ' 0994630668/0884071966 ', ' GSMECG9 IN054/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 197,780.00 ', ' 988,900.00 ', ' 170,392.48 ', ' 1,159,292.48 ', 10, ' 115,929.25 ', ' 1,159,292.48 ', ' 1,152,000.00 ', ' 7,292.48 ', 'CLOSED'),
(154, 657, 3642, ' JANDALALA BUMBA COOPERATIVE ', ' 13.00 ', ' 11.00 ', ' MZIMBA ', ' 0882824806 ', ' GSMECG9 IN055/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 611,320.00 ', ' 3,056,600.00 ', ' 526,667.66 ', ' 3,583,267.66 ', 10, ' 358,326.77 ', ' 3,583,267.66 ', ' 1,178,100.00 ', ' 2,405,167.66 ', ' ACTIVE '),
(155, 658, 5306, ' KAMUHYANYA GREA JERE FARMERS CLUB ', ' 14.00 ', ' 24.00 ', ' MZIMBA ', ' 0888620118/0888397750 ', ' GSMECG9 IN060/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 719,200.00 ', ' 3,596,000.00 ', ' 619,609.02 ', ' 4,215,609.02 ', 10, ' 421,560.90 ', ' 4,215,609.02 ', ' 1,420,000.00 ', ' 2,795,609.02 ', ' ACTIVE '),
(156, 659, 9923, ' KAULIMI CHILONGOZI COOPARETIVE ', ' 1.00 ', ' 16.00 ', ' RUMPHI ', ' 0886767723/0882526906 ', ' GSMECG9 IN062/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 251,720.00 ', ' 1,258,600.00 ', ' 216,863.16 ', ' 1,475,463.16 ', 10, ' 147,546.32 ', ' 1,475,463.16 ', ' 1,025,200.00 ', ' 450,263.16 ', ' ACTIVE '),
(157, 660, 9430, ' KAZUNI YOUTH BONGOWONGO CHIVAMBO CLUB ', ' 14.00 ', ' 19.00 ', ' MZIMBA ', ' 0881357019/0992186084 ', ' GSMECG9 IN063/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 485,460.00 ', ' 2,427,300.00 ', ' 418,236.09 ', ' 2,845,536.09 ', 10, ' 284,553.61 ', ' 2,845,536.09 ', ' 1,203,600.00 ', ' 1,641,936.09 ', ' ACTIVE '),
(158, 661, 6196, ' MATUNKHA MKONONGO CLUB ', '', '', ' RUMPHI ', ' 0888213305/0990861742 ', ' GSMECG9 IN065/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 323,640.00 ', ' 1,618,200.00 ', ' 278,824.06 ', ' 1,897,024.06 ', 10, ' 189,702.41 ', ' 1,897,024.06 ', ' 993,270.00 ', ' 903,754.06 ', ' ACTIVE '),
(159, 662, 8376, ' MWAZISI GALANG\'ANDA COOPERATION ', ' 25.00 ', ' 24.00 ', ' RUMPHI ', ' 0996595500/0888690877 ', ' GSMECG9 IN067/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 179,800.00 ', ' 899,000.00 ', ' 154,902.25 ', ' 1,053,902.25 ', 10, ' 105,390.23 ', ' 1,053,902.25 ', ' 428,050.00 ', ' 625,852.25 ', ' ACTIVE ');
INSERT INTO `inputloansprocessed` (`id`, `group_id`, `group_code`, `CLIENTNAME`, `Male`, `Female`, `LOCATION`, `PHONENUMBER`, `LOANNO`, `LOANPRODUCT`, `loan_product_id`, `disbursed_date`, `TO`, ` UPFRONTFEES`, ` COLLATERAL`, `LAMOUNT`, ` INTEREST`, ` LiNTEREST`, `loan_period`, `MREPAYMENT`, ` TOTALLOAN`, `AMOUNTPAID`, ` LOAN BALANCE`, `STATUS`) VALUES
(160, 663, 9780, ' CHIPONOSKO MWAYESU ', ' 4.00 ', ' 5.00 ', ' MZIMBA ', ' 0888145857 ', ' GSMECG9 IN069/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 53,940.00 ', ' 269,700.00 ', ' 46,470.68 ', ' 316,170.68 ', 10, ' 31,617.07 ', ' 316,170.68 ', ' 204,300.00 ', ' 111,870.68 ', ' ACTIVE '),
(161, 664, 4924, ' CHANJOKA MANGOMBERA COOPERATIVE ', ' 17.00 ', ' 43.00 ', ' MZIMBA ', ' 0881075599/0882862445 ', ' GSMECG9 IN070/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 1,078,800.00 ', ' 5,394,000.00 ', ' 929,413.53 ', ' 6,323,413.53 ', 10, ' 632,341.35 ', ' 6,323,413.53 ', ' 1,376,000.00 ', ' 4,947,413.53 ', ' ACTIVE '),
(162, 665, 5405, ' BEKHAMU MWAZISI COOPERATIVE ', '', '', ' RUMPHI ', ' 0996596519/09916658266 ', ' GSMECG9 IN071/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 269,700.00 ', ' 1,348,500.00 ', ' 232,353.38 ', ' 1,580,853.38 ', 10, ' 158,085.34 ', ' 1,580,853.38 ', ' 1,000,000.00 ', ' 580,853.38 ', ' ACTIVE '),
(163, 666, 8922, ' MWANGALA ACTION GROUP ', '', ' 8.00 ', ' DOWA ', ' 0885505276/0990194516 ', ' GSMECG9 IN073/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.45 ', ' 210,780.45 ', 10, ' 21,078.05 ', ' 210,780.45 ', ' -   ', ' 210,780.45 ', ' ACTIVE '),
(164, 667, 7455, ' NAMANDA CHIWERE COOPERATIVE ', ' 62.00 ', ' 110.00 ', ' DOWA ', ' 0993282057/0983422399 ', ' GSMECG9 IN073/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 53,940.00 ', ' 269,700.00 ', ' 46,470.68 ', ' 316,170.68 ', 10, ' 31,617.07 ', ' 316,170.68 ', ' 112,240.60 ', ' 203,930.08 ', ' ACTIVE '),
(165, 668, 2362, ' TOVWIRANE KALERA CLUB ', ' 13.00 ', ' 3.00 ', ' RUMPHI ', ' 0888029778/0888200393 ', ' GSMECG9 IN073/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 151,512.50 ', ' 757,562.50 ', ' 130,531.86 ', ' 888,094.36 ', 10, ' 88,809.44 ', ' 888,094.36 ', ' 888,500.00 ', '-405.64 ', 'CLOSED'),
(166, 669, 8273, ' TIKONDANE NTHONDO JTI ', ' 10.00 ', '', ' LILONGWE ', ' 0999398099/0995902288 ', ' GSMECG9 IN075/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 197,780.00 ', ' 988,900.00 ', ' 170,392.48 ', ' 1,159,292.48 ', 10, ' 115,929.25 ', ' 1,159,292.48 ', ' 960,620.00 ', ' 198,672.48 ', ' ACTIVE '),
(167, 670, 2677, ' KAPULULUKA ZONI KANYUMBU JTI ', ' 17.00 ', '', ' LILONGWE ', ' 0999838126/0882009790 ', ' GSMECG9 IN077/21 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 342,067.55 ', ' 1,710,337.74 ', ' 294,699.86 ', ' 2,005,037.60 ', 10, ' 200,503.76 ', ' 2,005,037.60 ', ' 800,000.00 ', ' 1,205,037.60 ', ' ACTIVE '),
(168, 671, 9370, ' KADWALA JTI CLUB ', ' 5.00 ', ' 5.00 ', ' KASUNGU ', ' 0995155312/0995187947 ', ' GSMECG9 IN078/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 179,800.00 ', ' 899,000.00 ', ' 154,902.25 ', ' 1,053,902.25 ', 10, ' 105,390.23 ', ' 1,053,902.25 ', ' -   ', ' 1,053,902.25 ', ' ACTIVE '),
(169, 672, 3671, ' KANYERERE MBWATALIKA JTI CLUB ', ' 21.00 ', '', ' LILONGWE ', ' 0998337842/ ', ' GSMECG9 IN082/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 143,840.00 ', ' 719,200.00 ', ' 123,921.80 ', ' 843,121.80 ', 10, ' 84,312.18 ', ' 843,121.80 ', ' 644,880.00 ', ' 198,241.80 ', ' ACTIVE '),
(170, 673, 6880, ' KAWATA SANTHE JTI CLUB ', ' 12.00 ', '', ' KASUNGU ', ' 0996766249/0993953945 ', ' GSMECG9 IN083/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 179,800.00 ', ' 899,000.00 ', ' 154,902.25 ', ' 1,053,902.25 ', 10, ' 105,390.23 ', ' 1,053,902.25 ', ' 109,250.00 ', ' 944,652.25 ', ' ACTIVE '),
(171, 674, 246, ' MWALAWANYENJE MALANGANO COOPERATIVE ', '', '', ' KASUNGU ', ' 0997091321/0995511781 ', ' GSMECG9 IN09/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 2,157,600.00 ', ' 10,788,000.00 ', ' 1,858,827.05 ', ' 12,646,827.05 ', 10, ' 1,264,682.71 ', ' 12,646,827.05 ', ' 1,130,000.00 ', ' 11,516,827.05 ', ' ACTIVE '),
(172, 675, 193, ' KHAMALIPINDULA WIMBE COOPERATIVE ', ' 22.00 ', ' 17.00 ', ' KASUNGU ', ' 0999233843/0994104065 ', ' GSMECG9 IN10/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 359,600.00 ', ' 1,798,000.00 ', ' 309,804.51 ', ' 2,107,804.51 ', 10, ' 210,780.45 ', ' 2,107,804.51 ', ' 850,000.00 ', ' 1,257,804.51 ', ' ACTIVE '),
(173, 676, 8765, ' SANGA COOPERATIVE ', ' 40.00 ', ' 20.00 ', ' DOWA ', ' 0991410428/0996101503 ', ' GSMECG9 IN13/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', ' 23,374.00 ', ' 71,920.00 ', ' 359,600.00 ', ' 61,960.90 ', ' 421,560.90 ', 10, ' 42,156.09 ', ' 421,560.90 ', ' 447,512.37 ', '-25,951.47 ', 'CLOSED'),
(174, 677, 9890, ' KABWAZI JTI ZONE ', ' 9.00 ', ' 2.00 ', ' Lilongwe ', ' 0996836155/0994324331 ', ' GSMECG9IN001/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 215,760.00 ', ' 1,078,800.00 ', ' 185,882.71 ', ' 1,264,682.71 ', 10, ' 126,468.27 ', ' 1,264,682.71 ', ' 1,300,850.00 ', '-36,167.29 ', 'CLOSED'),
(175, 678, 9697, ' TCHETSA COOPERATIVE ', ' 168.00 ', ' 156.00 ', ' DEDZA ', ' 0998105810/0990424165 ', ' GSMECG9IN003/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 2,013,760.00 ', ' 10,068,800.00 ', ' 1,734,905.25 ', ' 11,803,705.25 ', 10, ' 1,180,370.52 ', ' 11,803,705.25 ', ' 10,760,800.00 ', ' 1,042,905.25 ', ' ACTIVE '),
(176, 679, 693, ' CHUMACHILIMUNTHAKA ASSOCIATION ', ' 71.00 ', ' 29.00 ', ' NTCHEU ', ' 0995281410/0999651290 ', ' GSMECG9IN004/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 1,078,800.00 ', ' 5,394,000.00 ', ' 929,413.53 ', ' 6,323,413.53 ', 10, ' 632,341.35 ', ' 6,323,413.53 ', ' 3,275,302.00 ', ' 3,048,111.53 ', ' ACTIVE '),
(177, 680, 3628, ' CHIYEMBEKEZO SABWERA CLUB ', '', '', '', '', ' GSMECG9IN005/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 179,800.00 ', ' 899,000.00 ', ' 154,902.25 ', ' 1,053,902.25 ', 10, ' 105,390.23 ', ' 1,053,902.25 ', ' 1,080,850.00 ', '-26,947.75 ', 'CLOSED'),
(178, 681, 9940, ' KHAMALATHU COOPERATIVE ', '', '', '', '', ' GSMECG9IN008/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 467,480.00 ', ' 2,337,400.00 ', ' 487,305.80 ', ' 2,824,705.80 ', 10, ' 282,470.58 ', ' 2,740,145.86 ', ' 2,740,145.86 ', ' -   ', 'CLOSED'),
(179, 682, 5141, ' SIMULEMBA COOPERATIVE ', '', '', '', '', ' GSMECG9IN018/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 269,700.00 ', ' 46,470.68 ', ' 316,170.68 ', 10, ' 31,617.07 ', ' 316,170.68 ', ' 316,171.00 ', '-0.32 ', 'CLOSED'),
(180, 683, 700, ' KWACHA VWANISAMIRE ', ' 5.00 ', ' 6.00 ', ' MZIMBA ', ' 0999835811/0995849978 ', ' GSMECG9IN064/21 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 988,900.00 ', ' 170,392.48 ', ' 1,159,292.48 ', 10, ' 115,929.25 ', ' 1,159,292.48 ', ' 610,000.00 ', ' 549,292.48 ', ' ACTIVE '),
(181, 684, 2931, ' MOZERA KATAWA CLUB ', '', ' 15.00 ', ' MZIMBA ', ' 0883942826/0883941749 ', ' GSMELL IN066/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 269,700.00 ', ' 1,348,500.00 ', ' 232,353.38 ', ' 1,580,853.38 ', 10, ' 158,085.34 ', ' 1,580,853.38 ', ' 40,700.00 ', ' 1,540,153.38 ', ' ACTIVE '),
(182, 685, 2113, ' SANTHE JTI CLUB ', ' 5.00 ', ' 2.00 ', ' KASUNGU ', ' 0991626178/0990719875 ', ' GSMES IN042/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 35,960.00 ', ' 179,800.00 ', ' 30,980.45 ', ' 210,780.45 ', 10, ' 21,078.05 ', ' 210,780.45 ', ' 210,800.00 ', '-19.55 ', 'CLOSED'),
(183, 686, 6558, ' LEMWE JTI KALOLO CLUB ', ' 11.00 ', ' 1.00 ', ' Lilongwe ', ' 0885595947/0995732968 ', ' GSMES IN08/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 215,760.00 ', ' 1,078,800.00 ', ' 185,882.71 ', ' 1,264,682.71 ', 10, ' 126,468.27 ', ' 1,264,682.71 ', ' 848,140.00 ', ' 416,542.71 ', ' ACTIVE '),
(184, 687, 2806, ' MOZI MDUWA JTI CLUB ', ' 9.00 ', '', ' MCHINJI ', ' 0887003210/0997150359 ', ' GSMES IN115/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', '', ' 539,400.00 ', ' 92,941.35 ', ' 632,341.35 ', 10, ' 63,234.14 ', ' 632,341.35 ', ' 444,000.00 ', ' 188,341.35 ', ' ACTIVE '),
(185, 688, 7161, ' CHITU JTI MCHELEKA CLUB ', ' 24.00 ', ' 2.00 ', ' KASUNGU ', ' 0997695232 ', ' GSMESCG9 IN025/21 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 251,720.00 ', ' 1,258,600.00 ', ' 216,863.16 ', ' 1,475,463.16 ', 10, ' 147,546.32 ', ' 1,475,463.16 ', ' 1,077,720.00 ', ' 397,743.16 ', ' ACTIVE '),
(186, 689, 9711, ' KATEMA COOPERATIVE ', ' 20.00 ', ' 26.00 ', ' KASUNGU ', ' 0999729234/0992821899 ', ' GSMET IN011/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', ' 87,652.50 ', ' 269,700.00 ', ' 1,348,500.00 ', ' 232,353.38 ', ' 1,580,853.38 ', 10, ' 158,085.34 ', ' 1,580,853.38 ', ' -   ', ' 1,580,853.38 ', ' ACTIVE '),
(187, 690, 9071, ' KAMPINI JTI MTEMBE CHISEKA CLUB ', ' 20.00 ', ' 2.00 ', ' LILONGWE ', ' 0991553817/0996348839 ', ' GSMET IN053/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 431,520.00 ', ' 2,157,600.00 ', ' 371,765.41 ', ' 2,529,365.41 ', 10, ' 252,936.54 ', ' 2,529,365.41 ', ' 2,531,400.00 ', '-2,034.59 ', 'CLOSED'),
(188, 691, 6252, ' NAMBUMA JTI ZONE ', ' 24.00 ', '', ' DOWA ', ' 0993955440/0992125743 ', ' GSMET IN38/2021 ', ' CG9 ', 7, '25/10/2021', '25/07/2022', '', ' 71,920.00 ', ' 359,600.00 ', ' 61,960.00 ', ' 421,560.00 ', 10, ' 42,156.00 ', ' 421,560.00 ', ' 408,000.00 ', ' 13,560.00 ', ' ACTIVE '),
(189, 692, 8347, ' NSUNDWE KALOLO CENTRE ', ' 5.00 ', '', ' LILONGWE ', ' 0995338818/0992335033 ', ' GSMECG 7 IN 001/2021 ', ' CG7 ', 7, '25/10/2021', '25/07/2022', '', '', ' 145,400.00 ', ' 25,053.16 ', ' 170,453.16 ', 10, ' 17,045.32 ', ' 170,453.16 ', ' 179,000.00 ', '-8,546.84 ', 'CLOSED'),
(190, 693, 9096, ' Tadala 4 Club Nsambila ', ' 10.00 ', ' 7.00 ', ' Lilongwe ', ' 0992022983/0990337590 ', ' GSMECG 7 IN033/2021 ', ' CG7 ', 7, '25/10/2021', '25/07/2022', '', ' -   ', '', '', ' 170,453.16 ', 10, ' 17,045.32 ', ' 170,453.16 ', ' 167,000.00 ', ' 3,453.16 ', ' ACTIVE '),
(191, 694, 8425, ' THAWALE COOPERATIVE ', ' 78.00 ', ' 152.00 ', ' DEDZA ', ' 0990424380/0999399085 ', ' GSMECG7 IN04/2021 ', ' CG7 ', 7, '25/10/2021', '25/07/2022', '', ' 450,740.00 ', ' 2,253,700.00 ', ' 388,323.93 ', ' 2,642,023.93 ', 10, ' 264,202.39 ', ' 2,642,023.93 ', ' 858,450.00 ', ' 1,783,573.93 ', ' ACTIVE '),
(192, 695, 4204, ' TAMBALA COOPERATIVE  ', ' 9.00 ', ' 11.00 ', ' DEDZA ', ' 0995058276/0991676138 ', ' GSMECG7 IN05/2021 ', ' CG7 ', 7, '25/10/2021', '25/07/2022', '', ' 334,420.00 ', ' 1,672,100.00 ', ' 288,111.30 ', ' 1,960,211.30 ', 10, ' 196,021.13 ', ' 1,960,211.30 ', ' 1,276,000.00 ', ' 684,211.30 ', ' ACTIVE '),
(193, 696, 1190, ' TSOGOLO CHAPUMA COOPERATIVE ', ' 10.00 ', ' 24.00 ', ' DOWA ', ' **********/********** ', ' GSMECG7 IN06/2021 ', ' CG7 ', 7, '25/10/2021', '25/07/2022', '', ' 392,580.00 ', ' 1,962,900.00 ', ' 338,217.61 ', ' 2,301,117.61 ', 10, ' 230,111.76 ', ' 2,301,117.61 ', ' 284,000.00 ', ' 2,017,117.61 ', ' ACTIVE ');

-- --------------------------------------------------------

--
-- Table structure for table `internal_accounts`
--

CREATE TABLE `internal_accounts` (
  `internal_account_id` int(11) NOT NULL,
  `account_name` varchar(200) NOT NULL,
  `account_desc` text DEFAULT NULL,
  `is_cash_account` enum('Yes','No') NOT NULL,
  `adde_by` varchar(200) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `internal_accounts`
--

INSERT INTO `internal_accounts` (`internal_account_id`, `account_name`, `account_desc`, `is_cash_account`, `adde_by`, `date_created`) VALUES
(3, 'Teller1', 'This is Cashier account 1', 'No', '7', '2022-03-31 12:17:51'),
(4, 'Teller 2', 'Teller accounts', 'Yes', '7', '2022-03-31 12:47:39'),
(5, 'teller 1', 'teller 1', 'Yes', '7', '2022-04-01 12:48:35'),
(6, 'income account', 'Income account', 'No', '7', '2022-04-08 16:00:59'),
(7, 'income account', 'Income account', 'No', '7', '2022-04-08 16:01:49'),
(8, 'income account', 'ghghgh', 'No', '7', '2022-04-08 16:02:36'),
(10, 'Collection Acccount', 'This is a collection account to track all loan repayments ', 'No', '7', '2022-05-24 17:24:36'),
(11, 'Collection Acccount', 'This is a collection account to track all loan repayments ', 'No', '7', '2022-05-24 17:25:50'),
(12, 'Damison Msonthi cashier account', '', 'No', '7', '2022-08-23 08:26:16'),
(13, 'Damison Msonthi cashier account', 'Mchinji', 'No', '7', '2022-08-23 08:29:53'),
(14, 'Collection Acccount', 'Collection', 'No', '7', '2022-11-08 12:16:44'),
(19, 'tumi', '', 'Yes', '45', '2023-01-31 09:15:29'),
(20, 'Tumisang', '', 'Yes', '45', '2023-01-31 09:23:11'),
(22, 'Collection account', 'This account is just for collecting funds from loans , penalties etc triggerd by customer account or loan accounts', 'No', '7', '2023-04-30 10:56:57'),
(24, 'Lloyd Collection account', 'This account is just for collecting funds from loans , penalties etc triggerd by customer account or loan accounts', 'No', '7', '2023-05-19 00:24:56'),
(25, 'Lloyd Teller account', 'This is teller account', 'Yes', '7', '2023-05-19 00:56:19'),
(28, 'Tumisang Teller', '', 'No', '65', '2023-09-01 03:43:55'),
(30, 'Wilton Account', 'This wilton account to be used to make deposit ', 'No', '7', '2023-09-12 01:02:12'),
(31, 'Cashier Account', 'this is cash', 'Yes', '72', '2025-05-05 15:37:46');

-- --------------------------------------------------------

--
-- Table structure for table `loan`
--

CREATE TABLE `loan` (
  `loan_id` int(11) NOT NULL,
  `loan_number` varchar(200) NOT NULL,
  `loan_product` int(11) NOT NULL,
  `loan_customer` int(11) NOT NULL,
  `customer_type` enum('individual','group','institution') NOT NULL,
  `loan_date` date NOT NULL,
  `loan_principal` decimal(18,2) NOT NULL,
  `loan_period` int(11) NOT NULL,
  `period_type` varchar(200) NOT NULL,
  `loan_interest` double NOT NULL,
  `loan_interest_amount` decimal(18,2) NOT NULL,
  `loan_amount_term` decimal(18,2) NOT NULL,
  `loan_amount_total` decimal(18,2) NOT NULL,
  `next_payment_id` int(11) DEFAULT NULL,
  `next_payment_id_rescheduled` int(11) NOT NULL DEFAULT 1,
  `worthness_file` varchar(200) DEFAULT NULL,
  `narration` text DEFAULT NULL,
  `loan_added_by` int(11) NOT NULL,
  `loan_approved_by` int(11) DEFAULT NULL,
  `approved_date` datetime DEFAULT NULL,
  `rejected_by` varchar(200) DEFAULT NULL,
  `rejection_reasons` text NOT NULL,
  `rejected_date` datetime DEFAULT NULL,
  `counter` int(11) NOT NULL,
  `loannumber` varchar(200) DEFAULT NULL,
  `loan_status` enum('INITIATED','APPROVED','WRITTEN_OFF','DEFAULTED','DELETED','ACTIVE','CLOSED','REJECTED','RECOMMENDED','APPROVED_FIRST','APPROVED_SECOND') NOT NULL DEFAULT 'INITIATED',
  `loan_recommended_by` varchar(200) NOT NULL,
  `loan_recommended_date` datetime DEFAULT NULL,
  `recommend_reasons` text NOT NULL,
  `disbursed_amount` decimal(18,2) NOT NULL,
  `disbursed` enum('Yes','No') NOT NULL DEFAULT 'No',
  `disbursed_by` varchar(200) NOT NULL,
  `disbursed_date` datetime NOT NULL DEFAULT current_timestamp(),
  `written_off_by` varchar(200) DEFAULT NULL,
  `write_off_approved_by` varchar(200) DEFAULT NULL,
  `write_off_approval_date` date DEFAULT NULL,
  `written_off_date` date DEFAULT NULL,
  `loan_added_date` datetime NOT NULL DEFAULT current_timestamp(),
  `minutes` varchar(200) DEFAULT NULL,
  `reg_fee` decimal(18,2) NOT NULL DEFAULT 0.00,
  `delete_requested` enum('Yes','No') NOT NULL DEFAULT 'No',
  `delete_by` varchar(200) DEFAULT NULL,
  `delete_approve_by` varchar(200) DEFAULT NULL,
  `delete_athourise_by` varchar(200) DEFAULT NULL,
  `delete_rejected_by` varchar(200) DEFAULT NULL,
  `latest_delete_operation_comment` text DEFAULT NULL,
  `currency` varchar(200) NOT NULL,
  `off_taker` int(11) DEFAULT NULL,
  `processing_fee` double(18,2) DEFAULT 0.00,
  `calculation_type` varchar(200) NOT NULL,
  `paid_off` enum('Yes','No') NOT NULL DEFAULT 'No'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `loan`
--

INSERT INTO `loan` (`loan_id`, `loan_number`, `loan_product`, `loan_customer`, `customer_type`, `loan_date`, `loan_principal`, `loan_period`, `period_type`, `loan_interest`, `loan_interest_amount`, `loan_amount_term`, `loan_amount_total`, `next_payment_id`, `next_payment_id_rescheduled`, `worthness_file`, `narration`, `loan_added_by`, `loan_approved_by`, `approved_date`, `rejected_by`, `rejection_reasons`, `rejected_date`, `counter`, `loannumber`, `loan_status`, `loan_recommended_by`, `loan_recommended_date`, `recommend_reasons`, `disbursed_amount`, `disbursed`, `disbursed_by`, `disbursed_date`, `written_off_by`, `write_off_approved_by`, `write_off_approval_date`, `written_off_date`, `loan_added_date`, `minutes`, `reg_fee`, `delete_requested`, `delete_by`, `delete_approve_by`, `delete_athourise_by`, `delete_rejected_by`, `latest_delete_operation_comment`, `currency`, `off_taker`, `processing_fee`, `calculation_type`, `paid_off`) VALUES
(1, 'INVODis0001-25', 1, 2, 'institution', '2025-12-05', '320000.00', 60, 'Monthly', 7, '1344000.00', '0.00', '1664000.00', 1, 1, NULL, '', 139, NULL, NULL, NULL, '', NULL, 1, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-05-16 10:26:21', NULL, NULL, NULL, NULL, '2025-05-16 10:26:21', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(2, 'INVODis0002-25', 1, 2, 'institution', '2025-12-05', '320000.00', 2, 'Monthly', 7, '44800.00', '0.00', '364800.00', 1, 1, NULL, 'Client Just issues an invoice to Mopani and would like to have it discounted. The invoice has been confirmed be valid.', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 2, NULL, 'ACTIVE', '', NULL, '', '320000.00', 'Yes', '138', '2025-07-02 09:36:43', NULL, NULL, NULL, NULL, '2025-05-21 08:32:21', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(3, 'INVODis0003-25', 1, 12, 'institution', '2025-12-05', '195000.00', 2, 'Monthly', 7, '27300.00', '0.00', '222300.00', 1, 1, NULL, 'The Client is requesting for an invoice discounting facility of K195k. ', 139, NULL, NULL, NULL, '', NULL, 3, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-05-21 10:22:18', NULL, NULL, NULL, NULL, '2025-05-21 10:22:18', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(4, 'INVODis0004-25', 1, 2, 'institution', '2025-08-05', '380000.00', 2, 'Monthly', 7, '53200.00', '0.00', '433200.00', 1, 1, NULL, 'Request for Invoice discounting ', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 4, NULL, 'ACTIVE', '', NULL, '', '380000.00', 'Yes', '138', '2025-07-02 09:36:31', NULL, NULL, NULL, NULL, '2025-05-21 10:27:49', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(5, 'INVODis0005-25', 1, 14, 'institution', '2025-05-21', '100000.00', 2, 'Monthly', 7, '14000.00', '0.00', '114000.00', 1, 1, NULL, '', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 5, NULL, 'ACTIVE', '', NULL, '', '100000.00', 'Yes', '138', '2025-07-02 09:36:17', NULL, NULL, NULL, NULL, '2025-05-21 13:27:16', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(6, 'OrderFi0001-25', 2, 24, 'institution', '2025-05-27', '170000.00', 2, 'Monthly', 12, '40800.00', '0.00', '210800.00', 1, 1, NULL, 'Flabene Engineering has submitted a request for funds. Please find the details of the transaction below: \r\n \r\nTransaction Overview\r\nFlabene has received an order from Lumwana valued at K328,023.00 for the supply of  energy-saving fluorescent tubes, circuits, and heater elements (electrical components). Although these items are available locally (Kitwe), the client has opted to procure them from Lusaka due to more competitive pricing. The items are ex-stock, and upon payment, delivery can be completed within three days.  \r\n\r\nPricing & Collateral Details  \r\n- Pricing targeted at 12% per month \r\n\r\nTenor\r\n Facility to be structured for 60 days.                       \r\nCollateral Provided\r\n  - Toyota Mark X (2012 Model) valued at K150,000.00  \r\n  - Toyota Dyna (Canter, 2006 Model) valued at K300,000.00  \r\n  - Total Collateral Value K450,000.00  \r\n\r\nJustification for Approval\r\n- Flabene is a trusted client with an excellent payment history.  \r\n- The client has multiple receivables, including from ie Mopani, strengthening their financial reliability.  \r\n\r\nIn light of the above, I strongly recommend Flabene for the order financing facility. I would appreciate the team’s review and consideration of this request at the earliest convenience.  \r\n', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 1, NULL, 'ACTIVE', '', NULL, '', '170000.00', 'Yes', '138', '2025-07-02 09:36:08', NULL, NULL, NULL, NULL, '2025-05-27 13:31:43', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(7, 'SP100001-25', 5, 38, 'institution', '2025-06-05', '120000.00', 2, 'Monthly', 7, '16800.00', '0.00', '136800.00', 1, 1, NULL, 'We have received an order finance application from Westmead Limited. Below are the details of the transaction:\r\nThe client has secured an order from Lumwana valued at K504,977 for the supply and installation of a fire suppression system. Westmead Limited has extensive experience in this field and has successfully completed similar projects in the past. Most of the required materials are readily available, except for one panel, which needs to be sourced locally. The cost of acquiring this panel is K120,000, which the client requires financing for in order to complete the works.\r\nThe installation and invoicing process will take approximately seven (7) days, and the facility will be structured for 60 days at an interest rate of 12% per month.\r\nAs collateral, the client has provided a title deed for House No. 10, Kantanta Street, Nkana East, located next to EIZ. The property has a Forced Sale Value (FSV) of K1.2 million.\r\nGiven the client’s excellent payment history, Irecommend this facility for approval.\r\n', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 1, NULL, 'ACTIVE', '', NULL, '', '120000.00', 'Yes', '138', '2025-07-02 09:31:01', NULL, NULL, NULL, NULL, '2025-06-03 12:33:16', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 28, 0.00, 'Bullet Payment', 'No'),
(8, 'INVODis0006-25', 1, 34, 'institution', '2025-05-30', '70000.00', 2, 'Monthly', 7, '9800.00', '0.00', '79800.00', 1, 1, NULL, '', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 6, NULL, 'ACTIVE', '', NULL, '', '70000.00', 'Yes', '138', '2025-07-02 09:30:51', NULL, NULL, NULL, NULL, '2025-06-04 07:25:56', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(9, 'INVODis0007-25', 1, 31, 'institution', '2025-05-07', '200000.00', 2, 'Monthly', 7, '28000.00', '0.00', '228000.00', 1, 1, NULL, '- Client has excellent payment History.\r\n- Facility to be secured by collateral ', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 7, NULL, 'ACTIVE', '', NULL, '', '200000.00', 'Yes', '138', '2025-07-02 09:30:40', NULL, NULL, NULL, NULL, '2025-06-04 09:58:27', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(10, 'INVODis0008-25', 1, 39, 'institution', '2025-06-04', '100000.00', 2, 'Monthly', 7, '14000.00', '0.00', '114000.00', 1, 1, NULL, 'Client is requesting for an Invoice Discounting Facility against Mopani invoices\r\nAn officer from Fundit to be added as co-signatory', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 8, NULL, 'ACTIVE', '', NULL, '', '100000.00', 'Yes', '138', '2025-07-02 09:30:29', NULL, NULL, NULL, NULL, '2025-06-04 10:25:22', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(11, 'INVODis0009-25', 1, 43, 'institution', '2025-06-06', '600000.00', 2, 'Monthly', 5, '60000.00', '0.00', '660000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 9, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-09 09:21:11', NULL, NULL, NULL, NULL, '2025-06-09 09:21:11', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(12, 'INVODis00010-25', 1, 43, 'institution', '2025-06-06', '600000.00', 2, 'Monthly', 5, '60000.00', '0.00', '660000.00', 1, 1, NULL, NULL, 146, NULL, NULL, NULL, '', NULL, 10, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-09 09:24:29', NULL, NULL, NULL, NULL, '2025-06-09 09:24:29', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', NULL, 0.00, 'Bullet Payment', 'No'),
(13, 'OrderFi0002-25', 2, 29, 'institution', '2025-05-29', '40000.00', 2, 'Monthly', 12, '9600.00', '0.00', '49600.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 2, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-09 09:27:20', NULL, NULL, NULL, NULL, '2025-06-09 09:27:20', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(14, 'INVODis00011-25', 1, 33, 'institution', '2025-05-20', '115000.00', 2, 'Monthly', 9.5, '21850.00', '0.00', '136850.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 11, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-09 09:53:24', NULL, NULL, NULL, NULL, '2025-06-09 09:53:24', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(15, 'INVODis00012-25', 1, 48, 'institution', '2025-06-11', '68000.00', 2, 'Monthly', 7, '9520.00', '0.00', '77520.00', 1, 1, NULL, 'Turnkey has been our client for quite sometime. They are the rporietors of Sochi Investments who gave us good business during our time at INDE. \r\nThe client has an invoice issued to MCM and would love it discounted to enable them finance other orders\r\nPlease note that we are already signatories to their bank account.', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 12, NULL, 'ACTIVE', '', NULL, '', '68000.00', 'Yes', '138', '2025-07-02 09:30:13', NULL, NULL, NULL, NULL, '2025-06-11 09:35:09', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(16, 'INVODis00013-25', 1, 30, 'institution', '2025-06-11', '195000.00', 2, 'Monthly', 7, '27300.00', '0.00', '222300.00', 1, 1, NULL, 'Following our discussion and approval via WhatsApp, Shamuchisha Limited is approaching Fundit for an invoice discounting facility of K195,000.00 against the invoice valued at K244,112.05 issued to Mopani.\r\nPlease note that Shamuchisha has already a facility running of K195k raising the exposure to K390,000.00 . However, the client demonstrated a strong payment history.\r\nThe facility will be structured for a period of 60 days at an interest rate of 7% per month.\r\nPlease note that we are already  co-signatory to the account', 139, 138, '2025-06-17 14:24:16', NULL, '', NULL, 13, NULL, 'ACTIVE', '', NULL, '', '195000.00', 'Yes', '138', '2025-07-02 09:30:01', NULL, NULL, NULL, NULL, '2025-06-11 14:17:39', '', '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(17, 'INVODis00014-25', 1, 1, 'institution', '2025-06-11', '1000000.00', 3, 'Monthly', 3.67, '110100.00', '0.00', '1110100.00', 1, 1, NULL, 'As per our prior discussions and approval via WhatsApp, Kenport Logistics Limited is seeking a K1 million facility against invoices issued to Zambia Breweries worth K2.3 Million.\r\nCurrently, the client has an existing K2.8 million facility, which is due in the first week of July 2025. Granting this new facility will increase their total exposure to K3.8 million.\r\nWe are confident in this arrangement, given the client\'s demonstrated ability to obtain funding exceeding their current request at the time we were at INDE. Additionally, it is important to note that we are co-signatory to the account.\r\nThe facility is structured for 90 days, with provisions to prorate in the event of payment between the 30th and 60th day. As agreed, the pricing will align with the INDE facility to facilitate the full onboarding of the client.', 139, 139, '2025-06-17 14:26:46', NULL, '', NULL, 14, NULL, 'ACTIVE', '', NULL, '', '1000000.00', 'Yes', '138', '2025-07-02 09:29:40', NULL, NULL, NULL, NULL, '2025-06-11 14:21:17', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(18, 'INVODis00015-25', 1, 35, 'institution', '2025-04-02', '370000.00', 2, 'Monthly', 7, '51800.00', '0.00', '421800.00', 1, 1, NULL, '', 146, 139, '2025-07-23 11:14:45', NULL, '', NULL, 15, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-16 15:20:44', NULL, NULL, NULL, NULL, '2025-06-16 15:20:44', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(19, 'INVODis00016-25', 1, 15, 'institution', '2025-04-16', '1000000.00', 3, 'Monthly', 7, '210000.00', '0.00', '1210000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 16, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-16 15:45:50', NULL, NULL, NULL, NULL, '2025-06-16 15:45:50', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 49, 0.00, 'Bullet Payment', 'No'),
(20, 'INVODis00017-25', 1, 10, 'institution', '2025-04-23', '600000.00', 2, 'Monthly', 10, '120000.00', '0.00', '720000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 17, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-16 16:08:39', NULL, NULL, NULL, NULL, '2025-06-16 16:08:39', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(21, 'INVODis00018-25', 1, 21, 'institution', '2025-04-09', '40000.00', 1, 'Monthly', 16, '6400.00', '0.00', '46400.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 18, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-17 09:02:48', NULL, NULL, NULL, NULL, '2025-06-17 09:02:48', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '146', 18, 0.00, 'Bullet Payment', 'No'),
(22, 'INVODis00019-25', 1, 50, 'institution', '2025-06-05', '520000.00', 2, 'Monthly', 8, '83200.00', '0.00', '603200.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 19, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-17 09:37:37', NULL, NULL, NULL, NULL, '2025-06-17 09:37:37', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 51, 0.00, 'Bullet Payment', 'No'),
(23, 'INVODis00020-25', 1, 50, 'institution', '2025-04-15', '200000.00', 2, 'Monthly', 8, '32000.00', '0.00', '232000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 20, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-17 09:44:15', NULL, NULL, NULL, NULL, '2025-06-17 09:44:15', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 51, 0.00, 'Bullet Payment', 'No'),
(24, 'INVODis00021-25', 1, 50, 'institution', '2025-06-17', '50000.00', 2, 'Monthly', 8, '8000.00', '0.00', '58000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 21, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-17 11:22:43', NULL, NULL, NULL, NULL, '2025-06-17 11:22:43', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 51, 0.00, 'Bullet Payment', 'No'),
(25, 'INVODis00022-25', 1, 33, 'institution', '2024-09-25', '240000.00', 2, 'Monthly', 10, '48000.00', '0.00', '288000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 22, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 13:38:05', NULL, NULL, NULL, NULL, '2025-06-18 13:38:05', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(26, 'INVODis00023-25', 1, 38, 'institution', '2024-09-25', '50000.00', 1, 'Monthly', 10, '5000.00', '0.00', '55000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 23, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 13:40:09', NULL, NULL, NULL, NULL, '2025-06-18 13:40:09', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 28, 0.00, 'Bullet Payment', 'No'),
(27, 'INVODis00024-25', 1, 31, 'institution', '2024-10-11', '600000.00', 3, 'Monthly', 8, '144000.00', '0.00', '744000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 24, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 13:42:06', NULL, NULL, NULL, NULL, '2025-06-18 13:42:06', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(28, 'INVODis00025-25', 1, 2, 'institution', '2024-11-06', '288000.00', 2, 'Monthly', 7, '40320.00', '0.00', '328320.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 25, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:16:46', NULL, NULL, NULL, NULL, '2025-06-18 14:16:46', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(29, 'INVODis00026-25', 1, 33, 'institution', '2024-11-11', '240000.00', 2, 'Monthly', 10, '48000.00', '0.00', '288000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 26, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:20:50', NULL, NULL, NULL, NULL, '2025-06-18 14:20:50', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(30, 'INVODis00027-25', 1, 30, 'institution', '2024-11-20', '280000.00', 1, 'Monthly', 7, '19600.00', '0.00', '299600.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:50:16', NULL, '', NULL, 27, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:27:19', NULL, NULL, NULL, NULL, '2025-06-18 14:27:19', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(31, 'INVODis00028-25', 1, 2, 'institution', '2024-11-20', '379000.00', 2, 'Monthly', 7, '53060.00', '0.00', '432060.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 28, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:31:13', NULL, NULL, NULL, NULL, '2025-06-18 14:31:13', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(32, 'INVODis00029-25', 1, 48, 'institution', '2024-11-27', '90000.00', 2, 'Monthly', 7, '12600.00', '0.00', '102600.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 29, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:33:13', NULL, NULL, NULL, NULL, '2025-06-18 14:33:13', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(33, 'INVODis00030-25', 1, 31, 'institution', '2024-12-05', '400000.00', 3, 'Monthly', 7, '84000.00', '0.00', '484000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 30, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:35:24', NULL, NULL, NULL, NULL, '2025-06-18 14:35:24', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(34, 'INVODis00031-25', 1, 38, 'institution', '2024-12-11', '65000.00', 2, 'Monthly', 10, '13000.00', '0.00', '78000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 31, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 14:39:36', NULL, NULL, NULL, NULL, '2025-06-18 14:39:36', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 28, 0.00, 'Bullet Payment', 'No'),
(35, 'INVODis00032-25', 1, 2, 'institution', '2024-12-14', '328000.00', 3, 'Monthly', 7, '68880.00', '0.00', '396880.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 32, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 15:28:27', NULL, NULL, NULL, NULL, '2025-06-18 15:28:27', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(36, 'INVODis00033-25', 1, 34, 'institution', '2024-12-23', '261000.00', 10, 'Monthly', 1, '26100.00', '0.00', '287100.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 33, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 15:32:27', NULL, NULL, NULL, NULL, '2025-06-18 15:32:27', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 52, 0.00, 'Bullet Payment', 'No'),
(37, 'INVODis00034-25', 1, 2, 'institution', '2024-10-28', '329000.00', 2, 'Monthly', 7, '46060.00', '0.00', '375060.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 34, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-18 15:36:59', NULL, NULL, NULL, NULL, '2025-06-18 15:36:59', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(38, 'OrderFi0003-25', 2, 29, 'institution', '2024-12-09', '150000.00', 3, 'Monthly', 12, '54000.00', '0.00', '204000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 3, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:22:04', NULL, NULL, NULL, NULL, '2025-06-19 09:22:04', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(39, 'INVODis00035-25', 1, 30, 'institution', '2024-12-26', '180000.00', 2, 'Monthly', 7, '25200.00', '0.00', '205200.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 35, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:30:01', NULL, NULL, NULL, NULL, '2025-06-19 09:30:01', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(40, 'INVODis00036-25', 1, 30, 'institution', '2025-01-16', '138000.00', 3, 'Monthly', 7, '28980.00', '0.00', '166980.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 36, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:31:56', NULL, NULL, NULL, NULL, '2025-06-19 09:31:56', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(41, 'INVODis00037-25', 1, 31, 'institution', '2025-01-17', '300000.00', 3, 'Monthly', 7, '63000.00', '0.00', '363000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 37, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:34:08', NULL, NULL, NULL, NULL, '2025-06-19 09:34:08', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(42, 'INVODis00038-25', 1, 33, 'institution', '2025-02-05', '300000.00', 2, 'Monthly', 7, '42000.00', '0.00', '342000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 38, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:37:22', NULL, NULL, NULL, NULL, '2025-06-19 09:37:22', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(43, 'INVODis00039-25', 1, 33, 'institution', '2025-02-05', '366800.00', 2, 'Monthly', 10, '73360.00', '0.00', '440160.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:49:50', NULL, '', NULL, 39, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:43:42', NULL, NULL, NULL, NULL, '2025-06-19 09:43:42', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(44, 'OrderFi0004-25', 2, 29, 'institution', '2025-02-12', '210000.00', 3, 'Monthly', 12, '75600.00', '0.00', '285600.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:50:50', NULL, '', NULL, 4, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:47:49', NULL, NULL, NULL, NULL, '2025-06-19 09:47:49', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(45, 'INVODis00040-25', 1, 2, 'institution', '2025-02-13', '438000.00', 3, 'Monthly', 7, '91980.00', '0.00', '529980.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 40, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:53:11', NULL, NULL, NULL, NULL, '2025-06-19 09:53:11', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(46, 'INVODis00041-25', 1, 2, 'institution', '2025-02-28', '477000.00', 2, 'Monthly', 7, '66780.00', '0.00', '543780.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 41, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 09:55:18', NULL, NULL, NULL, NULL, '2025-06-19 09:55:18', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(47, 'INVODis00042-25', 1, 33, 'institution', '2025-02-07', '366800.00', 1, 'Monthly', 10, '36680.00', '0.00', '403480.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:49:24', NULL, '', NULL, 42, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 10:19:38', NULL, NULL, NULL, NULL, '2025-06-19 10:19:38', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(48, 'INVODis00043-25', 1, 53, 'institution', '2024-11-11', '300000.00', 2, 'Monthly', 7, '42000.00', '0.00', '342000.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:48:33', NULL, '', NULL, 43, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 11:26:03', NULL, NULL, NULL, NULL, '2025-06-19 11:26:03', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(49, 'INVODis00044-25', 1, 54, 'institution', '2024-12-03', '400000.00', 1, 'Monthly', 6, '24000.00', '0.00', '424000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 44, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 11:51:47', NULL, NULL, NULL, NULL, '2025-06-19 11:51:47', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(50, 'INVODis00045-25', 1, 54, 'institution', '2025-02-20', '350000.00', 1, 'Monthly', 7, '24500.00', '0.00', '374500.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 45, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 12:03:50', NULL, NULL, NULL, NULL, '2025-06-19 12:03:50', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 18, 0.00, 'Bullet Payment', 'No'),
(51, 'INVODis00046-25', 1, 53, 'institution', '2025-02-26', '350000.00', 1, 'Monthly', 7, '24500.00', '0.00', '374500.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:48:05', NULL, '', NULL, 46, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 12:09:47', NULL, NULL, NULL, NULL, '2025-06-19 12:09:47', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(52, 'INVODis00047-25', 1, 53, 'institution', '2024-10-09', '550000.00', 550000, 'Monthly', 7, '21175000000.00', '0.00', '21175550000.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 47, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 12:43:23', NULL, NULL, NULL, NULL, '2025-06-19 12:43:23', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(53, 'INVODis00048-25', 1, 53, 'institution', '2024-10-09', '550000.00', 2, 'Monthly', 7, '77000.00', '0.00', '627000.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:44:13', NULL, '', NULL, 48, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 12:46:33', NULL, NULL, NULL, NULL, '2025-06-19 12:46:33', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(54, 'INVODis00049-25', 1, 55, 'institution', '2025-04-01', '67000.00', 1, 'Monthly', 3.5, '2345.00', '0.00', '69345.00', 1, 1, NULL, '', 146, NULL, NULL, NULL, '', NULL, 49, NULL, 'INITIATED', '', NULL, '', '0.00', 'No', '', '2025-06-19 14:25:59', NULL, NULL, NULL, NULL, '2025-06-19 14:25:59', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '146', 8, 0.00, 'Bullet Payment', 'No'),
(55, 'INVODis00050-25', 1, 55, 'institution', '2025-04-01', '67000.00', 1, 'Monthly', 3.75, '2512.50', '0.00', '69512.50', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:34', NULL, '', NULL, 50, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-19 14:28:14', NULL, NULL, NULL, NULL, '2025-06-19 14:28:14', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '146', 8, 0.00, 'Bullet Payment', 'No'),
(56, 'INVODis00051-25', 1, 29, 'institution', '2025-04-16', '160000.00', 2, 'Monthly', 7, '22400.00', '0.00', '182400.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:44:37', NULL, '', NULL, 51, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-20 08:04:42', NULL, NULL, NULL, NULL, '2025-06-20 08:04:42', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(57, 'INVODis00052-25', 1, 2, 'institution', '2025-06-20', '143000.00', 2, 'Monthly', 7, '20020.00', '0.00', '163020.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:44:30', NULL, '', NULL, 52, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-20 13:54:21', NULL, NULL, NULL, NULL, '2025-06-20 13:54:21', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(58, 'INVODis00053-25', 1, 10, 'institution', '2023-06-23', '600000.00', 2, 'Monthly', 10, '120000.00', '0.00', '720000.00', 1, 1, NULL, 'Facility Request Summary: Invoice Discounting – Quorn Floor\r\n\r\nFacility Amount Requested: K600,000\r\n\r\nInvoice Value: K907,555\r\n\r\nInvoice Debtor: Konkola Copper Mines (KCM)\r\n\r\nPurpose: Discounting against invoice for works completed by Quorn Floor\r\n\r\nFacility History:\r\n\r\nQuorn Floor has no current outstanding facility with Fundit.\r\n\r\nPrevious facility has been cleared; however, existing collateral from that arrangement—two tipper trucks—remains with Fundit and will be applied to this new facility.\r\n\r\nSecurity:\r\n\r\nCollateral: Two tipper trucks (held from previous facility)\r\n\r\nNo additional collateral being offered at this point\r\n\r\nComments:\r\n\r\nContinued relationship with Fundit, with prior successful facility completion\r\n\r\nFacility well-covered by invoice value\r\n\r\nUse of existing collateral enhances processing efficiency and reduces setup time', 139, 138, '2025-07-02 10:27:25', NULL, '', NULL, 53, NULL, 'APPROVED_FIRST', '', NULL, '', '0.00', 'No', '', '2025-06-23 08:38:25', NULL, NULL, NULL, NULL, '2025-06-23 08:38:25', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 46, 0.00, 'Bullet Payment', 'No'),
(59, 'INVODis00054-25', 1, 56, 'institution', '2025-06-27', '192000.00', 2, 'Monthly', 7, '26880.00', '0.00', '218880.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:44:20', NULL, '', NULL, 54, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-06-27 09:54:35', NULL, NULL, NULL, NULL, '2025-06-27 09:54:35', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(60, 'OrderFi0005-25', 2, 29, 'institution', '2025-07-01', '854.00', 3, 'Monthly', 12, '307.44', '0.00', '1161.44', 1, 1, NULL, 'We have received an application for order finance with the following details:\r\nOrder Value: K2,019,750.00\r\nAmount Required: K609,400.00\r\nOff-taker: Mopani\r\nThe client currently has two invoice discount facilities totaling K244,768.00. They propose to offset this amount from the new facility, effectively tagging the facility at K854,168.00. Once the outstanding amount is deducted, the client will receive the K609,400.00 required for the order.\r\nThe client is fabricating Bateman spare feeders, with materials being sourced both locally (from Crap Dealers) and from South Africa. The stock is ex-stock, and the full order execution will take approximately 3–4 weeks. The client has also made provision for stage-based delivery and invoicing.\r\nStrengths of the Deal:\r\nThe client is experienced and has a history of executing similar projects.\r\nThey maintain a solid relationship with the white metal supplier in South Africa.\r\nThe client has multiple streams of income, which supports repayment confidence.\r\nSecurity:\r\nWe hold a title deed for a house in Riverside owned by the director, which holds significant value and will serve as collateral.\r\nPricing:\r\nThe facility is proposed at 12% per month, over a period of 90 days, with the flexibility to prorate.\r\nRecommendation:\r\nBased on the above, this facility is recommended for approval.\r\nI look forward to the team’s feedback.', 139, 139, '2025-07-23 10:43:30', NULL, '', NULL, 5, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-01 14:40:51', NULL, NULL, NULL, NULL, '2025-07-01 14:40:51', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(61, 'INVODis00055-25', 1, 57, 'institution', '2025-07-03', '130000.00', 2, 'Monthly', 7, '18200.00', '0.00', '148200.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:43:23', NULL, '', NULL, 55, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-03 08:58:01', NULL, NULL, NULL, NULL, '2025-07-03 08:58:01', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(62, 'INVODis00056-25', 1, 30, 'institution', '2025-07-02', '197000.00', 2, 'Monthly', 7, '27580.00', '0.00', '224580.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:43:16', NULL, '', NULL, 56, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-03 09:49:13', NULL, NULL, NULL, NULL, '2025-07-03 09:49:13', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(63, 'INVODis00057-25', 1, 14, 'institution', '2025-07-02', '130000.00', 2, 'Monthly', 7, '18200.00', '0.00', '148200.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:43:11', NULL, '', NULL, 57, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-03 10:31:53', NULL, NULL, NULL, NULL, '2025-07-03 10:31:53', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No'),
(64, 'INVODis00058-25', 1, 1, 'institution', '2025-07-11', '550000.00', 3, 'Monthly', 11, '181500.00', '0.00', '731500.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:48', NULL, '', NULL, 58, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-11 15:52:39', NULL, NULL, NULL, NULL, '2025-07-11 15:52:39', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(65, 'INVODis00059-25', 1, 1, 'institution', '2025-07-11', '2110.00', 3, 'Monthly', 11, '696.30', '0.00', '2806.30', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:42', NULL, '', NULL, 59, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-11 16:06:26', NULL, NULL, NULL, NULL, '2025-07-11 16:06:26', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(66, 'INVODis00060-25', 1, 1, 'institution', '2025-07-11', '2.00', 3, 'Monthly', 11, '0.66', '0.00', '2.66', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:23', NULL, '', NULL, 60, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-11 16:08:20', NULL, NULL, NULL, NULL, '2025-07-11 16:08:20', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(67, 'INVODis00061-25', 1, 1, 'institution', '2025-07-11', '2110000.00', 3, 'Monthly', 11, '696300.00', '0.00', '2806300.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:15', NULL, '', NULL, 61, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-15 13:41:56', NULL, NULL, NULL, NULL, '2025-07-15 13:41:56', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 45, 0.00, 'Bullet Payment', 'No'),
(68, 'INVODis00062-25', 1, 12, 'institution', '2025-07-15', '78000.00', 2, 'Monthly', 7, '10920.00', '0.00', '88920.00', 1, 1, NULL, '', 146, 139, '2025-07-23 10:42:07', NULL, '', NULL, 62, NULL, 'RECOMMENDED', '', NULL, '', '0.00', 'No', '', '2025-07-16 10:26:29', NULL, NULL, NULL, NULL, '2025-07-16 10:26:29', NULL, '0.00', 'No', NULL, NULL, NULL, NULL, NULL, '159', 8, 0.00, 'Bullet Payment', 'No');

-- --------------------------------------------------------

--
-- Table structure for table `loangeneralcashprocessed`
--

CREATE TABLE `loangeneralcashprocessed` (
  `id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `group_code` int(11) NOT NULL,
  `CLIENTNAME` varchar(52) DEFAULT NULL,
  `INSTITUTION` varchar(11) DEFAULT NULL,
  `MALE` varchar(7) DEFAULT NULL,
  `FEMALE` varchar(6) DEFAULT NULL,
  `LOCATION` varchar(9) DEFAULT NULL,
  `PHONENUMBER` varchar(32) DEFAULT NULL,
  `LOANNO` varchar(15) DEFAULT NULL,
  `disbursed_date` varchar(10) DEFAULT NULL,
  `TO` varchar(10) DEFAULT NULL,
  ` UPFRONT FEES` varchar(14) DEFAULT NULL,
  ` COLLATERAL` varchar(14) DEFAULT NULL,
  `LAMOUNT` varchar(15) DEFAULT NULL,
  ` INTEREST` varchar(14) DEFAULT NULL,
  ` LINTEREST` varchar(15) DEFAULT NULL,
  `loan_product_id` varchar(3) DEFAULT NULL,
  `loan_period` int(11) DEFAULT NULL,
  `MREPAYMENT` varchar(14) DEFAULT NULL,
  ` TOTAL` varchar(15) DEFAULT NULL,
  ` LOANBALANCE` varchar(14) DEFAULT NULL,
  `AMOUNTPAID` varchar(15) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `loan_collaterals`
--

CREATE TABLE `loan_collaterals` (
  `loan_collateral_id` int(11) NOT NULL,
  `collateral_loan_id` int(11) NOT NULL,
  `collateral_name` varchar(100) NOT NULL,
  `collateral_type` varchar(50) DEFAULT NULL,
  `collateral_serial` varchar(50) DEFAULT NULL,
  `collateral_value` decimal(18,2) NOT NULL,
  `collateral_file` varchar(100) DEFAULT NULL,
  `collateral_desc` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `loan_collaterals`
--

INSERT INTO `loan_collaterals` (`loan_collateral_id`, `collateral_loan_id`, `collateral_name`, `collateral_type`, `collateral_serial`, `collateral_value`, `collateral_file`, `collateral_desc`) VALUES
(0, 1, 'Mazda CX 5', 'Asset', '1838383', '50000.00', '462Daeyang QUote.png', 'thebndfjfd'),
(0, 2, 'Kaya', 'assets', '773737', '5000.00', '314Infocus Techlogy(RG6252) loan report as on2025-05-05 (1).pdf', '');

-- --------------------------------------------------------

--
-- Table structure for table `loan_files`
--

CREATE TABLE `loan_files` (
  `file_id` int(11) NOT NULL,
  `loan_id` varchar(200) NOT NULL,
  `file_name` varchar(200) NOT NULL,
  `real_file` varchar(200) NOT NULL,
  `file_stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `loan_files`
--

INSERT INTO `loan_files` (`file_id`, `loan_id`, `file_name`, `real_file`, `file_stamp`) VALUES
(1, '1', 'POS_Quote.pdf', 'POS Quote.pdf', '2025-05-05 14:56:26'),
(2, '2', 'POS_Quote.pdf', 'POS Quote.pdf', '2025-05-08 11:44:05'),
(3, '5', 'SMART_INVOICE_44.pdf', 'SMART INVOICE 44.pdf', '2025-05-21 13:27:16'),
(4, '6', 'PO_-_4501194470_-_7870.pdf', 'PO - 4501194470 - 7870.pdf', '2025-05-27 13:31:43'),
(5, '9', 'INVOICE_114.pdf', 'INVOICE 114.pdf', '2025-06-04 09:58:27'),
(6, '10', 'Invoices.zip', 'Invoices.zip', '2025-06-04 10:25:22'),
(7, '11', 'APPLICATION_TO_BORROW.pdf', 'APPLICATION TO BORROW.pdf', '2025-06-09 09:21:11'),
(8, '12', 'APPLICATION_TO_BORROW.pdf', 'APPLICATION TO BORROW.pdf', '2025-06-09 09:24:29'),
(9, '12', 'Board_Meeting_Summary_-_Jun_25.pdf', 'Board Meeting Summary - Jun 25.pdf', '2025-06-09 09:24:29'),
(10, '12', 'Deed_of_Assignment-Big_brand_-_Signed.pdf', 'Deed of Assignment-Big brand -  Signed.pdf', '2025-06-09 09:24:29'),
(11, '13', 'Application_For_Order_Financing_.pdf', 'Application For Order Financing .pdf', '2025-06-09 09:27:20'),
(12, '14', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'Director\'s Personal Guarantee - DEVOURGE .pdf', '2025-06-09 09:53:24'),
(13, '18', 'Facility_letter-Valve_Corp.docx', 'Facility letter-Valve Corp.docx', '2025-06-16 15:20:44'),
(14, '19', 'Letter_of_Guarantee_Wheals_.pdf', 'Letter of Guarantee Wheals .pdf', '2025-06-16 15:45:50'),
(15, '20', 'Directors_Personal_Guarantee_.pdf', 'Directors Personal Guarantee .pdf', '2025-06-16 16:08:39'),
(16, '21', 'Letter_of_guarantee_RGPM.docx', 'Letter of guarantee RGPM.docx', '2025-06-17 09:02:48'),
(17, '22', 'APPLICATION_FOR_INVOICE_DISCOUNTING_THEMEFEX_.pdf', 'APPLICATION FOR INVOICE DISCOUNTING THEMEFEX .pdf', '2025-06-17 09:37:37'),
(18, '24', 'Facility_letter-Themefex.docx', 'Facility letter-Themefex.docx', '2025-06-17 11:22:43'),
(19, '25', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'Director\'s Personal Guarantee - DEVOURGE .pdf', '2025-06-18 13:38:05'),
(20, '26', 'WESTMEAD_CONSENT_LETTER.pdf', 'WESTMEAD CONSENT LETTER.pdf', '2025-06-18 13:40:09'),
(21, '27', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'SALE AND PURCHASE AGREEEMENT CHIMWE INVESTMENTS.pdf', '2025-06-18 13:42:06'),
(22, '28', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'SALE AND PURCHASE AGREEEMENT CHIMWE INVESTMENTS.pdf', '2025-06-18 14:16:46'),
(23, '29', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'Director\'s Personal Guarantee - DEVOURGE .pdf', '2025-06-18 14:20:50'),
(24, '31', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'DIRECTOR\'S PERSONAL GUARANTEE.pdf', '2025-06-18 14:31:13'),
(25, '32', 'FACILITY_LETTER_TURNKEY.pdf', 'FACILITY LETTER TURNKEY.pdf', '2025-06-18 14:33:13'),
(26, '33', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'SALE AND PURCHASE AGREEEMENT CHIMWE INVESTMENTS.pdf', '2025-06-18 14:35:24'),
(27, '34', 'WESTMEAD_CONSENT_LETTER.pdf', 'WESTMEAD CONSENT LETTER.pdf', '2025-06-18 14:39:36'),
(28, '36', 'Directors_Guarantee_Hemiwards_.pdf', 'Director\'s Guarantee Hemiwards .pdf', '2025-06-18 15:32:27'),
(29, '37', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'DIRECTOR\'S PERSONAL GUARANTEE.pdf', '2025-06-18 15:36:59'),
(30, '38', 'Letter_Of_Guarantee_Fox_.pdf', 'Letter Of Guarantee Fox .pdf', '2025-06-19 09:22:04'),
(31, '39', 'Personal_Guarantee_Shamuchisha_.pdf', 'Personal Guarantee Shamuchisha .pdf', '2025-06-19 09:30:01'),
(32, '40', 'Application_for_Invoice_Discounting_Shamuchisha_.pdf', 'Application for Invoice Discounting Shamuchisha .pdf', '2025-06-19 09:31:56'),
(33, '41', 'SALE_AND_PURCHASE_AGREEEMENT_CHIMWE_INVESTMENTS.pdf', 'SALE AND PURCHASE AGREEEMENT CHIMWE INVESTMENTS.pdf', '2025-06-19 09:34:08'),
(34, '42', 'Directors_Personal_Guarantee_-_DEVOURGE_.pdf', 'Director\'s Personal Guarantee - DEVOURGE .pdf', '2025-06-19 09:37:22'),
(35, '45', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'DIRECTOR\'S PERSONAL GUARANTEE.pdf', '2025-06-19 09:53:11'),
(36, '46', 'DIRECTORS_PERSONAL_GUARANTEE.pdf', 'DIRECTOR\'S PERSONAL GUARANTEE.pdf', '2025-06-19 09:55:18'),
(37, '47', 'APPLICATION_FOR_INVOICE_DISCOUNTING_-DEVOURGE_.pdf', 'APPLICATION FOR INVOICE DISCOUNTING -DEVOURGE .pdf', '2025-06-19 10:19:38'),
(38, '48', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', '2025-06-19 11:26:03'),
(39, '49', 'THELB_Certificate_of_Incorporation-Tax_Clearance_Directors_IDs-4.pdf', 'THELB_Certificate_of_Incorporation-Tax_Clearance_Directors_IDs-4.pdf', '2025-06-19 11:51:47'),
(40, '50', 'Thelb_Articles_of_Association_12072023.pdf', 'Thelb_Articles_of_Association_12072023.pdf', '2025-06-19 12:03:50'),
(41, '51', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', 'IC_SUPRAIG_TECH__PROCUREMENT_LTD_APPRAISAL_REPORT_11022021.pdf', '2025-06-19 12:09:47'),
(42, '52', 'Bank_statement(1).pdf', 'Bank_statement(1).pdf', '2025-06-19 12:43:23'),
(43, '53', 'AppraisalReport_51663.pdf', 'AppraisalReport_51663.pdf', '2025-06-19 12:46:33'),
(44, '54', 'Oriental_Products_Articles_of_Association_CF2_24022020.pdf', 'Oriental_Products_Articles_of_Association_CF2_24022020.pdf', '2025-06-19 14:25:59'),
(45, '55', 'Oriental_Products_Share_Capital_24022020.pdf', 'Oriental_Products_Share_Capital_24022020.pdf', '2025-06-19 14:28:14'),
(46, '56', 'Letter_Of_Guarantee_Fox_.pdf', 'Letter Of Guarantee Fox .pdf', '2025-06-20 08:04:42'),
(47, '57', 'PEREZ_PERSONAL_GUARANTEE_.pdf', 'PEREZ PERSONAL GUARANTEE .pdf', '2025-06-20 13:54:21'),
(48, '58', 'Invoice.jpg', 'Invoice.jpg', '2025-06-23 08:38:25'),
(49, '59', 'Deed_of_Assignment-_DRILL.docx', 'Deed of Assignment- DRILL.docx', '2025-06-27 09:54:35'),
(50, '60', 'Order_MCM.pdf', 'Order MCM.pdf', '2025-07-01 14:40:51'),
(51, '64', 'Deed_of_Assignment_********.pdf', 'Deed of Assignment ********.pdf', '2025-07-11 15:52:39'),
(52, '65', 'Deed_of_Assignment_********.pdf', 'Deed of Assignment ********.pdf', '2025-07-11 16:06:26'),
(53, '66', 'Deed_of_Assignment_********.pdf', 'Deed of Assignment ********.pdf', '2025-07-11 16:08:20'),
(54, '67', 'Deed_of_Assignment_********.pdf', 'Deed of Assignment ********.pdf', '2025-07-15 13:41:56'),
(55, '68', 'DEED_OF_ASSIGNMENT.pdf', 'DEED OF ASSIGNMENT.pdf', '2025-07-16 10:26:29');

-- --------------------------------------------------------

--
-- Table structure for table `loan_products`
--

CREATE TABLE `loan_products` (
  `loan_product_id` int(11) NOT NULL,
  `product_name` varchar(200) NOT NULL,
  `abbreviation` varchar(200) DEFAULT NULL,
  `interest` double DEFAULT 0,
  `calculation_type` enum('Reducing Balance','Straight Line','Bullet Payment') NOT NULL DEFAULT 'Straight Line',
  `frequency` enum('Monthly','Weekly','2 Weeks') NOT NULL DEFAULT 'Monthly',
  `penalty` double DEFAULT NULL,
  `penalty_threshold` decimal(10,0) DEFAULT 0,
  `penalty_charge_type_above` enum('Fixed','Variable') NOT NULL DEFAULT 'Variable',
  `penalty_charge_type_below` enum('Fixed','Variable') NOT NULL,
  `penalty_fixed_charge_below` decimal(10,0) DEFAULT 0,
  `penalty_variable_charge_below` double DEFAULT 0,
  `penalty_fixed_charge_above` decimal(10,0) DEFAULT 0,
  `penalty_variable_charge_above` double DEFAULT 0,
  `loan_processing_fee_threshold` decimal(10,0) DEFAULT 0,
  `processing_charge_type_above` enum('Fixed','Variable') NOT NULL,
  `processing_charge_type_below` enum('Fixed','Variable') NOT NULL,
  `processing_fixed_charge_above` decimal(18,2) DEFAULT 0.00,
  `processing_variable_charge_above` double DEFAULT 0,
  `processing_fixed_charge_below` decimal(18,2) DEFAULT 0.00,
  `processing_variable_charge_below` double DEFAULT 0,
  `minimum_principal` decimal(18,2) DEFAULT 0.00,
  `maximum_principal` decimal(18,2) DEFAULT 0.00,
  `grace_period` int(11) NOT NULL,
  `interest_min` float DEFAULT NULL,
  `interest_max` float DEFAULT NULL,
  `added_by` varchar(200) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `loan_products`
--

INSERT INTO `loan_products` (`loan_product_id`, `product_name`, `abbreviation`, `interest`, `calculation_type`, `frequency`, `penalty`, `penalty_threshold`, `penalty_charge_type_above`, `penalty_charge_type_below`, `penalty_fixed_charge_below`, `penalty_variable_charge_below`, `penalty_fixed_charge_above`, `penalty_variable_charge_above`, `loan_processing_fee_threshold`, `processing_charge_type_above`, `processing_charge_type_below`, `processing_fixed_charge_above`, `processing_variable_charge_above`, `processing_fixed_charge_below`, `processing_variable_charge_below`, `minimum_principal`, `maximum_principal`, `grace_period`, `interest_min`, `interest_max`, `added_by`, `date_created`) VALUES
(1, 'Invoice Discounting', 'INVODis', 0, 'Bullet Payment', 'Monthly', NULL, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0', 'Fixed', 'Variable', '0.00', 0, NULL, 0, '0.00', '10000000.00', 0, 2.5, 15, '72', '2024-12-26 12:35:25'),
(2, 'Order Finance', 'OrderFi', 0, 'Bullet Payment', 'Monthly', NULL, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0.00', '10000000.00', 0, 2.5, 15, '72', '2024-12-26 12:45:14'),
(3, 'Term Loans', 'TerLo', 0, 'Bullet Payment', 'Monthly', NULL, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0.00', '10000000.00', 0, 2.5, 15, '72', '2024-12-26 12:46:24'),
(4, 'Payroll-backed loans', 'Payba', 0, 'Bullet Payment', 'Monthly', NULL, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0', 'Variable', 'Variable', NULL, 0, NULL, 0, '0.00', '10000000.00', 0, 2.5, 15, '72', '2024-12-26 12:47:48'),
(5, 'SME Product #1', 'SP10', 5, 'Bullet Payment', 'Monthly', NULL, '0', 'Variable', 'Variable', '0', 0, '0', 0, '0', 'Variable', 'Variable', '0.00', 0, '0.00', 0, '10000.00', '2500000.00', 3, 2.5, 12.5, '72', '0000-00-00 00:00:00'),
(7, 'New Bullet test', 'NBT', 10, 'Bullet Payment', 'Monthly', NULL, '0', 'Fixed', 'Fixed', '0', NULL, '0', NULL, '0', 'Fixed', 'Fixed', '0.00', NULL, '0.00', NULL, '0.00', '0.00', 1, 0, 0, '72', '2025-03-26 19:09:51');

-- --------------------------------------------------------

--
-- Table structure for table `massrepayments`
--

CREATE TABLE `massrepayments` (
  `massrepayment_id` int(11) NOT NULL,
  `mass_loan_id` int(11) DEFAULT NULL,
  `loan_number` varchar(50) NOT NULL,
  `mass_loan_product_id` int(11) DEFAULT NULL,
  `loan_period` int(11) DEFAULT NULL,
  `starting_pay_num` int(11) DEFAULT NULL,
  `monthly_repayment` decimal(18,2) DEFAULT NULL,
  `amount_paid` decimal(18,2) NOT NULL,
  `paid_date` date NOT NULL,
  `massrepayment_status` enum('imported','processed','payment_made','issues','deposited') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `mass_repayment_requests`
--

CREATE TABLE `mass_repayment_requests` (
  `mass_repayment_request_id` int(11) NOT NULL,
  `file` varchar(200) NOT NULL,
  `status` enum('Pending','Completed','Failed','Has Errors') NOT NULL DEFAULT 'Pending',
  `user` varchar(200) NOT NULL,
  `progress` varchar(200) NOT NULL,
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `mass_repayment_requests_details`
--

CREATE TABLE `mass_repayment_requests_details` (
  `mass_repayment_requests_details_id` int(11) NOT NULL,
  `mass_repayment_request` int(11) NOT NULL,
  `loan_id` varchar(200) NOT NULL,
  `loan_number` varchar(200) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `payment_date` date NOT NULL,
  `status` enum('Pending','Paid','Failed') NOT NULL DEFAULT 'Pending'
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `menu`
--

CREATE TABLE `menu` (
  `id` int(10) UNSIGNED NOT NULL,
  `label` varchar(200) DEFAULT NULL,
  `type` varchar(200) DEFAULT NULL,
  `icon_color` varchar(200) DEFAULT NULL,
  `link` varchar(200) DEFAULT NULL,
  `sort` int(11) NOT NULL,
  `parent` int(11) NOT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `menu_type_id` int(11) NOT NULL,
  `active` int(11) NOT NULL,
  `roll` varchar(12) DEFAULT 'admin'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `menu`
--

INSERT INTO `menu` (`id`, `label`, `type`, `icon_color`, `link`, `sort`, `parent`, `icon`, `menu_type_id`, `active`, `roll`) VALUES
(23, 'Loan Information', 'menu', 'text_red', '#', 3, 0, 'bi bi-journal-album', 1, 1, '2'),
(38, 'Global Administration', '1', 'fa fa-cogs', '#', 11, 0, 'bi bi-gear', 1, 1, '1'),
(39, 'My account', '1', '1', '#', 12, 0, 'fa-user', 1, 0, '1'),
(40, 'Reports', '1', '1', '#', 8, 0, 'bi bi-clipboard-data', 1, 1, '1'),
(41, 'Enquiries', '1', '1', '#', 2, 0, 'bi bi-question-square', 1, 1, '1'),
(42, 'Users', '1', '1', '#', 10, 0, 'bi bi-person-badge', 1, 1, '1'),
(43, 'Personal customers', '1', 'red', '#', 1, 0, 'bi bi-journal-text', 1, 1, 'admin'),
(44, 'Expenses', '1', 'color', '#', 9, 0, 'fa-dollar-sign', 1, 0, 'admin'),
(45, 'Cost of funding', '1', 'red', '#', 4, 0, 'fa-list', 1, 0, 'admin'),
(46, 'Savings Account', '1', 'fa', '#', 4, 0, 'fa-wallet', 1, 0, 'admin'),
(47, 'Internal Accounts', '1', 're', 'Loan Settings', 4, 0, 'fa-building', 1, 1, 'admin'),
(48, 'Cash Operations', '1', 'red', '#', 4, 0, 'fa-users', 1, 0, 'admin'),
(49, 'Groups', '1', '1', '#', 2, 0, 'bi bi-people', 1, 0, 'admin'),
(50, 'Collection Sheet', '1', '1', '#', 3, 0, 'fa-calendar-check', 1, 1, 'admin'),
(51, 'Loan Products', '1', '1', '#', 2, 0, 'bi bi-journal-bookmark', 1, 1, 'admin'),
(52, 'Loan Repayments', '1', 'red', '#', 3, 0, 'bi bi-collection\n', 1, 1, 'admin'),
(53, 'Customer Logins', '1', 'red', '#', 1, 0, 'fa-lock', 1, 0, 'admin'),
(55, 'Corporate Customers ', '1', '1', '#', 2, 0, 'bi bi-briefcase', 1, 1, 'admin');

-- --------------------------------------------------------

--
-- Table structure for table `menuitems`
--

CREATE TABLE `menuitems` (
  `id` int(11) NOT NULL,
  `mid` int(11) DEFAULT NULL,
  `label` varchar(200) DEFAULT NULL,
  `method` varchar(200) DEFAULT NULL,
  `fa_icon` varchar(200) DEFAULT NULL,
  `sortt` int(11) NOT NULL DEFAULT 0,
  `active` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `menuitems`
--

INSERT INTO `menuitems` (`id`, `mid`, `label`, `method`, `fa_icon`, `sortt`, `active`) VALUES
(7, 27, 'Buy Foreign Curr From CB', 'Accounts/buy_foreign_curr', 'fa', 1, 0),
(19, 33, 'Cashier to vault', 'Tellering/move_to_vault', 'fa', 3, 0),
(21, 33, 'Vault to Cashier', 'Tellering/move_to_teller', 'fa', 6, 0),
(23, 33, 'Central Bank to vault', 'Tellering/boss_to_vault', 'fa', 9, 0),
(25, 33, 'Vault to central bank', 'Tellering/move_to_boss', 'fa', 11, 0),
(27, 36, 'Bank Activity Authorisations', 'Authorisation/authlist', 'fa', 1, 0),
(28, 37, 'Start of Day', '#', 'fa', 1, 1),
(29, 37, 'End of day', 'Tools/day_end', 'fa', 2, 1),
(30, 42, 'Employees', 'Employees', 'fa', 1, 1),
(31, 42, 'Configure System users', 'User_access', 'fa', 4, 1),
(34, 38, 'Financial Year', 'Financial_year', 'fa', 8, 1),
(35, 38, 'Branch Managemnt', 'Branches', 'fa', 9, 1),
(38, 38, 'System Settings', 'settings/update/1', 'fa', 12, 1),
(39, 42, 'System access previleges', 'access/give_menu', 'fa', 12, 1),
(40, 39, 'My profile', 'Employees/profile', 'fa', 1, 1),
(42, 42, 'Configure user roles', 'roles', 'fa', 5, 1),
(43, 42, 'Audit Trail', 'Activity_logger', 'fa', 15, 1),
(46, 27, 'Sell of foreign curr- cash to cash', 'Accounts/sell_foreign_cash_to_cash', 'fa', 4, 0),
(48, 38, 'Backups', 'Backup', 'fa', 16, 0),
(50, 41, 'Account Statement', 'transactions/statement', 'gdfd', 2, 0),
(52, 40, 'Payments report', 'reports/payments', 'fa', 2, 1),
(55, 33, 'Cashier-Vault Acceptance', 'Vault_cashier_pends/acceptance', 'fa', 1, 0),
(56, 33, 'Vault-Cashier Acceptance', 'Cashier_vault_pends/acceptance', 'fa', 2, 0),
(57, 38, 'Financial Year Holidays', 'Fyer_holiday', 'f', 9, 1),
(58, 43, 'Customer Information', 'Individual_customers', 'fa', 1, 1),
(61, 51, 'Loan Products', 'Loan_products', 'fa', 5, 1),
(62, 41, 'Loan calculator', 'Loan/calculator', 'fa', 1, 1),
(64, 50, 'Payment collections this week', 'reports/to_pay_week', 'fa', 2, 1),
(65, 40, 'PAR Report', 'reports/par_report', NULL, 3, 1),
(66, 50, 'Payment collections today', 'reports/to_pay_today', 'fa', 1, 1),
(67, 40, 'Arrears report', 'reports/arrears', NULL, 5, 1),
(68, 40, 'Portfolio report', 'loan/loan_report', NULL, 7, 1),
(69, 23, 'Add Loan', 'loan/loan_application', 'fa', 1, 1),
(70, 23, 'Approve Loan (First)', 'loan/initiated', 'fa', 2, 1),
(71, 23, 'Track loan', 'loan/track', 'fa', 5, 1),
(72, 23, 'Disburse loan', 'loan/approved', 'fa', 2, 1),
(74, 38, 'Charges', 'Charges', NULL, 4, 1),
(75, 43, 'My customers', 'individual_customers/my_customers', 'fa', 2, 0),
(76, 43, 'Approve', 'individual_customers/approve', 'fa', 2, 1),
(77, 43, 'Approved Customers', 'individual_customers/approved', '', 3, 1),
(78, 43, 'Rejected Customer', 'Individual_customers/rejected', 'fa', 4, 1),
(79, 40, 'Period analysis', 'Reports/period_analysis', 'fa', 7, 1),
(80, 40, 'Financial analysis', 'Reports/financial_analysis', 'fa', 8, 1),
(81, 44, 'Credit expenses', 'Transactions/expenses', 'fa', 1, 0),
(82, 45, 'Borrowed Funds', 'Borrowed', 'fa', 1, 0),
(83, 43, 'Add Customer', 'individual_customers/create', 'fa', 1, 1),
(84, 49, 'All groups', 'Groups/index', 'fa', 1, 0),
(85, 49, 'Create group', 'Groups/create', 'fa', 2, 0),
(86, 49, 'Approve Groups', 'Groups/approve', 'fa', 3, 0),
(87, 49, 'Group assigned amount', 'Group_assigned_amount', NULL, 4, 0),
(88, 49, 'Assign amount to group', 'Group_assigned_amount/create', NULL, 5, 0),
(89, 49, 'Approve group amount', 'Group_assigned_amount/approve', 'fa', 6, 0),
(91, 50, 'Payment collection this month', 'Reports/to_pay_month', 'adf', 3, 1),
(92, 46, 'Savings Products', 'Savings_products', NULL, 2, 1),
(93, 46, 'Add Savings Product', 'Savings_products/create', NULL, 1, 1),
(94, 46, 'Savings Accounts', 'Account/savings', NULL, 6, 1),
(95, 46, 'Create Savings Account', 'Account/create', NULL, 5, 1),
(96, 47, 'All internal accounts', 'Internal_accounts', 'fa', 1, 1),
(97, 47, 'Create Internal account', 'Internal_accounts/create', NULL, 2, 1),
(98, 47, 'Configure Vault/cashier', 'Internal_accounts/configure_accounts', NULL, 3, 1),
(99, 48, 'Account deposit/withdraw operations', 'Account/teller', NULL, 1, 1),
(100, 48, 'Cashier to vault', '#', NULL, 2, 1),
(101, 48, 'Vault to cashier', 'Tellering/move_to_teller', NULL, 3, 1),
(102, 48, 'Journal and transfers', 'Account/journal', NULL, 4, 1),
(103, 48, 'Account reconciliations', '#', NULL, 6, 0),
(104, 48, 'Vault to teller cash Approval', 'Vault_cashier_pends/acceptance', NULL, 3, 0),
(105, 49, 'Assign group member/Edit group', 'Groups/assign_members', 'fa', 2, 0),
(106, 52, 'Manual loan payment', 'loan/loan_repayment', 'fa', 1, 1),
(107, 52, 'Configure automatic repayment', 'global_config/update/1', NULL, 2, 0),
(108, 47, 'Configure income/expense account', 'Internal_accounts/configure_income', 'fa', 4, 1),
(109, 53, 'Create/Track', 'Customer_access/track', 'fa', 1, 1),
(110, 53, 'Approve Customer access', 'Customer_access/approve', NULL, 2, 1),
(111, 53, 'Approved list', 'Customer_access/approved', 'fa', 3, 1),
(112, 53, 'Rejected list', 'Customer_access/rejected', 'fa', 4, 1),
(113, 40, 'Show Dashboard statistics', '#', 'fa', 8, 0),
(114, 23, 'Write Off Loan', 'Loan/write_off', 'fa', 8, 1),
(115, 23, 'Approve Loan write-off', 'Loan/write_off_approve', NULL, 9, 1),
(118, 23, 'Recommend Loan', 'Loan/recommend', 'fa', 1, 1),
(120, 38, 'Manage Account types', 'Account_types', NULL, 4, 1),
(121, 42, 'Approve System user config', 'User_access/approve', 'fa', 4, 1),
(122, 38, 'SMS sending config', 'Sms_settings/update/1', 'fa', 3, 1),
(123, 43, 'Edit Customer', 'Individual_customers/edit', NULL, 3, 1),
(124, 43, 'Edit Customer KYC', 'Individual_customers/kyc_edit', 'fa', 3, 1),
(125, 43, 'Delete Customer', 'individual_customers/to_delete', NULL, 5, 1),
(126, 51, 'Add Loan product', 'Loan_products/create', 'fa', 1, 1),
(127, 51, 'Edit Loan Product', 'Loan_products/edit_list', NULL, 2, 1),
(128, 51, 'Delete Loan Product', 'Loan_products/delete_list', NULL, 3, 1),
(129, 46, 'Edit Savings Products', 'Savings_products/edit', NULL, 3, 0),
(130, 46, 'Delete Savings Product', 'Savings_products/to_delete', NULL, 4, 1),
(131, 46, 'Delete Savings Account', 'Account/to_delete', NULL, 7, 1),
(132, 46, 'Activate/Deactivate Savings Account', 'Account/to_deactivate', NULL, 8, 1),
(133, 23, 'Previous Loans docs', 'Previous_loans', 'fa', 15, 0),
(134, 40, 'Customer Report', 'Reports/customers_report', NULL, 7, 1),
(135, 38, 'Payment Methods/Banks', 'Payment_method', NULL, 5, 1),
(136, 40, 'CRB report', 'loan/exportExceView', 'fa', 2, 1),
(137, 23, 'Delete loan', 'loan/deleteloan_view', 'fa', 15, 0),
(138, 48, 'Track Transactions', 'Tellering/track_transactions_view', 'fa', 7, 1),
(139, 40, 'Client summary', 'Reports/client_summary', NULL, 8, 1),
(140, 40, 'Disbursed loans (LCTR)', 'loan/disbursed_loans', NULL, 9, 1),
(141, 54, 'Initiate ', 'loan/initiate_close_loan', 'fa', 1, 0),
(142, 54, 'Recomends ', 'loan/recomend_close_loan', 'fa', 2, 0),
(143, 54, 'Close ', 'loan/close_loan', 'fa', 3, 1),
(144, 55, 'Initiate ', 'loan/initiate_edit_loan', 'fa', 1, 0),
(145, 55, 'Recomends ', 'loan/recomend_edit_loan', 'fa', 2, 0),
(146, 55, 'Approved', 'loan/edit_loan', 'fa', 3, 0),
(147, 56, 'Import mass loans', 'loan/import_loan_mass_repayments', 'fa', 1, 0),
(148, 56, 'Process imported loans', 'loan/process_imported_loan_mass_repayments', 'fa', 2, 0),
(149, 56, 'Make mass repayments', 'loan/make_mass_mass_repayments', 'fa', 4, 0),
(150, 23, 'Unpaid loans list', 'loan/un_paid_loans', 'fa', 10, 1),
(151, 49, 'Group customers', 'groups/group_customers', 'fa', 5, 0),
(152, 40, 'Generated reports tracker', 'Reports/tracker', NULL, 13, 0),
(153, 40, 'My generated reports tracker', 'Reports/my_tracker', NULL, 14, 0),
(154, 56, 'Make mass deposits', 'loan/make_mass_mass_deposit', 'fa', 3, 0),
(155, 49, 'Add group customer', 'individual_customers/create_group', 'fa', 4, 0),
(156, 40, 'Collateral report', 'reports/collateral', NULL, 9, 0),
(157, 57, 'Initiate Delete payments', 'tellering/initiate_delete_payment', NULL, 1, 0),
(158, 57, 'Recomend Delete payments', 'tellering/recomend_delete_payment', NULL, 2, 0),
(159, 57, 'Approved Delete payments', 'tellering/Approved_delete_payment', NULL, 3, 0),
(160, 23, 'Recommend Loan Delete ', 'Loan/delete_request', NULL, 15, 0),
(161, 23, 'Approve Loan Delete', 'Loan/delete_request_approve', NULL, 16, 0),
(162, 23, 'Edit Loan', 'Loan/initiate_edit_loan', NULL, 18, 0),
(163, 23, 'Loan edit recommend', 'loan/edit_recommend', NULL, 19, 0),
(164, 23, 'Deleted Loans', 'Loan/deleted_loans', NULL, 20, 0),
(165, 23, 'Loan Edit Approve', 'loan/edit_approve', NULL, 17, 0),
(166, 52, 'Initiate Mass Repayemnt', 'Loan/initiate_mass_payment', NULL, 5, 0),
(167, 52, 'Track/commit mass repayments', 'Loan/view_mass_payment_requests', NULL, 6, 0),
(168, 56, 'All Shareholders', 'shareholders/index', 'fa', 1, 1),
(170, 56, 'Approve Shareholder', 'shareholders/approve', 'fa', 3, 1),
(171, 56, 'Approved Shareholder', 'shareholders/approved', 'fa', 4, 1),
(172, 55, 'All corporate customers', 'corporate_customers/index', 'fa', 1, 1),
(173, 55, 'Create corporate customers', 'corporate_customers/create', 'fa', 2, 1),
(174, 55, 'Approve corporate customers', 'corporate_customers/approve', 'fa', 3, 1),
(175, 55, 'Approved corporate_customers', 'corporate_customers/approved', 'fa', 4, 1),
(176, 23, 'Approve Loan (Second)', 'Loan/get_approved_first', 'fa', 2, 1),
(177, 23, 'Approve Loan (Third)', 'Loan/get_approved_second', 'fa', 2, 1);

-- --------------------------------------------------------

--
-- Table structure for table `payement_schedules`
--

CREATE TABLE `payement_schedules` (
  `customer` varchar(200) NOT NULL,
  `customer_type` enum('individual','group','institution') NOT NULL,
  `loan_id` varchar(200) NOT NULL,
  `payment_schedule` date NOT NULL,
  `payment_number` int(11) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `principal` decimal(18,2) NOT NULL,
  `interest` decimal(18,2) NOT NULL,
  `paid_amount` decimal(18,2) NOT NULL,
  `partial_paid` enum('YES','NO') NOT NULL DEFAULT 'NO',
  `loan_balance` decimal(18,2) NOT NULL,
  `status` enum('NOT PAID','PAID','PARTIAL PAID') NOT NULL DEFAULT 'NOT PAID',
  `loan_date` datetime NOT NULL DEFAULT current_timestamp(),
  `paid_date` datetime DEFAULT NULL,
  `marked_due` enum('NO','YES') NOT NULL DEFAULT 'NO',
  `marked_due_date` datetime DEFAULT NULL,
  `is_bullet_payment` int(11) NOT NULL DEFAULT 0,
  `id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `payement_schedules`
--

INSERT INTO `payement_schedules` (`customer`, `customer_type`, `loan_id`, `payment_schedule`, `payment_number`, `amount`, `principal`, `interest`, `paid_amount`, `partial_paid`, `loan_balance`, `status`, `loan_date`, `paid_date`, `marked_due`, `marked_due_date`, `is_bullet_payment`, `id`) VALUES
('2', 'individual', '1', '2030-12-05', 1, '1664000.00', '320000.00', '1344000.00', '0.00', 'NO', '320000.00', 'NOT PAID', '2025-12-05 00:00:00', NULL, 'NO', NULL, 1, 1),
('2', 'individual', '2', '2026-02-05', 1, '364800.00', '320000.00', '44800.00', '0.00', 'NO', '320000.00', 'NOT PAID', '2025-12-05 00:00:00', NULL, 'NO', NULL, 1, 2),
('12', 'individual', '3', '2026-02-05', 1, '222300.00', '195000.00', '27300.00', '0.00', 'NO', '195000.00', 'NOT PAID', '2025-12-05 00:00:00', NULL, 'NO', NULL, 1, 3),
('2', 'individual', '4', '2025-10-05', 1, '433200.00', '380000.00', '53200.00', '0.00', 'NO', '380000.00', 'NOT PAID', '2025-08-05 00:00:00', NULL, 'NO', NULL, 1, 4),
('14', 'individual', '5', '2025-07-21', 1, '114000.00', '100000.00', '14000.00', '0.00', 'NO', '100000.00', 'NOT PAID', '2025-05-21 00:00:00', NULL, 'NO', NULL, 1, 5),
('24', 'individual', '6', '2025-07-27', 1, '210800.00', '170000.00', '40800.00', '0.00', 'NO', '170000.00', 'NOT PAID', '2025-05-27 00:00:00', NULL, 'NO', NULL, 1, 6),
('38', 'individual', '7', '2025-08-05', 1, '136800.00', '120000.00', '16800.00', '0.00', 'NO', '120000.00', 'NOT PAID', '2025-06-05 00:00:00', NULL, 'NO', NULL, 1, 7),
('34', 'individual', '8', '2025-07-30', 1, '79800.00', '70000.00', '9800.00', '0.00', 'NO', '70000.00', 'NOT PAID', '2025-05-30 00:00:00', NULL, 'NO', NULL, 1, 8),
('31', 'individual', '9', '2025-07-07', 1, '228000.00', '200000.00', '28000.00', '0.00', 'NO', '200000.00', 'NOT PAID', '2025-05-07 00:00:00', NULL, 'NO', NULL, 1, 9),
('39', 'individual', '10', '2025-08-04', 1, '114000.00', '100000.00', '14000.00', '0.00', 'NO', '100000.00', 'NOT PAID', '2025-06-04 00:00:00', NULL, 'NO', NULL, 1, 10),
('43', 'individual', '11', '2025-08-06', 1, '660000.00', '600000.00', '60000.00', '0.00', 'NO', '600000.00', 'NOT PAID', '2025-06-06 00:00:00', NULL, 'NO', NULL, 1, 11),
('43', 'individual', '12', '2025-08-06', 1, '660000.00', '600000.00', '60000.00', '0.00', 'NO', '600000.00', 'NOT PAID', '2025-06-06 00:00:00', NULL, 'NO', NULL, 1, 12),
('29', 'individual', '13', '2025-07-29', 1, '49600.00', '40000.00', '9600.00', '0.00', 'NO', '40000.00', 'NOT PAID', '2025-05-29 00:00:00', NULL, 'NO', NULL, 1, 13),
('33', 'individual', '14', '2025-07-20', 1, '136850.00', '115000.00', '21850.00', '0.00', 'NO', '115000.00', 'NOT PAID', '2025-05-20 00:00:00', NULL, 'NO', NULL, 1, 14),
('48', 'individual', '15', '2025-08-11', 1, '77520.00', '68000.00', '9520.00', '0.00', 'NO', '68000.00', 'NOT PAID', '2025-06-11 00:00:00', NULL, 'NO', NULL, 1, 15),
('30', 'individual', '16', '2025-08-11', 1, '222300.00', '195000.00', '27300.00', '0.00', 'NO', '195000.00', 'NOT PAID', '2025-06-11 00:00:00', NULL, 'NO', NULL, 1, 16),
('1', 'individual', '17', '2025-09-11', 1, '1110100.00', '1000000.00', '110100.00', '0.00', 'NO', '1000000.00', 'NOT PAID', '2025-06-11 00:00:00', NULL, 'NO', NULL, 1, 17),
('35', 'individual', '18', '2025-06-02', 1, '421800.00', '370000.00', '51800.00', '0.00', 'NO', '370000.00', 'NOT PAID', '2025-04-02 00:00:00', NULL, 'NO', NULL, 1, 18),
('15', 'individual', '19', '2025-07-16', 1, '1210000.00', '1000000.00', '210000.00', '0.00', 'NO', '1000000.00', 'NOT PAID', '2025-04-16 00:00:00', NULL, 'NO', NULL, 1, 19),
('10', 'individual', '20', '2025-06-23', 1, '720000.00', '600000.00', '120000.00', '0.00', 'NO', '600000.00', 'NOT PAID', '2025-04-23 00:00:00', NULL, 'NO', NULL, 1, 20),
('21', 'individual', '21', '2025-05-09', 1, '46400.00', '40000.00', '6400.00', '0.00', 'NO', '40000.00', 'NOT PAID', '2025-04-09 00:00:00', NULL, 'NO', NULL, 1, 21),
('50', 'individual', '22', '2025-08-05', 1, '603200.00', '520000.00', '83200.00', '0.00', 'NO', '520000.00', 'NOT PAID', '2025-06-05 00:00:00', NULL, 'NO', NULL, 1, 22),
('50', 'individual', '23', '2025-06-15', 1, '232000.00', '200000.00', '32000.00', '0.00', 'NO', '200000.00', 'NOT PAID', '2025-04-15 00:00:00', NULL, 'NO', NULL, 1, 23),
('50', 'individual', '24', '2025-08-17', 1, '58000.00', '50000.00', '8000.00', '0.00', 'NO', '50000.00', 'NOT PAID', '2025-06-17 00:00:00', NULL, 'NO', NULL, 1, 24),
('33', 'individual', '25', '2024-11-25', 1, '288000.00', '240000.00', '48000.00', '0.00', 'NO', '240000.00', 'NOT PAID', '2024-09-25 00:00:00', NULL, 'NO', NULL, 1, 25),
('38', 'individual', '26', '2024-10-25', 1, '55000.00', '50000.00', '5000.00', '0.00', 'NO', '50000.00', 'NOT PAID', '2024-09-25 00:00:00', NULL, 'NO', NULL, 1, 26),
('31', 'individual', '27', '2025-01-11', 1, '744000.00', '600000.00', '144000.00', '0.00', 'NO', '600000.00', 'NOT PAID', '2024-10-11 00:00:00', NULL, 'NO', NULL, 1, 27),
('2', 'individual', '28', '2025-01-06', 1, '328320.00', '288000.00', '40320.00', '0.00', 'NO', '288000.00', 'NOT PAID', '2024-11-06 00:00:00', NULL, 'NO', NULL, 1, 28),
('33', 'individual', '29', '2025-01-11', 1, '288000.00', '240000.00', '48000.00', '0.00', 'NO', '240000.00', 'NOT PAID', '2024-11-11 00:00:00', NULL, 'NO', NULL, 1, 29),
('30', 'individual', '30', '2024-12-20', 1, '299600.00', '280000.00', '19600.00', '0.00', 'NO', '280000.00', 'NOT PAID', '2024-11-20 00:00:00', NULL, 'NO', NULL, 1, 30),
('2', 'individual', '31', '2025-01-20', 1, '432060.00', '379000.00', '53060.00', '0.00', 'NO', '379000.00', 'NOT PAID', '2024-11-20 00:00:00', NULL, 'NO', NULL, 1, 31),
('48', 'individual', '32', '2025-01-27', 1, '102600.00', '90000.00', '12600.00', '0.00', 'NO', '90000.00', 'NOT PAID', '2024-11-27 00:00:00', NULL, 'NO', NULL, 1, 32),
('31', 'individual', '33', '2025-03-05', 1, '484000.00', '400000.00', '84000.00', '0.00', 'NO', '400000.00', 'NOT PAID', '2024-12-05 00:00:00', NULL, 'NO', NULL, 1, 33),
('38', 'individual', '34', '2025-02-11', 1, '78000.00', '65000.00', '13000.00', '0.00', 'NO', '65000.00', 'NOT PAID', '2024-12-11 00:00:00', NULL, 'NO', NULL, 1, 34),
('2', 'individual', '35', '2025-03-14', 1, '396880.00', '328000.00', '68880.00', '0.00', 'NO', '328000.00', 'NOT PAID', '2024-12-14 00:00:00', NULL, 'NO', NULL, 1, 35),
('34', 'individual', '36', '2025-10-23', 1, '287100.00', '261000.00', '26100.00', '0.00', 'NO', '261000.00', 'NOT PAID', '2024-12-23 00:00:00', NULL, 'NO', NULL, 1, 36),
('2', 'individual', '37', '2024-12-28', 1, '375060.00', '329000.00', '46060.00', '0.00', 'NO', '329000.00', 'NOT PAID', '2024-10-28 00:00:00', NULL, 'NO', NULL, 1, 37),
('29', 'individual', '38', '2025-03-09', 1, '204000.00', '150000.00', '54000.00', '0.00', 'NO', '150000.00', 'NOT PAID', '2024-12-09 00:00:00', NULL, 'NO', NULL, 1, 38),
('30', 'individual', '39', '2025-02-26', 1, '205200.00', '180000.00', '25200.00', '0.00', 'NO', '180000.00', 'NOT PAID', '2024-12-26 00:00:00', NULL, 'NO', NULL, 1, 39),
('30', 'individual', '40', '2025-04-16', 1, '166980.00', '138000.00', '28980.00', '0.00', 'NO', '138000.00', 'NOT PAID', '2025-01-16 00:00:00', NULL, 'NO', NULL, 1, 40),
('31', 'individual', '41', '2025-04-17', 1, '363000.00', '300000.00', '63000.00', '0.00', 'NO', '300000.00', 'NOT PAID', '2025-01-17 00:00:00', NULL, 'NO', NULL, 1, 41),
('33', 'individual', '42', '2025-04-05', 1, '342000.00', '300000.00', '42000.00', '0.00', 'NO', '300000.00', 'NOT PAID', '2025-02-05 00:00:00', NULL, 'NO', NULL, 1, 42),
('33', 'individual', '43', '2025-04-05', 1, '440160.00', '366800.00', '73360.00', '0.00', 'NO', '366800.00', 'NOT PAID', '2025-02-05 00:00:00', NULL, 'NO', NULL, 1, 43),
('29', 'individual', '44', '2025-05-12', 1, '285600.00', '210000.00', '75600.00', '0.00', 'NO', '210000.00', 'NOT PAID', '2025-02-12 00:00:00', NULL, 'NO', NULL, 1, 44),
('2', 'individual', '45', '2025-05-13', 1, '529980.00', '438000.00', '91980.00', '0.00', 'NO', '438000.00', 'NOT PAID', '2025-02-13 00:00:00', NULL, 'NO', NULL, 1, 45),
('2', 'individual', '46', '2025-04-28', 1, '543780.00', '477000.00', '66780.00', '0.00', 'NO', '477000.00', 'NOT PAID', '2025-02-28 00:00:00', NULL, 'NO', NULL, 1, 46),
('33', 'individual', '47', '2025-03-07', 1, '403480.00', '366800.00', '36680.00', '0.00', 'NO', '366800.00', 'NOT PAID', '2025-02-07 00:00:00', NULL, 'NO', NULL, 1, 47),
('53', 'individual', '48', '2025-01-11', 1, '342000.00', '300000.00', '42000.00', '0.00', 'NO', '300000.00', 'NOT PAID', '2024-11-11 00:00:00', NULL, 'NO', NULL, 1, 48),
('54', 'individual', '49', '2025-01-03', 1, '424000.00', '400000.00', '24000.00', '0.00', 'NO', '400000.00', 'NOT PAID', '2024-12-03 00:00:00', NULL, 'NO', NULL, 1, 49),
('54', 'individual', '50', '2025-03-20', 1, '374500.00', '350000.00', '24500.00', '0.00', 'NO', '350000.00', 'NOT PAID', '2025-02-20 00:00:00', NULL, 'NO', NULL, 1, 50),
('53', 'individual', '51', '2025-03-26', 1, '374500.00', '350000.00', '24500.00', '0.00', 'NO', '350000.00', 'NOT PAID', '2025-02-26 00:00:00', NULL, 'NO', NULL, 1, 51),
('53', 'individual', '52', '0000-00-00', 1, '21175550000.00', '550000.00', '21175000000.00', '0.00', 'NO', '550000.00', 'NOT PAID', '2024-10-09 00:00:00', NULL, 'NO', NULL, 1, 52),
('53', 'individual', '53', '2024-12-09', 1, '627000.00', '550000.00', '77000.00', '0.00', 'NO', '550000.00', 'NOT PAID', '2024-10-09 00:00:00', NULL, 'NO', NULL, 1, 53),
('55', 'individual', '54', '2025-05-01', 1, '69345.00', '67000.00', '2345.00', '0.00', 'NO', '67000.00', 'NOT PAID', '2025-04-01 00:00:00', NULL, 'NO', NULL, 1, 54),
('55', 'individual', '55', '2025-05-01', 1, '69512.50', '67000.00', '2512.50', '0.00', 'NO', '67000.00', 'NOT PAID', '2025-04-01 00:00:00', NULL, 'NO', NULL, 1, 55),
('29', 'individual', '56', '2025-06-16', 1, '182400.00', '160000.00', '22400.00', '0.00', 'NO', '160000.00', 'NOT PAID', '2025-04-16 00:00:00', NULL, 'NO', NULL, 1, 56),
('2', 'individual', '57', '2025-08-20', 1, '163020.00', '143000.00', '20020.00', '0.00', 'NO', '143000.00', 'NOT PAID', '2025-06-20 00:00:00', NULL, 'NO', NULL, 1, 57),
('10', 'individual', '58', '2023-08-23', 1, '720000.00', '600000.00', '120000.00', '0.00', 'NO', '600000.00', 'NOT PAID', '2023-06-23 00:00:00', NULL, 'NO', NULL, 1, 58),
('56', 'individual', '59', '2025-08-27', 1, '218880.00', '192000.00', '26880.00', '0.00', 'NO', '192000.00', 'NOT PAID', '2025-06-27 00:00:00', NULL, 'NO', NULL, 1, 59),
('29', 'individual', '60', '2025-10-01', 1, '1161.44', '854.00', '307.44', '0.00', 'NO', '854.00', 'NOT PAID', '2025-07-01 00:00:00', NULL, 'NO', NULL, 1, 60),
('57', 'individual', '61', '2025-09-03', 1, '148200.00', '130000.00', '18200.00', '0.00', 'NO', '130000.00', 'NOT PAID', '2025-07-03 00:00:00', NULL, 'NO', NULL, 1, 61),
('30', 'individual', '62', '2025-09-02', 1, '224580.00', '197000.00', '27580.00', '0.00', 'NO', '197000.00', 'NOT PAID', '2025-07-02 00:00:00', NULL, 'NO', NULL, 1, 62),
('14', 'individual', '63', '2025-09-02', 1, '148200.00', '130000.00', '18200.00', '0.00', 'NO', '130000.00', 'NOT PAID', '2025-07-02 00:00:00', NULL, 'NO', NULL, 1, 63),
('1', 'individual', '64', '2025-10-11', 1, '731500.00', '550000.00', '181500.00', '0.00', 'NO', '550000.00', 'NOT PAID', '2025-07-11 00:00:00', NULL, 'NO', NULL, 1, 64),
('1', 'individual', '65', '2025-10-11', 1, '2806.30', '2110.00', '696.30', '0.00', 'NO', '2110.00', 'NOT PAID', '2025-07-11 00:00:00', NULL, 'NO', NULL, 1, 65),
('1', 'individual', '66', '2025-10-11', 1, '2.66', '2.00', '0.66', '0.00', 'NO', '2.00', 'NOT PAID', '2025-07-11 00:00:00', NULL, 'NO', NULL, 1, 66),
('1', 'individual', '67', '2025-10-11', 1, '2806300.00', '2110000.00', '696300.00', '0.00', 'NO', '2110000.00', 'NOT PAID', '2025-07-11 00:00:00', NULL, 'NO', NULL, 1, 67),
('12', 'individual', '68', '2025-09-15', 1, '88920.00', '78000.00', '10920.00', '0.00', 'NO', '78000.00', 'NOT PAID', '2025-07-15 00:00:00', NULL, 'NO', NULL, 1, 68);

-- --------------------------------------------------------

--
-- Table structure for table `payment_method`
--

CREATE TABLE `payment_method` (
  `payment_method` int(11) NOT NULL,
  `payment_method_name` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `payment_method_stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `payment_method`
--

INSERT INTO `payment_method` (`payment_method`, `payment_method_name`, `description`, `payment_method_stamp`) VALUES
(1, 'Mpamba', '', '2023-02-21 17:33:29'),
(2, 'Airtel Money', '', '2023-02-21 17:33:29'),
(3, 'FDH bank', 'Repayment account', '2023-02-21 17:33:29'),
(4, 'National Bank', '', '2023-02-21 17:33:29'),
(5, 'NBS', '', '2023-02-21 17:33:53'),
(6, 'Standard Bank', 'Disbursement Account', '2023-02-21 17:33:53'),
(7, 'Reserve Bank', 'gfjhgj', '2023-02-23 15:23:38');

-- --------------------------------------------------------

--
-- Table structure for table `previous_loans`
--

CREATE TABLE `previous_loans` (
  `p_lid` int(11) NOT NULL,
  `customer_id` varchar(200) NOT NULL,
  `description` text NOT NULL,
  `loan_effective_date` date NOT NULL,
  `loan_end_date` date NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `amount_paid` decimal(8,2) NOT NULL,
  `status` enum('Paid','Written off','Not Paid','Partial Paid') NOT NULL DEFAULT 'Paid',
  `added_by` int(11) NOT NULL,
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `proofofidentity`
--

CREATE TABLE `proofofidentity` (
  `id` int(11) NOT NULL,
  `IDType` enum('DRIVING_LISENCE','NATIONAL_IDENTITY_CARD','PASSPORT','WORK_PERMIT','VOTER_REGISTRATION','PUBLIC_STATE_OFFICIAL_LETTER','DEFAULT') NOT NULL,
  `IDNumber` varchar(40) NOT NULL,
  `IssueDate` date NOT NULL,
  `ExpiryDate` date DEFAULT NULL,
  `DocImageURL` varchar(250) NOT NULL,
  `Stamp` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `ClientId` int(11) NOT NULL,
  `photograph` varchar(200) NOT NULL,
  `signature` varchar(200) NOT NULL,
  `Id_back` varchar(200) NOT NULL,
  `id_front` varchar(200) NOT NULL,
  `server_date` datetime NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `reports`
--

CREATE TABLE `reports` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user` varchar(200) NOT NULL,
  `report_type` varchar(200) NOT NULL,
  `download_link` varchar(200) DEFAULT NULL,
  `status` enum('in progress','completed') NOT NULL DEFAULT 'in progress',
  `generated_time` datetime NOT NULL DEFAULT current_timestamp(),
  `completed_time` datetime DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `rescheduled_payments`
--

CREATE TABLE `rescheduled_payments` (
  `rescheduled_payments_id` int(11) NOT NULL,
  `loan_id` varchar(200) NOT NULL,
  `customer_id` varchar(200) NOT NULL,
  `customer_type` varchar(200) NOT NULL,
  `payment_number` int(11) NOT NULL,
  `payment_amount` decimal(18,2) NOT NULL,
  `payment_date` date NOT NULL,
  `pay_status` enum('PAID','UNPAID') NOT NULL DEFAULT 'UNPAID',
  `partial_paid` enum('YES','NO') NOT NULL DEFAULT 'NO',
  `paid_amount` decimal(18,2) NOT NULL,
  `p_stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `RoleName` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `RoleName`) VALUES
(1, 'SUPER ADMIN'),
(26, 'Admin Officer'),
(27, 'Finance Manager'),
(28, 'Managing Director'),
(29, 'Relationship Manager'),
(30, 'COO'),
(32, 'Accountant'),
(33, 'Data Entry intern'),
(36, 'Risk and Rehabilitation Senior Officer'),
(37, 'Risk Officer'),
(38, 'Relationship Supervisor'),
(39, 'ICT Supervisor'),
(40, 'Cashier'),
(41, 'Chief Services Officer'),
(42, 'Credit Manager'),
(43, 'Credit Committee'),
(44, 'Chief Strategy Officer'),
(45, 'Chief Executive Officer'),
(46, 'Chief Financial Officer');

-- --------------------------------------------------------

--
-- Table structure for table `savings_charges`
--

CREATE TABLE `savings_charges` (
  `savings_charges_id` int(11) NOT NULL,
  `fee_name` int(11) NOT NULL,
  `charge_type` enum('Fixed','Variable') NOT NULL,
  `fixed_amount` decimal(10,0) NOT NULL,
  `variable_percentage` int(11) NOT NULL,
  `applied_on` text NOT NULL COMMENT 'Withdraw,\r\ntransfer out\r\nbank fees\r\ncommission',
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `min_amount` decimal(18,2) DEFAULT NULL,
  `max_amount` decimal(18,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `savings_products`
--

CREATE TABLE `savings_products` (
  `saviings_product_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `added_by` varchar(200) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT current_timestamp(),
  `interest_per_anum` int(11) NOT NULL,
  `interest_method` enum('prorata','last savings balance') NOT NULL,
  `interest_posting_frequency` enum('Every 1 month','Every 2 month','Every 3 month','Every 4 month','Every 6 month','Every 12 month') NOT NULL,
  `when_to_post` enum('day 1','day 2','day 3','day 4','day 5','day 6','day 7','day 8','day 9','day 10','day 11','day 12','day 13','day 14','day 15','day 16','day 17','day 18','day 19','day 20','day 21','day 22','day 23','day 24','day 25','day 26','day 27','day 28','day 29') NOT NULL,
  `minimum_balance_for_interest` decimal(18,2) NOT NULL,
  `minimum_balance_withdrawal` decimal(18,2) NOT NULL,
  `overdraft` enum('Yes','No') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `savings_products`
--

INSERT INTO `savings_products` (`saviings_product_id`, `name`, `added_by`, `date_added`, `interest_per_anum`, `interest_method`, `interest_posting_frequency`, `when_to_post`, `minimum_balance_for_interest`, `minimum_balance_withdrawal`, `overdraft`) VALUES
(1, 'Student Security savings', '7', '2022-03-31 01:07:37', 10, 'last savings balance', 'Every 1 month', 'day 6', '10000.00', '10000.00', 'No'),
(2, 'General Savings', '7', '2022-03-31 01:15:28', 20, 'last savings balance', 'Every 2 month', 'day 15', '10000.00', '10000.00', 'Yes');

-- --------------------------------------------------------

--
-- Table structure for table `sdate_logger`
--

CREATE TABLE `sdate_logger` (
  `id` int(11) NOT NULL,
  `table_name` enum('accounts','access','account_type','activity_logger','authorisation','authorised_signitories','banks','branches','chart_of_accounts','corporate_customers','currency','denominations','employees','fx_transaction_logger','geo_countries','global_sessions','individual_customers','inward_clearing','menu','menuitems','next_of_kin','office_account','office_journal','outward_clearing','proofofindentity','roles','sytem_date','tellering','transaction','user_access') NOT NULL,
  `crud` enum('delete','create','update','read') NOT NULL,
  `server_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `system_date` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `sdate_logger`
--

INSERT INTO `sdate_logger` (`id`, `table_name`, `crud`, `server_time`, `system_date`) VALUES
(1, 'banks', 'create', '2021-05-26 22:54:50', '2021-05-06'),
(2, 'banks', 'create', '2021-05-27 00:46:07', '2021-05-06');

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `settings_id` int(11) NOT NULL,
  `logo` varchar(200) NOT NULL,
  `address` text NOT NULL,
  `phone_number` varchar(200) NOT NULL,
  `company_name` varchar(200) NOT NULL,
  `company_email` varchar(200) NOT NULL,
  `currency` varchar(50) NOT NULL,
  `time_zone` varchar(200) NOT NULL,
  `tax` double NOT NULL,
  `defaulter_durations` int(11) NOT NULL,
  `protocal` varchar(200) DEFAULT NULL,
  `email_host` varchar(200) DEFAULT NULL,
  `email_port` varchar(200) DEFAULT NULL,
  `email_user` varchar(200) DEFAULT NULL,
  `email_pass` varchar(200) DEFAULT NULL,
  `reg_fee_new` decimal(18,2) NOT NULL,
  `reg_fee_old` decimal(18,2) NOT NULL,
  `require_reg_fee` enum('Yes','No') NOT NULL DEFAULT 'Yes',
  `arrears_grace` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`settings_id`, `logo`, `address`, `phone_number`, `company_name`, `company_email`, `currency`, `time_zone`, `tax`, `defaulter_durations`, `protocal`, `email_host`, `email_port`, `email_user`, `email_pass`, `reg_fee_new`, `reg_fee_old`, `require_reg_fee`, `arrears_grace`) VALUES
(1, '6749cfd3aecf73223.png', '<p>Fundit Capital Solutions Limited</p>\r\n\r\n<p>17 Kanfisa Sreet Riverside Extension</p>\r\n\r\n<p>Kitwe</p>\r\n\r\n<p>Zambia</p>', '0988799844/0881081705', 'FUNDIT Capital Solutions Limited', '<EMAIL>', 'ZMW', 'Lusaka/harare', 16.5, 90, 'smtp', 'infocustech-mw.com', '465', '<EMAIL>', 'Mwaii@1992', '2000.00', '0.00', 'Yes', 5);

-- --------------------------------------------------------

--
-- Table structure for table `shareholders`
--

CREATE TABLE `shareholders` (
  `id` int(11) NOT NULL,
  `title` varchar(20) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `gender` enum('male','female') NOT NULL,
  `approval_status` enum('Initiated','Approved','Not Approved','Rejected') NOT NULL DEFAULT 'Initiated',
  `nationality` varchar(100) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `email_address` varchar(100) NOT NULL,
  `full_address` text DEFAULT NULL,
  `added_by` int(11) NOT NULL,
  `idtype` varchar(50) DEFAULT NULL,
  `idfile` varchar(100) DEFAULT NULL,
  `CreatedOn` datetime NOT NULL DEFAULT current_timestamp(),
  `LastUpdatedOn` datetime DEFAULT NULL ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `shareholders`
--

INSERT INTO `shareholders` (`id`, `title`, `first_name`, `last_name`, `gender`, `approval_status`, `nationality`, `phone_number`, `email_address`, `full_address`, `added_by`, `idtype`, `idfile`, `CreatedOn`, `LastUpdatedOn`) VALUES
(1, 'Mr', 'Misheck ', 'Kamuloni', 'male', 'Initiated', '454', '+265994099461', '<EMAIL>', 'Private Bag B444, Lilongwe', 72, 'NATIONAL_IDENTITY_CARD', '235POS_Quote1.pdf', '2025-05-05 14:33:55', NULL),
(2, 'Mr', 'Philip', 'Bwembya', 'male', 'Initiated', '716', '09594847557', 'kamulonim@gmail', 'Address here', 72, 'PASSPORT', '102Desing_and_Build_Invoice1.png', '2025-05-05 14:40:16', NULL),
(3, 'Mr', 'Mai', 'Bambo', 'male', 'Initiated', '716', '099409482772', '<EMAIL>', 'AddrKuyesaess here', 72, 'PASSPORT', NULL, '2025-05-26 16:10:14', NULL),
(4, 'Mr', 'Richard', 'Phiri', 'male', 'Initiated', '894', '0966462590', '<EMAIL>', 'Ad3rd Floor, ECL Office Park, Block 2dress here', 139, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-27 09:48:35', NULL),
(5, 'Mr', 'Lazarus', 'Museka', 'male', 'Initiated', '894', '0966 852400', '<EMAIL>', 'Address here', 139, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-27 13:10:55', NULL),
(6, 'Mrs', 'Jane', 'Chilolo', 'female', 'Initiated', '894', '0955 852400         ', '<EMAIL>', 'Address here', 139, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-27 13:10:55', NULL),
(7, 'Mr', 'CHITI', 'CHINGAMBO', 'male', 'Initiated', '894', '0966908888', '<EMAIL>', '7 , PAMO, AVENUE , PARKLANDS ', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 10:21:40', NULL),
(8, 'Mr', 'DAVIS', 'KANYUMBU', 'male', 'Initiated', '894', '0966950916', '<EMAIL>', 'KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 10:32:29', NULL),
(9, 'Mr', 'DINGISWAYO', 'ZULU', 'male', 'Initiated', '894', '0966750694', '<EMAIL>', 'SOLWEZI', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 11:09:57', NULL),
(10, 'Mr', 'KENNEDY', 'CHANSA', 'male', 'Initiated', '894', '096216885', '<EMAIL>', 'PLOT NO 6470 , PHASE IV RIVERSIDE , KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 13:45:08', NULL),
(11, 'Mr', 'HENRY', 'NGULUBE', 'male', 'Initiated', '894', '0975791923', '<EMAIL>', 'PLOT NO 608 M/N , KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 14:00:40', NULL),
(12, 'Mr', 'MEDWIN .C.', 'MWEWA', 'male', 'Initiated', '894', '0966928388', '<EMAIL>', '3192 , CHIMWEMWE , KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-05-28 14:09:12', NULL),
(13, 'Mr', 'BONIFACE', 'SOKONI', 'male', 'Initiated', '894', '0963407289', '<EMAIL>', 'KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-02 09:59:16', NULL),
(14, 'Mr', 'Moses', 'Munyakula', 'male', 'Initiated', '894', '0971783447', '<EMAIL>', 'Plot 4967 Lyoneno Road Heavy Industrial AreaAddress here', 139, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-04 10:18:26', NULL),
(15, 'Mr', 'Moses', 'Munyakula', 'male', 'Initiated', '894', '0971783447', '<EMAIL>', 'Plot 4967 Lyoneno Road Heavy Industrial AreaAddress here', 139, 'NATIONAL_IDENTITY_CARD', '812CERTIFICATE_OF_INCORPORATION-1BRASSCO1.pdf', '2025-06-04 10:19:01', NULL),
(16, 'Mr', 'Elijah ', 'Mwewa', 'male', 'Initiated', '894', '0966364058', '<EMAIL>', 'A1st Floor Room 10 Solidarity House,Oxford Road,Town Centre', 139, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-09 16:26:54', NULL),
(17, 'Miss', 'Mama', 'Bwalya', 'female', 'Initiated', '894', '0966019658', '<EMAIL>', '314 Section 26 , Mpatamato , Luanshya ', 146, 'NATIONAL_IDENTITY_CARD', '255_reports_complianceReports_General_TCC_(10)1.pdf', '2025-06-11 09:17:32', NULL),
(18, 'Miss', 'Ngandwe ', 'Kanungwe', 'female', 'Initiated', '894', '0955019658', '<EMAIL>', 'T84 Wusakile , Kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-11 09:17:32', NULL),
(19, 'Mr', 'BRIGHT', 'CHANDA', 'male', 'Initiated', '894', '0966203033', '<EMAIL>', '3074, Copper Street , Nkana East , Kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-17 09:23:40', NULL),
(20, 'Mr', 'Moffat', 'Banda', 'male', 'Initiated', '894', '0955839562', '<EMAIL>', 'Kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 11:14:06', NULL),
(21, 'Mr', 'Lorrane ', 'Banda', 'female', 'Initiated', '894', '0955839526', '<EMAIL>', 'Kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 11:14:06', NULL),
(22, 'Mr', 'Andrew ', 'Mulenga ', 'male', 'Initiated', '894', 'N/A ', '<EMAIL>', 'Mushitala Area ', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 11:47:00', NULL),
(23, 'Miss', 'Thelma', 'Mulenga', 'male', 'Initiated', '894', 'N/A ', '<EMAIL>', 'Malashitala Area ', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 11:47:00', NULL),
(24, 'Mr', 'Pelmel', 'Banda', 'male', 'Initiated', '894', '0966284832', '<EMAIL>', 'Kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 14:19:59', NULL),
(25, 'Mr', 'Andrew ', 'Mapenzi', 'male', 'Initiated', '894', '0967838800', '<EMAIL>', 'Kitwe ', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-19 14:19:59', NULL),
(26, 'Mr', 'Steven', 'Kashembe', 'male', 'Initiated', '894', '0955783836', '<EMAIL>', 'kitwe', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-27 08:57:09', NULL),
(27, 'Mr', 'Innocent ', 'Chumba', 'male', 'Initiated', '894', '0977783836', '<EMAIL>', 'KITWE', 146, 'NATIONAL_IDENTITY_CARD', NULL, '2025-06-27 08:57:09', NULL),
(28, 'Mr', 'Themba', 'Mwale ', 'male', 'Initiated', '894', '0969848015', '<EMAIL>', '34, 10th Street, Nchanga South, Chingola ', 146, 'NATIONAL_IDENTITY_CARD', '857InFocus_PACRA_Printout1.pdf', '2025-07-03 08:53:37', NULL),
(29, 'Miss', 'Cindy Mukuka ', 'Mpundu', 'female', 'Initiated', '894', '0969848015', '<EMAIL>', '34 , 10th Street Nchanga South Chingola ', 146, 'NATIONAL_IDENTITY_CARD', '857InFocus_PACRA_Printout2.pdf', '2025-07-03 08:53:37', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `smeloansnotsure`
--

CREATE TABLE `smeloansnotsure` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `Salutation` varchar(4) DEFAULT NULL,
  `Surname` varchar(11) DEFAULT NULL,
  `FirstName` varchar(11) DEFAULT NULL,
  `MiddleName` varchar(13) DEFAULT NULL,
  `MaidenName` varchar(7) DEFAULT NULL,
  `Gender` varchar(6) DEFAULT NULL,
  `MaritalStatus` varchar(8) DEFAULT NULL,
  `NoofDependents` varchar(2) DEFAULT NULL,
  `DateofBirth` int(11) DEFAULT NULL,
  `IDType` varchar(16) DEFAULT NULL,
  `Idnumber` varchar(9) DEFAULT NULL,
  `Nationality` varchar(8) DEFAULT NULL,
  `Village` varchar(15) DEFAULT NULL,
  ` TA` varchar(14) DEFAULT NULL,
  `HomeDistrict` varchar(10) DEFAULT NULL,
  `ResidentPermitNo` varchar(27) DEFAULT NULL,
  `PhoneNo` varchar(22) DEFAULT NULL,
  `PostalAddress` varchar(64) DEFAULT NULL,
  `EmailAddress` varchar(33) DEFAULT NULL,
  `ResidentialAddress` varchar(27) DEFAULT NULL,
  `ResidentialDistrict` varchar(8) DEFAULT NULL,
  `ProfessionOccupation` varchar(31) DEFAULT NULL,
  `EmployerName` varchar(42) DEFAULT NULL,
  `EmployerAddress` varchar(64) DEFAULT NULL,
  `EmployerPhoneNo` varchar(19) DEFAULT NULL,
  `EmploymentDate` varchar(8) DEFAULT NULL,
  `LOANPRODUCT` varchar(13) DEFAULT NULL,
  `loan_product_id` int(11) NOT NULL DEFAULT 3,
  `LoanReferenceNo` varchar(14) DEFAULT NULL,
  `Currency` varchar(3) DEFAULT NULL,
  `ApprovedAmount` varchar(14) DEFAULT NULL,
  `ApprovedAmountMWK` varchar(14) DEFAULT NULL,
  `DisbursedAmount` varchar(14) DEFAULT NULL,
  `DisbursedAmountMWK` varchar(14) DEFAULT NULL,
  `DisbursementDate` int(11) DEFAULT NULL,
  `MaturityDate` int(11) DEFAULT NULL,
  `BorrowerType` varchar(8) DEFAULT NULL,
  `ProductType` varchar(15) DEFAULT NULL,
  `PaymentPeriod` int(11) DEFAULT NULL,
  `Collateral Status` varchar(8) DEFAULT NULL,
  `ReserveBankClassification` varchar(8) DEFAULT NULL,
  `Account Status` varchar(21) DEFAULT NULL,
  `Account Status Change Date` varchar(8) DEFAULT NULL,
  `ScheduledRepaymentAmount` varchar(12) DEFAULT NULL,
  `ScheduledRepaymentAmountMWK` varchar(12) DEFAULT NULL,
  `TotalAmountPaidToDate` varchar(14) DEFAULT NULL,
  `TotalAmountPaidToDateMWK` varchar(14) DEFAULT NULL,
  `FirstPaymentDate` decimal(10,2) DEFAULT NULL,
  `Last Payment Date` int(11) DEFAULT NULL,
  `LastPaymentAmountMWK` varchar(14) DEFAULT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sms_settings`
--

CREATE TABLE `sms_settings` (
  `id` int(11) NOT NULL,
  `customer_approval` enum('Yes','No') NOT NULL DEFAULT 'No',
  `group_approval` enum('Yes','No') NOT NULL DEFAULT 'No',
  `loan_disbursement` enum('Yes','No') NOT NULL DEFAULT 'No',
  `before_notice` enum('Yes','No') NOT NULL DEFAULT 'No',
  `before_notice_period` int(11) NOT NULL DEFAULT 10,
  `arrears` enum('Yes','No') NOT NULL DEFAULT 'No',
  `arrears_age` int(11) NOT NULL,
  `customer_app_pass_recovery` enum('Yes','No') NOT NULL DEFAULT 'Yes',
  `customer_birthday_notify` enum('Yes','No') NOT NULL DEFAULT 'No',
  `loan_payment_notification` enum('Yes','No') NOT NULL DEFAULT 'No',
  `deposit_withdraw_notification` enum('Yes','No') NOT NULL DEFAULT 'No'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

--
-- Dumping data for table `sms_settings`
--

INSERT INTO `sms_settings` (`id`, `customer_approval`, `group_approval`, `loan_disbursement`, `before_notice`, `before_notice_period`, `arrears`, `arrears_age`, `customer_app_pass_recovery`, `customer_birthday_notify`, `loan_payment_notification`, `deposit_withdraw_notification`) VALUES
(1, 'No', 'No', 'No', 'No', 10, 'No', 10, 'Yes', 'No', 'No', 'No');

-- --------------------------------------------------------

--
-- Table structure for table `sytem_date`
--

CREATE TABLE `sytem_date` (
  `id` int(11) NOT NULL,
  `s_date` date NOT NULL,
  `is_active` enum('Yes','No') NOT NULL DEFAULT 'No'
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `sytem_date`
--

INSERT INTO `sytem_date` (`id`, `s_date`, `is_active`) VALUES
(1, '2021-05-26', 'No'),
(2, '2021-05-27', 'No'),
(3, '2021-05-28', 'No'),
(4, '2021-05-29', 'No'),
(5, '2021-05-30', 'No'),
(6, '2021-05-31', 'No'),
(8, '2021-08-20', 'No'),
(9, '2021-08-27', 'No'),
(11, '2021-08-28', 'No'),
(12, '2021-08-30', 'No'),
(13, '2021-09-01', 'No'),
(14, '2021-09-04', 'Yes');

-- --------------------------------------------------------

--
-- Table structure for table `ta`
--

CREATE TABLE `ta` (
  `ta_id` int(11) NOT NULL,
  `district_id` int(11) NOT NULL,
  `ta_name` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Dumping data for table `ta`
--

INSERT INTO `ta` (`ta_id`, `district_id`, `ta_name`) VALUES
(1, 3, 'STA Bulambya Songwe'),
(2, 3, 'TA Mwabulambya'),
(3, 3, 'TA Mwenemisuku'),
(4, 3, 'STA Lwangwa'),
(5, 3, 'TA Mwenewenya'),
(6, 3, 'TA Kameme'),
(7, 3, 'TA Nthalire'),
(8, 3, 'STA Wavikaza'),
(9, 3, 'Nyika Ntational Park-Chitipa'),
(10, 3, 'STA Nthengatenga'),
(11, 3, 'Chitipa Boma'),
(12, 9, 'TA Kilupula'),
(13, 9, 'TA Mwakaboko'),
(14, 9, 'TA Kyungu'),
(15, 9, 'TA Wasambo'),
(16, 20, 'TA Kabunduli'),
(17, 9, 'TA Mwirang\'ombe'),
(18, 9, 'Nyika National Park-Karonga'),
(19, 9, 'Karonga Town'),
(20, 20, 'TA Fukamapiri'),
(21, 20, 'TA Malenga Mzoma'),
(22, 20, 'TA Malanda'),
(23, 20, 'TA Zilakoma'),
(24, 20, 'TA M\'bwana'),
(25, 20, 'TA Mankhambira'),
(26, 20, 'TA Fukamalaza'),
(27, 20, 'TA Mkumbira'),
(28, 20, 'STA Nyaluwanga'),
(29, 20, 'STA Kondowe'),
(30, 20, 'TA Timbiri'),
(31, 20, 'TA Boghoyo'),
(32, 20, 'Nkhatabay Boma'),
(33, 22, 'TA Chikulamayembe'),
(34, 22, 'TA Mwamlowe'),
(35, 22, 'TA Mwahenga'),
(36, 22, 'TA Mwalweni'),
(37, 22, 'STA Kachulu'),
(38, 22, 'STA Chapinduka'),
(39, 22, 'TA Mwankhunikira'),
(40, 22, 'TA Katumbi'),
(41, 22, 'STA Zolokere'),
(42, 22, 'STA Njikula'),
(43, 22, 'STA Chisovya'),
(44, 22, 'Nyika National Park'),
(45, 22, 'Vwaza Marsh Reserve'),
(46, 22, 'Rumphi Boma'),
(47, 27, 'TA M\'Mbelwa'),
(48, 27, 'TA Mtwalo'),
(49, 27, 'TA Kampingo Sibande'),
(50, 27, 'TA Jaravikuba Munthali'),
(51, 27, 'TA Chindi'),
(52, 27, 'TA Mzikubola'),
(53, 27, 'TA Mabulabo'),
(54, 27, 'TA Khosolo Gwaza Jere'),
(55, 27, 'TA Mpherembe'),
(56, 27, 'Vwaza Marsh Reserve'),
(57, 27, 'Mzimba Boma'),
(58, 27, 'TA Mzukuzuku'),
(59, 27, 'STA Levi Jere'),
(60, 26, 'Chibavi East Ward'),
(61, 26, 'Chibavi West Ward'),
(62, 26, 'Chiputula Ward'),
(63, 26, 'Masasa Ward'),
(64, 26, 'Chibanja Ward'),
(65, 26, 'Jombo - Kaning\'ina Ward'),
(66, 26, 'Katawa Ward'),
(67, 26, 'Luwinga Ward'),
(68, 26, 'Mchengautuwa East Ward'),
(69, 26, 'Mchengautuwa West Ward'),
(70, 26, 'Msongwe Ward'),
(71, 26, 'Mzilawaingwe Ward'),
(72, 26, 'Nkhorongo - Lupaso Ward'),
(73, 26, 'Zolozolo West Ward'),
(74, 26, 'Zolozolo East Ward'),
(75, 8, 'TA Kaluluma'),
(76, 8, 'TA Chisemphere'),
(77, 8, 'TA Simlemba'),
(78, 8, 'STA M\'nyanja'),
(79, 8, 'TA Kawamba'),
(80, 8, 'STA Chisikwa'),
(81, 8, 'TA Kaomba'),
(82, 8, 'STA Nthunduwala'),
(83, 8, 'TA Njombwa'),
(84, 8, 'TA Chulu'),
(85, 8, 'TA Chilowamatambe'),
(86, 8, 'STA Chambwe'),
(87, 8, 'STA Chisinga'),
(88, 8, 'STA Mphomwa'),
(89, 8, 'STA Chaima'),
(90, 8, 'TA Kaphaizi'),
(91, 8, 'STA Mangwazu'),
(92, 8, 'STA Mawawa'),
(93, 8, 'TA Lukwa'),
(94, 8, 'Kasungu Boma'),
(95, 8, 'TA Kapelula'),
(96, 8, 'STA Kapichira'),
(97, 8, 'TA Santhe'),
(98, 8, 'TA Chidzuma'),
(99, 8, 'TA Wimbe'),
(100, 8, 'STA Mdunga'),
(101, 8, 'TA Mwase'),
(102, 8, 'Kasungu National Park'),
(103, 19, 'TA Kanyenda'),
(104, 19, 'TA Kafuzila'),
(105, 19, 'STA Kalimanjira'),
(106, 19, 'TA Mphonde'),
(107, 19, 'TA Malenga Chanzi'),
(108, 19, 'TA Mwadzama'),
(109, 19, 'Nkhotakota Game Reserve'),
(110, 19, 'TA Mwansambo'),
(111, 19, 'Nkhotakota Boma'),
(112, 18, 'TA Kasakula'),
(113, 18, 'TA Chikho'),
(114, 18, 'TA Kalumo'),
(115, 18, 'TA Nthondo'),
(116, 18, 'TA Chilooko'),
(117, 18, 'TA Vuso Jere'),
(118, 18, 'TA Malenga'),
(119, 18, 'Ntchisi Boma'),
(120, 7, 'TA Dzoole'),
(121, 7, 'TA Chakhaza'),
(122, 7, 'TA Mkukula'),
(123, 7, 'TA Msakambewa'),
(124, 7, 'TA Kayembe'),
(125, 7, 'TA Chiwere'),
(126, 7, 'TA Mponela'),
(127, 7, 'Dowa Boma'),
(128, 7, 'Mponela Urban'),
(129, 23, 'TA Maganga'),
(130, 23, 'TA Karonga'),
(131, 23, 'TA Pemba'),
(132, 23, 'TA Ndindi'),
(133, 23, 'TA Kambalame'),
(134, 23, 'TA Kambwiri'),
(135, 23, 'TA Khombedza'),
(136, 23, 'TA Kuluunda'),
(137, 23, 'TA Msosa'),
(138, 23, 'TA Mwanza'),
(139, 10, 'TA Chadza'),
(140, 23, 'Lake Malawi National Park'),
(141, 23, 'Salima Town'),
(142, 23, 'Chipoka Urban'),
(143, 10, 'TA Kalolo'),
(144, 10, 'TA Masula'),
(145, 10, 'TA Masumbankhunda'),
(146, 10, 'TA Chiseka'),
(147, 10, 'TA Mazengera'),
(148, 10, 'STA Chitekwele'),
(149, 10, 'TA Khongoni'),
(150, 10, 'TA Chimutu'),
(151, 10, 'TA Chitukula'),
(152, 10, 'TA Mtema'),
(153, 10, 'TA Kalumba'),
(154, 10, 'TA Malili'),
(155, 10, 'TA Kabudula'),
(156, 10, 'TA Kalumbu'),
(157, 10, 'TA Tsabango'),
(158, 10, 'TA Njewa'),
(159, 15, 'TA Mlonyeni'),
(160, 10, 'STA Mbang\'ombe'),
(161, 15, 'TA Mavwere'),
(162, 15, 'TA Zulu'),
(163, 15, 'TA Simphasi'),
(164, 15, 'TA Mduwa'),
(165, 15, 'STA Nyoka'),
(166, 15, 'TA Mkanda'),
(167, 15, 'STA Pitala'),
(168, 15, 'STA Gumba'),
(169, 15, 'STA Kapunula'),
(170, 15, 'TA Kazyozyo'),
(171, 15, 'TA Dambe'),
(172, 15, 'TA Kapondo'),
(173, 15, 'Mchinji Boma'),
(174, 6, 'TA Kaphuka'),
(175, 6, 'TA Kachere'),
(176, 6, 'TA Chilikumwendo'),
(177, 6, 'TA Tambala'),
(178, 6, 'TA Chauma'),
(179, 6, 'TA Kasumbu'),
(180, 6, 'TA Kachindamoto'),
(181, 6, 'TA Kamenya Gwaza'),
(182, 6, 'Dedza Boma'),
(183, 17, 'TA Phambala'),
(184, 17, 'STA Tsikulamowa'),
(185, 17, 'TA Mpando'),
(186, 17, 'TA Kwataine'),
(187, 17, 'STA Mkutumula'),
(188, 17, 'TA Makwangwala'),
(189, 17, 'TA Njolomole'),
(190, 17, 'TA Goodson Ganya'),
(191, 17, 'TA Masasa'),
(192, 17, 'TA Champiti'),
(193, 17, 'TA Chakhumbira'),
(194, 17, 'Ntcheu Boma'),
(195, 10, 'Area 1'),
(196, 10, 'Area 2'),
(197, 10, 'Area 3'),
(198, 10, 'Area 6'),
(199, 10, 'Area 4'),
(200, 10, 'Area 5'),
(201, 10, 'Area 7'),
(202, 10, 'Area 8'),
(203, 10, 'Area 11'),
(204, 10, 'Area 9'),
(205, 10, 'Area 10'),
(206, 10, 'Area 12'),
(207, 10, 'Area 14'),
(208, 10, 'Area 13'),
(209, 10, 'Area 15'),
(210, 10, 'Area 16'),
(211, 10, 'Area 18'),
(212, 10, 'Area 17'),
(213, 10, 'Area 21'),
(214, 10, 'Area 19'),
(215, 10, 'Area 20'),
(216, 10, 'Area 22'),
(217, 10, 'Area 23'),
(218, 10, 'Area 24'),
(219, 10, 'Area 25'),
(220, 10, 'Area 26'),
(221, 10, 'Area 27'),
(222, 10, 'Area 28'),
(223, 10, 'Area 29'),
(224, 10, 'Area 30'),
(225, 10, 'Area 31'),
(226, 10, 'Area 32'),
(227, 10, 'Area 33'),
(228, 10, 'Area 34'),
(229, 10, 'Area 35'),
(230, 10, 'Area 36'),
(231, 10, 'Area 37'),
(232, 10, 'Area 38'),
(233, 10, 'Area 39'),
(234, 10, 'Area 40'),
(235, 10, 'Area 41'),
(236, 10, 'Area 42'),
(237, 10, 'Area 43'),
(238, 10, 'Area 44'),
(239, 10, 'Area 45'),
(240, 10, 'Area 46'),
(241, 10, 'Area 47'),
(242, 10, 'Area 48'),
(243, 10, 'Area 49'),
(244, 10, 'Area 50'),
(245, 10, 'Area 51'),
(246, 10, 'Area 52'),
(247, 10, 'Area 53'),
(248, 10, 'Area 55'),
(249, 10, 'Area 54'),
(250, 10, 'Area 58'),
(251, 10, 'Area 56'),
(252, 10, 'Area 57'),
(253, 13, 'TA Mponda'),
(254, 13, 'TA Chimwala'),
(255, 13, 'TA Chilipa'),
(256, 13, 'TA Nankumba'),
(257, 13, 'TA Jalasi'),
(258, 13, 'TA Mbwana Nyambi'),
(259, 13, 'TA Chowe'),
(260, 13, 'TA Makanjila'),
(261, 13, 'TA Katuli'),
(262, 13, 'STA Lulanga'),
(263, 13, 'TA Namabvi'),
(264, 13, 'Lake Malawi National Park'),
(265, 13, 'STA Ntonda'),
(266, 13, 'STA Chiunda'),
(267, 13, 'Mangochi Town'),
(268, 13, 'Monkey Bay Urban'),
(269, 12, 'STA Nsanama'),
(270, 13, 'Lake Malombe'),
(271, 12, 'TA Liwonde'),
(272, 12, 'TA Sitola'),
(273, 12, 'TA Kawinga'),
(274, 12, 'TA Nkula'),
(275, 12, 'STA Nchinguza'),
(276, 12, 'TA Nkoola'),
(277, 12, 'TA Chamba'),
(278, 12, 'TA Mposa'),
(279, 12, 'TA Mlomba'),
(280, 12, 'TA Chikweo'),
(281, 12, 'TA Ngokwe'),
(282, 12, 'TA Chiwalo'),
(283, 12, 'STA Chesale'),
(284, 12, 'TA Kapoloma'),
(285, 12, 'TA Nyambi'),
(286, 12, 'Liwonde National Park'),
(287, 12, 'Machinga Boma'),
(288, 25, 'TA Kuntumanji'),
(289, 12, 'Liwonde Town'),
(290, 25, 'TA Mbiza'),
(291, 25, 'TA Mwambo'),
(292, 25, 'TA Mkumbira'),
(293, 25, 'TA Chikowi'),
(294, 25, 'TA Malemia'),
(295, 25, 'STA Nkagula'),
(296, 25, 'TA Mlumbe'),
(297, 25, 'STA Ntholowa'),
(298, 25, 'STA Ngwelero'),
(299, 25, 'STA Nkapita'),
(300, 5, 'TA Mpama'),
(301, 5, 'TA Likoswe'),
(302, 5, 'TA Kadewere'),
(303, 5, 'TA Nkalo'),
(304, 5, 'TA Chitera'),
(305, 5, 'TA Nchema'),
(306, 5, 'STA Mpunga'),
(307, 5, 'STA Sandareki'),
(308, 5, 'STA Onga'),
(309, 5, 'Chiradzulu Boma'),
(310, 2, 'TA Kapeni'),
(311, 2, 'TA Lundu'),
(312, 2, 'TA Chigaru'),
(313, 2, 'TA Kunthembwe'),
(314, 2, 'TA Makata'),
(315, 2, 'TA Kuntaja'),
(316, 2, 'TA Machinjiri'),
(317, 2, 'TA Somba'),
(318, 29, 'TA Kanduku'),
(319, 29, 'TA Nthache'),
(320, 29, 'STA Govati'),
(321, 29, 'Majete Game Reserve - Mwanza'),
(322, 29, 'Mwanza Boma'),
(323, 24, 'TA Nsabwe'),
(324, 24, 'STA Thukuta'),
(325, 24, 'STA Mbawela'),
(326, 24, 'TA Changata'),
(327, 24, 'TA Mphuka'),
(328, 24, 'TA Kwethemule'),
(329, 24, 'TA Kapichi'),
(330, 24, 'TA Nchilamwela'),
(331, 24, 'TA Chimaliro'),
(332, 24, 'TA Bvumbwe'),
(333, 24, 'TA Thomas'),
(334, 24, 'TA Nanseta'),
(335, 24, 'TA Ngolongoliwa'),
(336, 24, 'STA Boyidi'),
(337, 24, 'Thyolo Boma'),
(338, 24, 'Luchenza Town'),
(339, 14, 'TA Mabuka'),
(340, 14, 'TA Laston Njema'),
(341, 14, 'TA Chikumbu'),
(342, 14, 'TA Nthiramanja'),
(343, 14, 'TA Nkanda'),
(344, 14, 'TA Juma'),
(345, 14, 'STA Sunganinzeru'),
(346, 14, 'STA Tombondiya'),
(347, 14, 'Mulanje Mountain Reserve'),
(348, 14, 'Mulanje Boma'),
(349, 4, 'TA Ngabu'),
(350, 4, 'TA Lundu'),
(351, 4, 'TA Chapananga'),
(352, 4, 'TA Maseya'),
(353, 4, 'TA Katunga'),
(354, 4, 'TA Kasisi'),
(355, 4, 'TA Makhwira'),
(356, 4, 'STA Ndakwela'),
(357, 4, 'TA Mlilima'),
(358, 4, 'STA Masache'),
(359, 4, 'TA Ngowe'),
(360, 4, 'Lengwe National Park'),
(361, 4, 'Majete Game Reserve - Chikwawa'),
(362, 4, 'Chikwawa Boma'),
(363, 4, 'Ngabu Urban'),
(364, 16, 'TA Ndamera'),
(365, 16, 'TA Chimombo'),
(366, 16, 'TA Nyachikadza'),
(367, 16, 'TA Mlolo'),
(368, 16, 'TA Tengani'),
(369, 16, 'TA Malemia'),
(370, 16, 'TA Mbenje'),
(371, 16, 'TA Ngabu'),
(372, 16, 'TA Makoko'),
(373, 16, 'Mwabvi Game Reserve'),
(374, 16, 'Nsanje Boma'),
(375, 1, 'TA Msamala'),
(376, 1, 'TA Kalembo'),
(377, 1, 'STA Kachenga'),
(378, 1, 'TA Amidu'),
(379, 1, 'TA Nkaya'),
(380, 1, 'STA Phalula'),
(381, 1, 'TA Chanthunya'),
(382, 1, 'TA Sawali'),
(383, 1, 'STA Matola'),
(384, 1, 'STA Toleza'),
(385, 1, 'Balaka Town'),
(386, 1, 'Liwonde Town'),
(387, 21, 'TA Dambe'),
(388, 21, 'TA Mlauli'),
(389, 21, 'TA Symon Likongwe'),
(390, 21, 'TA Chekucheku'),
(391, 21, 'Neno Boma'),
(392, 25, 'Mpira Ward'),
(393, 25, 'Mbedza Ward'),
(394, 25, 'Chinamwali Ward'),
(395, 25, 'Chirunga Ward'),
(396, 25, 'Mtiya Ward'),
(397, 25, 'Masongola Ward'),
(398, 25, 'Zomba Central Ward'),
(399, 25, 'Likangala Ward'),
(400, 25, 'Chambo Ward'),
(401, 25, 'Sadzi Ward'),
(402, 2, 'Michiru Ward'),
(403, 2, 'South Lunzu Ward'),
(404, 2, 'Mapanga Ward'),
(405, 2, 'Nkolokoti Ward'),
(406, 2, 'Ndirande Matope Ward'),
(407, 2, 'Ndirande Makata Ward'),
(408, 2, 'Ndirande Gamulani Ward'),
(409, 2, 'Nyambadwe Ward'),
(410, 2, 'Mbayani Ward'),
(411, 2, 'Chilomoni Ward'),
(412, 2, 'Blantyre City Centre Ward'),
(413, 2, 'Namalimwe Ward'),
(414, 2, 'Limbe Central Ward'),
(415, 2, 'Mzedi Ward'),
(416, 2, 'Bangwe Ward'),
(417, 2, 'Bangwe Mthandizi Ward'),
(418, 2, 'Soche East Ward'),
(419, 2, 'Blantyre South Ward'),
(420, 2, 'Green Corner Ward'),
(421, 2, 'Soche West Ward'),
(422, 2, 'Namiyango Ward'),
(423, 2, 'Chigumula Ward'),
(424, 2, 'Misesa Ward'),
(425, 15, 'TA Matuwamba'),
(426, 15, 'T/A Kawere'),
(427, 10, 'T/A Mbwatalika'),
(428, 8, 'T/A Nyaza');

-- --------------------------------------------------------

--
-- Table structure for table `tellering`
--

CREATE TABLE `tellering` (
  `id` int(11) NOT NULL,
  `teller` varchar(200) NOT NULL,
  `account` varchar(200) NOT NULL,
  `date_time` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `tellering`
--

INSERT INTO `tellering` (`id`, `teller`, `account`, `date_time`) VALUES
(7, '34', '400004', '2022-11-08 06:14:42'),
(10, '76', '300008', '2023-06-14 03:27:49'),
(11, '65', '300010', '2023-09-01 03:37:21'),
(12, '79', '300012', '2023-09-12 03:37:21'),
(13, '1', '300001', '2025-04-25 13:42:42'),
(14, '71', '300002', '2025-04-25 13:42:52'),
(15, '72', '300004', '2025-05-05 15:38:19');

-- --------------------------------------------------------

--
-- Table structure for table `testimport`
--

CREATE TABLE `testimport` (
  `user_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `mobile_no` varchar(30) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `testimport`
--

INSERT INTO `testimport` (`user_id`, `name`, `mobile_no`) VALUES
(18, 'Mani', '**********'),
(17, 'Aravinth', '**********'),
(19, 'Prakesh', '*********0'),
(20, 'rajesh', '***********');

-- --------------------------------------------------------

--
-- Table structure for table `transaction`
--

CREATE TABLE `transaction` (
  `id` int(11) NOT NULL,
  `account_number` varchar(200) NOT NULL,
  `transaction_id` varchar(200) NOT NULL,
  `credit` decimal(18,2) NOT NULL,
  `debit` decimal(18,2) NOT NULL,
  `balance` decimal(18,2) NOT NULL,
  `system_time` datetime NOT NULL,
  `server_time` datetime NOT NULL DEFAULT current_timestamp(),
  `proof` varchar(250) DEFAULT NULL,
  `coresponding_account` varchar(250) DEFAULT NULL,
  `reason` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `transaction`
--

INSERT INTO `transaction` (`id`, `account_number`, `transaction_id`, `credit`, `debit`, `balance`, `system_time`, `server_time`, `proof`, `coresponding_account`, `reason`) VALUES
(1, '300004', 'TR-S415020250505', '0.00', '5000.00', '-5000.00', '2025-05-05 00:00:00', '2025-05-05 15:39:16', '', NULL, NULL),
(2, 'NBT0001-25', 'TR-S415020250505', '5000.00', '0.00', '5000.00', '2025-05-05 00:00:00', '2025-05-05 15:39:16', '', NULL, NULL),
(3, 'NBT0001-25', 'TR-S415020250505', '0.00', '5000.00', '0.00', '2025-05-05 00:00:00', '2025-05-05 15:39:16', '', NULL, NULL),
(4, '300002', 'TR-S415020250505', '5000.00', '0.00', '5000.00', '2025-05-05 00:00:00', '2025-05-05 15:39:16', '', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `transaction_id` int(11) NOT NULL,
  `transaction_type` int(11) DEFAULT NULL,
  `ref` varchar(200) NOT NULL,
  `loan_id` varchar(200) NOT NULL,
  `amount` varchar(200) NOT NULL,
  `payment_number` varchar(200) NOT NULL,
  `date_stamp` datetime NOT NULL DEFAULT current_timestamp(),
  `description` varchar(250) DEFAULT NULL,
  `payment_proof` varchar(200) DEFAULT NULL,
  `method` int(11) NOT NULL,
  `reference` varchar(200) NOT NULL,
  `added_by` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`transaction_id`, `transaction_type`, `ref`, `loan_id`, `amount`, `payment_number`, `date_stamp`, `description`, `payment_proof`, `method`, `reference`, `added_by`) VALUES
(1, 1, 'CF.20250505.609', '1', '0.00', '0', '2025-05-05 15:19:07', NULL, NULL, 0, 'CF.20250505.874', 72),
(2, 3, 'FI.20250505.300', '1', '5000', '1', '2025-05-05 00:00:00', NULL, 'null', 0, '', 72),
(3, 1, 'CF.20250702.809', '17', '0.00', '0', '2025-07-02 09:29:40', NULL, NULL, 0, 'CF.20250702.543', 138),
(4, 1, 'CF.20250702.169', '16', '0.00', '0', '2025-07-02 09:30:01', NULL, NULL, 0, 'CF.20250702.817', 138),
(5, 1, 'CF.20250702.420', '15', '0.00', '0', '2025-07-02 09:30:13', NULL, NULL, 0, 'CF.20250702.973', 138),
(6, 1, 'CF.20250702.829', '10', '0.00', '0', '2025-07-02 09:30:29', NULL, NULL, 0, 'CF.20250702.483', 138),
(7, 1, 'CF.20250702.111', '9', '0.00', '0', '2025-07-02 09:30:40', NULL, NULL, 0, 'CF.20250702.658', 138),
(8, 1, 'CF.20250702.809', '8', '0.00', '0', '2025-07-02 09:30:51', NULL, NULL, 0, 'CF.20250702.578', 138),
(9, 1, 'CF.20250702.861', '7', '0', '0', '2025-07-02 09:31:01', NULL, NULL, 0, 'CF.20250702.561', 138),
(10, 1, 'CF.20250702.561', '6', '0', '0', '2025-07-02 09:36:08', NULL, NULL, 0, 'CF.20250702.399', 138),
(11, 1, 'CF.20250702.140', '5', '0.00', '0', '2025-07-02 09:36:17', NULL, NULL, 0, 'CF.20250702.472', 138),
(12, 1, 'CF.20250702.382', '4', '0.00', '0', '2025-07-02 09:36:31', NULL, NULL, 0, 'CF.20250702.467', 138),
(13, 1, 'CF.20250702.132', '2', '0.00', '0', '2025-07-02 09:36:43', NULL, NULL, 0, 'CF.20250702.423', 138);

-- --------------------------------------------------------

--
-- Table structure for table `transaction_type`
--

CREATE TABLE `transaction_type` (
  `transaction_type_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `transaction_type`
--

INSERT INTO `transaction_type` (`transaction_type_id`, `name`) VALUES
(1, 'Processing fee'),
(2, 'Penalties'),
(3, 'Loan repayments'),
(4, 'Expenses'),
(5, 'Registration Fee');

-- --------------------------------------------------------

--
-- Table structure for table `users_authentication`
--

CREATE TABLE `users_authentication` (
  `id` int(11) NOT NULL,
  `users_id` varchar(200) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expired_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_access`
--

CREATE TABLE `user_access` (
  `id` int(11) NOT NULL,
  `AccessCode` varchar(20) NOT NULL,
  `Password` varchar(255) NOT NULL,
  `Employee` int(11) NOT NULL,
  `Status` enum('AUTHORIZED','UNAUTHORIZED','SUSPENDED','BLOCKED','DELETED','REJECTED') NOT NULL DEFAULT 'UNAUTHORIZED',
  `reset_code` int(11) DEFAULT NULL,
  `server_date` datetime NOT NULL DEFAULT current_timestamp(),
  `system_date` date DEFAULT NULL,
  `is_logged_in` varchar(200) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_access`
--

INSERT INTO `user_access` (`id`, `AccessCode`, `Password`, `Employee`, `Status`, `reset_code`, `server_date`, `system_date`, `is_logged_in`) VALUES
(1, 'ADMINISTRATOR(SUPER)', '8cb2237d0679ca88db6464eac60da96345513964', 72, 'AUTHORIZED', NULL, '2022-11-24 07:21:06', NULL, '7551'),
(2, 'CHIPASHADAKA708', 'c7aaaaae98a076b10568f80133cd9b7a162132d4', 1, 'AUTHORIZED', 271, '2024-12-25 18:38:08', NULL, '9892'),
(3, 'PHILIPBWEMBYA406', '908f2af9442f391ee1be97a276105c7af68bffc5', 138, 'AUTHORIZED', 686, '2024-12-27 15:10:39', NULL, '8867'),
(4, 'PAULKALUBALE767', 'd6d0035bf37db749604cf5b8ccde4ec70d562a8d', 139, 'AUTHORIZED', 593, '2024-12-27 15:13:30', NULL, '4742'),
(5, 'MUTINTALUNDA359', '68767047de0fc273b70f20bf14144656216bc72d', 140, 'AUTHORIZED', 166, '2024-12-27 15:14:57', NULL, NULL),
(6, 'MISHECKKAMULONI692', 'a90fede275cd703e423d5768670efd8866a45303', 141, 'AUTHORIZED', 978, '2025-04-10 08:35:36', NULL, '4242'),
(7, 'PAULKALUBALE918', 'c13f88feabb0296d72655b3afb4161ad', 143, 'AUTHORIZED', 333482, '2025-04-26 20:03:44', NULL, NULL),
(8, 'PHILIPBWEMBYA384', '907a44ced0b260fa53358ab1783cd6d12ca76b65', 142, 'AUTHORIZED', 145, '2025-04-29 22:28:13', NULL, '4956'),
(9, 'MUTINTALUNDA130', '2512dbd7599b17f120d39d023aef7efc32fee54b', 144, 'AUTHORIZED', 404, '2025-04-29 22:28:52', NULL, 'No'),
(10, 'LUBUMBEMATANI655', 'e2bd738522ccf56a73cacd27918e48b6e53e4c11', 145, 'AUTHORIZED', 705, '2025-04-29 22:29:24', NULL, NULL),
(11, 'CURTISBANDA783', 'eae5fd2e45d3cde5bf4c0f9c3642351901b7d8f9', 146, 'AUTHORIZED', 207352, '2025-04-29 22:30:19', NULL, '7908'),
(12, 'CHILESHEMWAMBA701', '83245129655b742cff1f13c6e6ea799abba8db06', 147, 'AUTHORIZED', 619, '2025-06-17 07:01:04', NULL, '124');

-- --------------------------------------------------------

--
-- Table structure for table `user_group`
--

CREATE TABLE `user_group` (
  `user_group_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `added_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `vault_cashier_pends`
--

CREATE TABLE `vault_cashier_pends` (
  `cvpid` int(11) NOT NULL,
  `vault_account` varchar(200) NOT NULL,
  `teller_account` varchar(200) NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `creator` varchar(200) NOT NULL,
  `teller` varchar(200) DEFAULT NULL,
  `approved_by` varchar(200) DEFAULT NULL,
  `rejected_by` varchar(200) DEFAULT NULL,
  `system_date` datetime NOT NULL DEFAULT current_timestamp(),
  `status` enum('Initiated','Approved','Denied') NOT NULL DEFAULT 'Initiated',
  `stamp` datetime NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `working_days`
--

CREATE TABLE `working_days` (
  `id` int(11) NOT NULL,
  `day` varchar(200) NOT NULL,
  `value` enum('Yes','No') NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

--
-- Dumping data for table `working_days`
--

INSERT INTO `working_days` (`id`, `day`, `value`) VALUES
(1, 'saturday', 'Yes'),
(2, 'sunday ', 'No');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `access`
--
ALTER TABLE `access`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `account`
--
ALTER TABLE `account`
  ADD PRIMARY KEY (`account_id`,`account_number`),
  ADD UNIQUE KEY `account_number` (`account_number`);

--
-- Indexes for table `account_types`
--
ALTER TABLE `account_types`
  ADD PRIMARY KEY (`account_type_id`);

--
-- Indexes for table `activity_logger`
--
ALTER TABLE `activity_logger`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `approval_edits`
--
ALTER TABLE `approval_edits`
  ADD PRIMARY KEY (`approval_edits_id`);

--
-- Indexes for table `authorized_signitories`
--
ALTER TABLE `authorized_signitories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `borrowed`
--
ALTER TABLE `borrowed`
  ADD PRIMARY KEY (`borrowed_id`);

--
-- Indexes for table `borrowed_repayements`
--
ALTER TABLE `borrowed_repayements`
  ADD PRIMARY KEY (`b_id`);

--
-- Indexes for table `branches`
--
ALTER TABLE `branches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `BranchCode` (`BranchCode`);

--
-- Indexes for table `cashier_vault_pends`
--
ALTER TABLE `cashier_vault_pends`
  ADD PRIMARY KEY (`cvpid`);

--
-- Indexes for table `charges`
--
ALTER TABLE `charges`
  ADD PRIMARY KEY (`charge_id`);

--
-- Indexes for table `close_loan`
--
ALTER TABLE `close_loan`
  ADD PRIMARY KEY (`close_loan_id`);

--
-- Indexes for table `corporate_customers`
--
ALTER TABLE `corporate_customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `RegistrationNumber` (`RegistrationNumber`);

--
-- Indexes for table `corporate_shareholders`
--
ALTER TABLE `corporate_shareholders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`currency_id`),
  ADD UNIQUE KEY `currency_code` (`currency_code`);

--
-- Indexes for table `currency`
--
ALTER TABLE `currency`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customer_access`
--
ALTER TABLE `customer_access`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `customer_groups`
--
ALTER TABLE `customer_groups`
  ADD PRIMARY KEY (`customer_group_id`);

--
-- Indexes for table `delete_payments`
--
ALTER TABLE `delete_payments`
  ADD PRIMARY KEY (`delete_loan_id`);

--
-- Indexes for table `districts`
--
ALTER TABLE `districts`
  ADD PRIMARY KEY (`district_id`),
  ADD UNIQUE KEY `district_key` (`district_code`);

--
-- Indexes for table `documents`
--
ALTER TABLE `documents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `edit_loan`
--
ALTER TABLE `edit_loan`
  ADD PRIMARY KEY (`edit_loan_id`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `EmailAddress` (`EmailAddress`),
  ADD UNIQUE KEY `PhoneNumber` (`PhoneNumber`),
  ADD KEY `Role` (`Role`),
  ADD KEY `Branch` (`Branch`);

--
-- Indexes for table `file_folders`
--
ALTER TABLE `file_folders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_parent_folder` (`parent_folder_id`),
  ADD KEY `idx_owner_id` (`owner_id`);

--
-- Indexes for table `file_folder_mapping`
--
ALTER TABLE `file_folder_mapping`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_file_folder` (`file_id`,`folder_id`),
  ADD KEY `idx_file_id` (`file_id`),
  ADD KEY `idx_folder_id` (`folder_id`);

--
-- Indexes for table `file_library`
--
ALTER TABLE `file_library`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_owner` (`owner_type`,`owner_id`),
  ADD KEY `idx_file_category` (`file_category`),
  ADD KEY `idx_date_added` (`date_added`),
  ADD KEY `idx_added_by` (`added_by`);

--
-- Indexes for table `file_shares`
--
ALTER TABLE `file_shares`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_file_share` (`file_id`,`shared_by`,`shared_with`),
  ADD KEY `idx_file_id` (`file_id`),
  ADD KEY `idx_shared_by` (`shared_by`),
  ADD KEY `idx_shared_with` (`shared_with`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- Indexes for table `individual_customers`
--
ALTER TABLE `individual_customers`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `internal_accounts`
--
ALTER TABLE `internal_accounts`
  ADD PRIMARY KEY (`internal_account_id`);

--
-- Indexes for table `loan`
--
ALTER TABLE `loan`
  ADD PRIMARY KEY (`loan_id`);

--
-- Indexes for table `loan_files`
--
ALTER TABLE `loan_files`
  ADD PRIMARY KEY (`file_id`);

--
-- Indexes for table `loan_products`
--
ALTER TABLE `loan_products`
  ADD PRIMARY KEY (`loan_product_id`);

--
-- Indexes for table `menu`
--
ALTER TABLE `menu`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `menuitems`
--
ALTER TABLE `menuitems`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payement_schedules`
--
ALTER TABLE `payement_schedules`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payment_method`
--
ALTER TABLE `payment_method`
  ADD PRIMARY KEY (`payment_method`);

--
-- Indexes for table `proofofidentity`
--
ALTER TABLE `proofofidentity`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `reports`
--
ALTER TABLE `reports`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `shareholders`
--
ALTER TABLE `shareholders`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sms_settings`
--
ALTER TABLE `sms_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `sytem_date`
--
ALTER TABLE `sytem_date`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `ta`
--
ALTER TABLE `ta`
  ADD PRIMARY KEY (`ta_id`);

--
-- Indexes for table `tellering`
--
ALTER TABLE `tellering`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transaction`
--
ALTER TABLE `transaction`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`transaction_id`);

--
-- Indexes for table `transaction_type`
--
ALTER TABLE `transaction_type`
  ADD PRIMARY KEY (`transaction_type_id`);

--
-- Indexes for table `user_access`
--
ALTER TABLE `user_access`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `access`
--
ALTER TABLE `access`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12236;

--
-- AUTO_INCREMENT for table `account`
--
ALTER TABLE `account`
  MODIFY `account_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=133;

--
-- AUTO_INCREMENT for table `activity_logger`
--
ALTER TABLE `activity_logger`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=909;

--
-- AUTO_INCREMENT for table `corporate_customers`
--
ALTER TABLE `corporate_customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=58;

--
-- AUTO_INCREMENT for table `corporate_shareholders`
--
ALTER TABLE `corporate_shareholders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=27;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `currency_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=161;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=148;

--
-- AUTO_INCREMENT for table `file_folders`
--
ALTER TABLE `file_folders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=376;

--
-- AUTO_INCREMENT for table `file_folder_mapping`
--
ALTER TABLE `file_folder_mapping`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=148;

--
-- AUTO_INCREMENT for table `file_library`
--
ALTER TABLE `file_library`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=159;

--
-- AUTO_INCREMENT for table `file_shares`
--
ALTER TABLE `file_shares`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `individual_customers`
--
ALTER TABLE `individual_customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `internal_accounts`
--
ALTER TABLE `internal_accounts`
  MODIFY `internal_account_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `loan`
--
ALTER TABLE `loan`
  MODIFY `loan_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=69;

--
-- AUTO_INCREMENT for table `loan_files`
--
ALTER TABLE `loan_files`
  MODIFY `file_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- AUTO_INCREMENT for table `loan_products`
--
ALTER TABLE `loan_products`
  MODIFY `loan_product_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `menu`
--
ALTER TABLE `menu`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=57;

--
-- AUTO_INCREMENT for table `menuitems`
--
ALTER TABLE `menuitems`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=178;

--
-- AUTO_INCREMENT for table `payement_schedules`
--
ALTER TABLE `payement_schedules`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=69;

--
-- AUTO_INCREMENT for table `payment_method`
--
ALTER TABLE `payment_method`
  MODIFY `payment_method` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `proofofidentity`
--
ALTER TABLE `proofofidentity`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `reports`
--
ALTER TABLE `reports`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `shareholders`
--
ALTER TABLE `shareholders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `sytem_date`
--
ALTER TABLE `sytem_date`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `ta`
--
ALTER TABLE `ta`
  MODIFY `ta_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=429;

--
-- AUTO_INCREMENT for table `tellering`
--
ALTER TABLE `tellering`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `transaction`
--
ALTER TABLE `transaction`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `transaction_type`
--
ALTER TABLE `transaction_type`
  MODIFY `transaction_type_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `user_access`
--
ALTER TABLE `user_access`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `file_folders`
--
ALTER TABLE `file_folders`
  ADD CONSTRAINT `file_folders_ibfk_1` FOREIGN KEY (`parent_folder_id`) REFERENCES `file_folders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `file_folder_mapping`
--
ALTER TABLE `file_folder_mapping`
  ADD CONSTRAINT `file_folder_mapping_ibfk_1` FOREIGN KEY (`file_id`) REFERENCES `file_library` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `file_folder_mapping_ibfk_2` FOREIGN KEY (`folder_id`) REFERENCES `file_folders` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `file_shares`
--
ALTER TABLE `file_shares`
  ADD CONSTRAINT `file_shares_ibfk_1` FOREIGN KEY (`file_id`) REFERENCES `file_library` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
