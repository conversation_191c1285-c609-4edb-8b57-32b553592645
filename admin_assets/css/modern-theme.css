/* ============================================
   FINANCE REALM SYSTEM - MODERN THEME
   ============================================ */

:root {
	--primary: #3f51b5;       /* Modern indigo */
	--primary-light: #5c6bc0; /* Lighter shade for gradients */
	--secondary: #00bcd4;     /* Cyan for accents */
	--success: #4caf50;       /* Refined green */
	--success-light: #66bb6a; /* Lighter green */
	--warning: #ff9800;       /* Clean orange */
	--danger: #f44336;        /* Clear red */
	--danger-light: #ef5350;  /* Lighter red */
	--dark: #2c3e50;          /* Deep slate for text */
	--light: #f5f7fa;         /* Light background */
	--border: #e6e9ed;        /* Subtle borders */
	--text-primary: #4a4a4a;  /* Main text color */
	--text-secondary: #72849a; /* Secondary text color */
	--card-shadow: 0 4px 12px rgba(0,0,0,0.05); /* Subtle shadows */
}

/* ============= GLOBAL STYLES ============= */

body {
	font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	font-size: 14px;
	line-height: 1.6;
	color: var(--text-primary);
	background-color: #f8f9fc;
}

h1, h2, h3, h4, h5, h6 {
	font-weight: 600;
	color: var(--dark);
	line-height: 1.3;
}

a {
	color: var(--primary);
	transition: color 0.2s;
}

a:hover, a:focus {
	color: var(--primary-light);
	text-decoration: none;
}

.text-muted {
	color: var(--text-secondary) !important;
}

.font-weight-semibold {
	font-weight: 600 !important;
}

/* ============= LAYOUT ELEMENTS ============= */

/* Header Styling */
.header {
	background: linear-gradient(135deg, var(--primary), var(--primary-light));
	border: none !important;
	border-radius: 0 !important;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	height: 70px;
}

.header .logo-dark,
.header .logo-white {
	padding: 10px 0;
}

.header .logo img {
	max-height: 45px;
	border-radius: 6px;
	transition: all 0.3s;
}

.header .logo img:hover {
	transform: scale(1.05);
}

.header .style {
	background-color: rgba(255,255,255,0.9);
	border-radius: 30px;
	padding: 8px 20px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	font-weight: 500;
	letter-spacing: 0.5px;
	margin-left: 15px;
}

.header .nav-wrap .nav-left li > a,
.header .nav-wrap .nav-right li > a {
	color: rgba(255,255,255,0.85);
	border-radius: 8px;
	padding: 7px 15px;
	transition: all 0.2s;
}

.header .nav-wrap .nav-left li > a:hover,
.header .nav-wrap .nav-right li > a:hover {
	background-color: rgba(255,255,255,0.15);
	color: #fff;
	transform: translateY(-2px);
}

.header .nav-wrap .nav-left .mobile-toggle > a,
.header .nav-wrap .nav-right .desktop-toggle > a {
	font-size: 20px;
}

/* Side Navigation */
.side-nav {
	background-color: #fff;
	box-shadow: 2px 0 10px rgba(0,0,0,0.05);
	width: 260px;
}

.side-nav-inner {
	border: none !important;
	border-radius: 0 !important;
}

.side-nav-menu {
	padding-top: 10px;
}

.side-nav-menu li a {
	border-radius: 8px;
	margin: 5px 15px;
	padding: 12px 15px;
	transition: all 0.2s;
	color: var(--text-primary);
}

.side-nav-menu li a:hover {
	background-color: var(--light);
	transform: translateX(5px);
	color: var(--primary);
}

.side-nav-menu li a .icon-holder {
	font-size: 18px;
	width: 30px;
	height: 30px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	background-color: rgba(63, 81, 181, 0.1);
	border-radius: 8px;
	margin-right: 12px;
}

.side-nav-menu li.dropdown.open > a {
	background-color: var(--light);
	color: var(--primary);
}

.side-nav-menu > li.dropdown ul.dropdown-menu > li.active {
	background-color: rgba(63, 81, 181, 0.08);
}

.side-nav-menu > li.dropdown ul.dropdown-menu > li.active:after {
	border-color: var(--primary);
}

.side-nav-menu > li.dropdown ul.dropdown-menu > li.active a {
	color: var(--primary);
}

/* Page Container */
.page-container {
	padding-left: 260px;
}

.page-container .main-content {
	padding: calc(70px + 25px) 30px 30px;
	min-height: calc(100vh - 70px);
	background-color: #f8f9fc;
}

.page-container .page-header {
	margin-bottom: 30px;
}

.page-container .page-header .header-title {
	font-size: 22px;
	font-weight: 600;
	color: var(--dark);
}

/* Footer */
.footer {
	background-color: #fff;
	border-top: 1px solid var(--border);
}

.footer .footer-content {
	padding: 15px 0;
	font-size: 13px;
	color: var(--text-secondary);
}

/* ============= COMPONENTS ============= */

/* Dashboard Stats/Cards */
.dashboard-stat {
	border-radius: 12px;
	overflow: hidden;
	transition: all 0.3s;
	box-shadow: var(--card-shadow);
	border: none;
	margin-bottom: 25px;
}

.dashboard-stat:hover {
	transform: translateY(-5px);
	box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.dashboard-stat .details {
	padding: 20px;
}

.dashboard-stat .details .number {
	font-size: 24px;
	font-weight: 600;
	margin-bottom: 5px;
	line-height: 1.2;
	letter-spacing: 0.5px;
}

.dashboard-stat .details .numberr {
	font-size: 20px;
	font-weight: 600;
}

.dashboard-stat .details .desc {
	font-size: 14px;
	opacity: 0.8;
	letter-spacing: 0.5px;
	font-weight: 500;
}

.dashboard-stat .visual i {
	font-size: 60px;
	opacity: 0.2;
}

/* Color variants with gradients */
.dashboard-stat.blue {
	background: linear-gradient(135deg, var(--primary), var(--primary-light));
}

.dashboard-stat.green {
	background: linear-gradient(135deg, var(--success), var(--success-light));
}

.dashboard-stat.red {
	background: linear-gradient(135deg, var(--danger), var(--danger-light));
}

.dashboard-stat.purple {
	background: linear-gradient(135deg, #9c27b0, #ba68c8);
}

.dashboard-stat.orange {
	background: linear-gradient(135deg, #ff9800, #ffb74d);
}

.dashboard-stat.hoki {
	background: linear-gradient(135deg, #67809F, #899eb9);
}

/* Cards */
.card {
	border-radius: 12px;
	border: none;
	box-shadow: var(--card-shadow);
	margin-bottom: 25px;
	transition: all 0.3s;
}

.card:hover {
	box-shadow: 0 8px 24px rgba(0,0,0,0.1);
}

.card-header {
	background-color: transparent;
	border-bottom: 1px solid var(--border);
	padding: 15px 20px;
	display: flex;
	align-items: center;
}

.card-header .card-title {
	font-size: 16px;
	font-weight: 600;
	margin-bottom: 0;
	color: var(--dark);
}

.card-body {
	padding: 20px;
}

.card-footer {
	background-color: transparent;
	border-top: 1px solid var(--border);
	padding: 15px 20px;
}

/* Buttons */
.btn {
	border-radius: 8px;
	padding: 10px 18px;
	font-weight: 500;
	letter-spacing: 0.3px;
	transition: all 0.2s;
	border: none;
	box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

.btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 10px rgba(0,0,0,0.15);
}

.btn-primary {
	background: linear-gradient(135deg, var(--primary), var(--primary-light));
}

.btn-success {
	background: linear-gradient(135deg, var(--success), var(--success-light));
}

.btn-warning {
	background: linear-gradient(135deg, var(--warning), #ffb74d);
}

.btn-danger {
	background: linear-gradient(135deg, var(--danger), var(--danger-light));
}

.btn-rounded {
	border-radius: 50px;
}

.btn-icon {
	width: 40px;
	height: 40px;
	padding: 0;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	border-radius: 8px;
}

/* Tables */
.table thead th {
	background-color: var(--light);
	color: var(--dark);
	font-weight: 600;
	border-top: none;
	letter-spacing: 0.5px;
	text-transform: uppercase;
	font-size: 12px;
	padding: 15px 20px;
}

.table td {
	padding: 15px 20px;
	vertical-align: middle;
	border-bottom: 1px solid var(--border);
	color: var(--text-primary);
}

.table-hover tbody tr:hover {
	background-color: rgba(63, 81, 181, 0.03);
}

.table-striped tbody tr:nth-of-type(odd) {
	background-color: rgba(245, 247, 250, 0.5);
}

.tableCss {
	border-radius: 8px;
	overflow: hidden;
	border: 1px solid var(--border);
}

.tableCss thead {
	background-color: var(--primary);
	color: white;
}

.tableCss td {
	padding: 12px 15px;
	border: 1px solid var(--border);
}

/* Forms */
.form-control {
	border-radius: 8px;
	border: 1px solid var(--border);
	padding: 12px 15px;
	transition: all 0.2s;
	color: var(--text-primary);
}

.form-control:focus {
	border-color: var(--primary);
	box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
}

.input-group-text {
	border-radius: 8px;
	background-color: var(--light);
	border-color: var(--border);
}

.custom-select {
	border-radius: 8px;
	border: 1px solid var(--border);
	padding: 12px 15px;
}

.custom-select:focus {
	border-color: var(--primary);
	box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
}

/* Checkboxes and Radios */
.checkbox input[type=checkbox] + label:before,
.radio input[type=radio] + label:before {
	border-color: var(--border);
}

.checkbox input[type=checkbox]:checked + label:before {
	background: var(--primary);
	border-color: var(--primary);
}

.radio input[type=radio]:checked + label:before {
	border-color: var(--primary);
}

.radio input[type=radio]:checked + label:after {
	background-color: var(--primary);
}

/* Badges */
.badge {
	font-weight: 500;
	padding: 5px 10px;
	border-radius: 6px;
}

.badge-primary {
	background-color: var(--primary);
}

.badge-success {
	background-color: var(--success);
}

.badge-warning {
	background-color: var(--warning);
}

.badge-danger {
	background-color: var(--danger);
}

/* Animations */
@keyframes fadeIn {
	from { opacity: 0; transform: translateY(10px); }
	to { opacity: 1; transform: translateY(0); }
}

.card, .dashboard-stat {
	animation: fadeIn 0.5s ease-out;
}

.side-nav-menu li {
	animation: fadeIn 0.3s ease-out;
	animation-fill-mode: both;
}

.side-nav-menu li:nth-child(1) { animation-delay: 0.1s; }
.side-nav-menu li:nth-child(2) { animation-delay: 0.15s; }
.side-nav-menu li:nth-child(3) { animation-delay: 0.2s; }
.side-nav-menu li:nth-child(4) { animation-delay: 0.25s; }
.side-nav-menu li:nth-child(5) { animation-delay: 0.3s; }
.side-nav-menu li:nth-child(6) { animation-delay: 0.35s; }
.side-nav-menu li:nth-child(7) { animation-delay: 0.4s; }
.side-nav-menu li:nth-child(8) { animation-delay: 0.45s; }
.side-nav-menu li:nth-child(9) { animation-delay: 0.5s; }
.side-nav-menu li:nth-child(10) { animation-delay: 0.55s; }

/* ============= RESPONSIVE ADJUSTMENTS ============= */

@media only screen and (max-width: 992px) {
	.side-nav {
		width: 260px;
		left: -260px;
		box-shadow: none;
		z-index: 1050;
	}

	.is-expand .side-nav {
		left: 0;
		box-shadow: 5px 0 25px rgba(0,0,0,0.1);
	}

	.page-container {
		padding-left: 0;
	}

	.header .nav-wrap .nav-left .mobile-toggle {
		margin-right: 15px;
		display: block;
	}

	.page-container .main-content {
		padding: 85px 15px 15px;
	}
}

@media only screen and (max-width: 767px) {
	.dashboard-stat {
		margin-bottom: 20px;
	}

	.card-body {
		padding: 15px;
	}

	.hidden-mobile {
		display: none !important;
	}

	.table td, .table th {
		padding: 10px;
	}

	.header .style {
		font-size: 12px;
		padding: 5px 10px;
	}

	.btn {
		padding: 8px 15px;
		font-size: 13px;
	}
}

/* Theme-specific overrides */
.is-primary .side-nav .side-nav-inner .side-nav-menu > li.dropdown ul.dropdown-menu > li.active {
	background-color: rgba(63, 81, 181, 0.08);
}

.is-primary .side-nav .side-nav-inner .side-nav-menu > li.dropdown ul.dropdown-menu > li.active:after {
	border-color: var(--primary);
}

.is-primary .header {
	background: linear-gradient(135deg, var(--primary), var(--primary-light));
}

.is-success .header {
	background: linear-gradient(135deg, var(--success), var(--success-light));
}

.is-danger .header {
	background: linear-gradient(135deg, var(--danger), var(--danger-light));
}

/* Shareholder container styles */
.shareholder-container {
	border: 1px solid var(--border);
	border-radius: 12px;
	padding: 20px;
	margin-bottom: 20px;
	background: #f9f9f9;
	box-shadow: 0 2px 8px rgba(0,0,0,0.03);
	transition: all 0.3s;
}

.shareholder-container:hover {
	box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.shareholder-title {
	font-weight: 600;
	margin-bottom: 15px;
	font-size: 16px;
	color: var(--dark);
}

.removeRow {
	background: var(--danger);
	color: #fff;
	border: none;
	padding: 6px 12px;
	cursor: pointer;
	border-radius: 6px;
	transition: all 0.2s;
}

.removeRow:hover {
	background: var(--danger-light);
	transform: translateY(-2px);
}

#addRow {
	background: var(--success);
	color: #fff;
	border: none;
	padding: 8px 15px;
	cursor: pointer;
	border-radius: 6px;
	margin-top: 15px;
	transition: all 0.2s;
}

#addRow:hover {
	background: var(--success-light);
	transform: translateY(-2px);
}
